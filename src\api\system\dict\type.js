import request from '@/utils/request'

// 查询字典类型列表
export function listType (query) {
  return request({
    url: '/system/dict/type/page',
    method: 'get',
    params: query
  })
}

// 查询字典类型详细
export function getType (dictId) {
  return request({
    url: '/system/dict/type/' + dictId,
    method: 'get'
  })
}

// 新增字典类型
export function saveType (data) {
  return request({
    url: '/system/dict/type',
    method: 'post',
    data: data
  })
}

// 删除字典类型
export function delType (dictId) {
  return request({
    url: '/system/dict/type/' + dictId,
    method: 'delete'
  })
}

// 刷新字典缓存
export function refreshCache () {
  return request({
    url: '/system/dict/type/refreshCache',
    method: 'delete'
  })
}

// 导出字典类型
export function exportType (query) {
  return request({
    url: '/system/dict/type/export',
    method: 'get',
    params: query
  })
}

// 获取字典选择框列表
export function optionselect () {
  return request({
    url: '/system/dict/type/optionselect',
    method: 'get'
  })
}

// 查询字典类型列表
export function checkDictTypeUnique (data) {
  return request({
    url: 'system/dict/type/checkDictTypeUnique',
    method: 'get',
    params: data
  })
}
