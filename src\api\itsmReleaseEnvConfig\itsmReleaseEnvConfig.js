import request from '@/utils/request'

// 查询发布-测试环境配置表列表
export function listItsmReleaseEnvConfig (query) {
  return request({
    url: '/itsmReleaseEnvConfig/itsmReleaseEnvConfig/list',
    method: 'get',
    params: query
  })
}

// 查询发布-测试环境配置表详细
export function getItsmReleaseEnvConfig (id) {
  return request({
    url: '/itsmReleaseEnvConfig/itsmReleaseEnvConfig/' + id,
    method: 'get'
  })
}

// 新增发布-测试环境配置表
export function addItsmReleaseEnvConfig (data) {
  return request({
    url: '/itsmReleaseEnvConfig/itsmReleaseEnvConfig',
    method: 'post',
    data: data
  })
}

// 修改发布-测试环境配置表
export function updateItsmReleaseEnvConfig (data) {
  return request({
    url: '/itsmReleaseEnvConfig/itsmReleaseEnvConfig',
    method: 'put',
    data: data
  })
}

// 删除发布-测试环境配置表
export function delItsmReleaseEnvConfig (id) {
  return request({
    url: '/itsmReleaseEnvConfig/itsmReleaseEnvConfig/' + id,
    method: 'delete'
  })
}


// 导出发布-测试环境配置表
export function exportItsmReleaseEnvConfig (query) {
  return request({
    url: '/itsmReleaseEnvConfig/itsmReleaseEnvConfig/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/itsmReleaseEnvConfig/itsmReleaseEnvConfig/getInitData/' + dictTypes,
    method: 'get'
  })
}
