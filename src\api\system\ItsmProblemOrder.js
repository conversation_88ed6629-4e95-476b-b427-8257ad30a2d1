import request from '@/utils/request'

// 查询itsmProblemOrder列表

export function listItsmProblemOrder (query) {
  return request({
    url: '/system/itsmProblem/pendinglist',
    method: 'get',
    params: query
  })
}

export function listItsmProblemOrder2 (query) {
  return request({
    url: '/system/itsmProblem/list',
    method: 'get',
    params: query
  })
}

// 查询itsmProblemOrder详细
export function getItsmProblemOrder (id) {
  return request({
    url: '/system/itsmProblem/' + id,
    method: 'get'
  })
}

// 新增itsmProblemOrder
export function addItsmProblemOrder (data) {
  return request({
    url: '/system/itsmProblem',
    method: 'post',
    data: data
  })
}

// 修改itsmProblemOrder
export function updateItsmProblemOrder (data) {
  return request({
    url: '/system/itsmProblem',
    method: 'put',
    data: data
  })
}

// 删除itsmProblemOrder
export function delItsmProblemOrder (id) {
  return request({
    url: '/system/itsmProblem/' + id,
    method: 'delete'
  })
}


// 导出itsmProblemOrder
export function exportItsmProblemOrder (query) {
  return request({
    url: '/system/itsmProblem/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
// export function getInitData (dictTypes) {
//   return request({
//     url: '/system/itsmProblemOrder/getInitData/' + dictTypes,
//     method: 'get'
//   })
// }

//查询问题分类统计
export function listItsmProblemWtfltj(data) {
  return request({
    url: '/system/itsmProblem/wtfltj',
    method: 'post',
    data: data
  })
}
//查询问题状态统计
export function listItsmProblemWtzttj(data) {
  return request({
    url: '/system/itsmProblem/wtzttj',
    method: 'post',
    data: data
  })
}
// 查询问题关联事件单
export function listItsmProblemWtglgdcx(data) {
  return request({
    url: '/system/itsmProblem/wtglgdcx',
    method: 'post',
    data: data
  })
}
// 查询问题解决进度管控表_开发商
// http://localhost:8183/system/itsmProblem/kfs
export function listItsmProblemKfs(data) {
  return request({
    url: '/system/itsmProblem/kfs',
    method: 'post',
    data: data
  })
}
//查询问题解决进度管控表_自动化科业务人员
// http://localhost:8183/system/itsmProblem/zdhk
export function listItsmProblemZdhk(data) {
  return request({
    url: '/system/itsmProblem/zdhk',
    method: 'post',
    data: data
  })
}