import chooseDW from '@/pages/bpm/bzform/chooseDW'

export function chooseDWList(v, ifReadOnly, fildName) {
  console.log(ifReadOnly)
  if (!ifReadOnly) {
    v.$q.dialog({
      component: chooseDW,
      parent: this,
      lwdwList: fildName
    }).onOk(dwlist => {
      // console.log('OK')
      return dwlist
    }).onCancel(dwlist => {
      // console.log('Cancel')
      return dwlist
    }).onDismiss(dwlist => {
      // console.log('I am triggered on both OK and Cancel')
      return dwlist
    })
  }
}

export default { chooseDWList }
