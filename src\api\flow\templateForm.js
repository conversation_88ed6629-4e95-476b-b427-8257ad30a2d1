import request from '@/utils/request'

export function getTempForms(params) {
  return request({
    url: 'api/templateForm',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/templateForm',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/templateForm/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/templateForm',
    method: 'put',
    data
  })
}

export function getById(id) {
  return request({
    url: 'api/templateForm/queryById?id=' + id,
    method: 'get'
  })
}

export default { add, edit, del }
