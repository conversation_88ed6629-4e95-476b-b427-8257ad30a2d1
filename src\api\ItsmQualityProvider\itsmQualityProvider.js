import request from '@/utils/request'

// 查询服务质量考核/服务商列表
export function listItsmQualityProvider (query) {
  return request({
    url: '/system/itsmqualityprovider/list',
    method: 'get',
    params: query
  })
}

export function listItsmQualityProvider2 (query) {
  return request({
    url: '/system/itsmqualityprovider/list2',
    method: 'get',
    params: query
  })
}

// 查询服务质量考核/服务商详细
export function getItsmQualityProvider (id) {
  return request({
    url: '/system/itsmqualityprovider/' + id,
    method: 'get'
  })
}

// 新增服务质量考核/服务商
export function addItsmQualityProvider (data) {
  return request({
    url: '/system/itsmqualityprovider',
    method: 'post',
    data: data
  })
}

// 修改服务质量考核/服务商
export function updateItsmQualityProvider (data) {
  return request({
    url: '/system/itsmqualityprovider',
    method: 'put',
    data: data
  })
}

// 删除服务质量考核/服务商
export function delItsmQualityProvider (id) {
  return request({
    url: '/system/itsmqualityprovider/' + id,
    method: 'delete'
  })
}


// 导出服务质量考核/服务商
export function exportItsmQualityProvider (query) {
  return request({
    url: '/system/itsmqualityprovider/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmqualityprovider/getInitData/' + dictTypes,
    method: 'get'
  })
}
