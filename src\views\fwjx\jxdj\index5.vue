<template>
  <div>
    
    <a-row class="arowstyle">
         <a-col :span="16"><span style="font-size: 28px;font-weight: bold;">{{ steptemp2 }} </span></a-col>
         <a-col :span="8">
            <a-button style="margin: 20px;float:right;" >关闭</a-button>         
            <a-button type="primary" v-if="currentStep!=20"  style="margin: 20px;float:right;" @click="newSaveForm" >保存</a-button>
            <a-button type="primary" v-if="nextbtn" style="margin: 20px;float:right;" @click="showNext">{{ nextstr }}</a-button>
            <a-button type="primary" v-if="currentStep>1 && currentStep<4 && redoBtn" style="margin: 20px;float:right;" @click="rollBack" >退回</a-button>
         </a-col>
         <a-modal v-model="nextForm5" title="请选择下一步操作的用户" @ok="subimtNext5" >
          <a-row style="width:100%">
              <a-col >请选择下一步操作的用户：</a-col>
            </a-row>
            <a-row style="width:100%">
              <a-col >
                <a-checkbox-group v-model="nextUserId" :options="nextUserArr5">
                  </a-checkbox-group>
              </a-col>
            </a-row>


                      
          </a-modal>
         <a-modal v-model="nextForm" title="请选择下一步操作的用户" @ok="subimtNext" >
          <a-row style="width:100%">
              <a-col >请选择下一步操作的用户：</a-col>
            </a-row>
            <a-row style="width:100%">
              <a-col >
                <a-checkbox-group v-model="nextUserId" :options="nextUserArr">
                  </a-checkbox-group>
              </a-col>
            </a-row>


                      
          </a-modal>
          <a-modal v-model="nextForm2" title="请选择下一步操作的用户" @ok="subimtNext" >
            <a-row style="width:100%">
              <a-col >请选择 业务负责人审核 的用户：</a-col>
            </a-row>
            <a-row style="width:100%">
              <a-col >
                <a-checkbox-group v-model="nextUserId" :options="nextUserArr">
                    
                  </a-checkbox-group>
              </a-col>
            </a-row>
            <a-row style="width:100%;margin-top:50px;">
              <a-col >请选择 自动化科审核 的用户：</a-col>
            </a-row>
            <a-row style="width:100%">
              <a-col >
                <a-checkbox-group v-model="nextUserId2" :options="nextUserArr2">
                    
                  </a-checkbox-group>
              </a-col>
            </a-row>     
          </a-modal>
    </a-row>
    <a-row class="arowstyle">
      <a-col :span="24">
        <a-tabs v-model:activeKey="mykey" style="margin-top:-20px;">
          <a-tab-pane key="1" tab="服务绩效">
            <a-row style="width:100%;" >
              <a-col :span="24">
                <StepsShow ref="StepShow" :stepArr="Steps" :number="current" :stepInfoIds="stepInfoId"  v-if="refreshint0" />
              </a-col>
            </a-row>
        
            <a-row style="width:100%;" >
              <a-col :span="24">
                <a-spin :spinning="refreshint99" tip="加载中。。。" size="large" style="width:100%;">
                <FormShow ref="StepForm" v-if="reopenkkk" :doc2="form2" :doc3="form3" :doc4="form4" :doc5="form5" :doc6="form6" 
                :doc="doc" @readyStep="readyStep"  :showHandleResult="showHandleResult" :currentStep="steptemp2"
                    bussType="sjdj"   @rb2ndBtnCtl="rb2ndBtnCtl" 
                  :disabled="false" />
                </a-spin>
              </a-col>
            </a-row>
            <a-row style="width:100%;" >
              <a-col :span="24">
                <a-collapse style="width:100%;margin:0%;"  expandIconPosition="left" v-model="collapseActiveKey">
                <template slot="expandIcon" slot-scope="props">
                  <div style="display:flex;" >
                    <a-icon v-if="props.isActive" type="up-circle" theme="twoTone" />
                    <a-icon v-if="!props.isActive" type="down-circle" theme="twoTone" />
                  </div>
                </template>
                <a-collapse-panel :key="index + ''"  v-for="(item,index) in Steps" v-if="item.hideFormState!=1"   :header="item.stepName"  :style="collapsestyle"> 
                    <FormShow :key="index" :keyindex="index"  :steps="Steps" :doc4="form4" :doc5="form5" :doc6="form6" 
                     :doc="doc"  v-if="kkk"  :currentStep="item.stepName"   :doc3arr="form3arr" :doc2arr="form2arr"
                      :disabled="true" />
                  </a-collapse-panel>

                </a-collapse>
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane key="2" tab="服务绩效流程">
            <a-row>
              <a-col :span="24">
                流程图
              </a-col>
            </a-row>
            <a-row style="height:333px;background:#fff;">
              <a-col :span="24">
                <!-- <flow-view v-if="flowshow" :flowId="flowId"
          :taskId="taskId"
                   ></flow-view> -->
                   <!-- <service-kpi-flow-chart v-if="flowshow" :currentStep="steptemp2"></service-kpi-flow-chart> -->
                   <div class="flowImg" v-if="flowshow"></div>
                   
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-divider type="vertical" />
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                流转日志
              </a-col>
            </a-row>
            <a-row style="margin-top:50px;">
              <a-col :span="24">
                <a-timeline>
                  <a-timeline-item v-for="item in flowLogs">{{ item.dept }} {{ item.user }} {{ item.opTime }} {{item.stepName}}  {{item.acton}} </a-timeline-item>
                </a-timeline>
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane key="3" tab="关联 工单">
            <a-tabs v-model="commonkey" type="line" @change="mykeychange">
              <a-tab-pane key="1" tab="事件单号 ">
                <a-input placeholder="请输入事件单号" v-model="commonId" style="width: 50%;margin-left: 10px;" />
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="search" type="primary"
                    style="left: 10px;background-color: #1890ff;border-color: #1890ff;">
                    本页查询
                  </a-button>
                </a-config-provider>
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="resetsearch" style="left: 20px;">
                    重置
                  </a-button>
                </a-config-provider>
                <a-table :columns="columns" :data-source="commonrelationList" style="margin-top: 10px;">
                  
                  <template slot="bpm" slot-scope="text,record,index" >
                    <a @click="handleBpm(record)"  >
                      {{text}}
                    </a>
                  </template>
                  <template slot="bpm2" slot-scope="text,record,index" >
                    <a @click="handleBpm2(record)"  >
                      {{text}}
                    </a>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="2" tab="故障单号 ">
                <a-input placeholder="请输入事件单号" v-model="commonId"  style="width: 50%;margin-left: 10px;" />
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="search" type="primary"
                    style="left: 10px;background-color: #1890ff;border-color: #1890ff;">
                    本页查询
                  </a-button>
                </a-config-provider>
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="resetsearch" style="left: 20px;">
                    重置
                  </a-button>
                </a-config-provider>
                <a-table :columns="columns" :data-source="commonrelationList" style="margin-top: 10px;">
                  
                  <template slot="bpm" slot-scope="text,record,index" >
                    <a @click="handleBpm(record)"  >
                      {{text}}
                    </a>
                  </template>
                  <template slot="bpm2" slot-scope="text,record,index" >
                    <a @click="handleBpm2(record)"  >
                      {{text}}
                    </a>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="3" tab="问题单号 ">
                <a-input placeholder="请输入事件单号" v-model="commonId"  style="width: 50%;margin-left: 10px;" />
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="search" type="primary"
                    style="left: 10px;background-color: #1890ff;border-color: #1890ff;">
                    本页查询
                  </a-button>
                </a-config-provider>
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="resetsearch" style="left: 20px;">
                    重置
                  </a-button>
                </a-config-provider>
                <a-table :columns="columns" :data-source="commonrelationList" style="margin-top: 10px;">
                
                  <template slot="bpm" slot-scope="text,record,index" >
                    <a @click="handleBpm(record)"  >
                      {{text}}
                    </a>
                  </template>
                  <template slot="bpm2" slot-scope="text,record,index" >
                    <a @click="handleBpm2(record)"  >
                      {{text}}
                    </a>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="4" tab="需求单号 ">
                <a-input placeholder="请输入事件单号" v-model="commonId"  style="width: 50%;margin-left: 10px;" />
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="search" type="primary"
                    style="left: 10px;background-color: #1890ff;border-color: #1890ff;">
                    本页查询
                  </a-button>
                </a-config-provider>
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="resetsearch" style="left: 20px;">
                    重置
                  </a-button>
                </a-config-provider>
                <a-table :columns="columns" :data-source="commonrelationList" style="margin-top: 10px;">
                  
                  
                  <template slot="bpm" slot-scope="text,record,index" >
                    <a @click="handleBpm(record)"  >
                      {{text}}
                    </a>
                  </template>


                  <template slot="bpm2" slot-scope="text,record,index" >
                    <a @click="handleBpm2(record)"  >
                      {{text}}
                    </a>
                  </template>
                </a-table>
              </a-tab-pane>

            </a-tabs>
          </a-tab-pane>
        </a-tabs>
      </a-col>
    </a-row>
  
  </div>
</template>
<script>
import StepsShow from "@/components/bpm/StepsShow.vue";
import FormShow from "@/components/bpm/Formjxdj2.vue";
import SubmitShow from "@/components/bpm/SubmitShow.vue";
import Options from "@/components/bpm/Options.vue";
 import flowView from '@/components/flowchart/flowView.vue'
import AdvanceTable from '@/components/pt/table/AdvanceTable';
import ServiceKpiFlowChart from '@/components/bpm/flowChart/ServiceKpiFlowChart.vue';  // 服务绩效流程图
import {
  startflowPost,
  saveformdataPost,
  saveExpertVerify,
  getDocInfo,returnTodo,
  queryExpertVerify,getLastFlowInfo,
  getStepChartGet,saveDirectorVerify,queryDirectorVerify,
  targetUser,saveQualityConfirm,queryQualityConfirm,
  operationGetnowscriptpath,saveQualityFinallyConfirm,
  stepInfoGetStepInfoById,queryQualityFinallyConfirm,
  getbpmlog,queryQualityReview,saveQualityReview,
  queryCommonrelationList,saveyys,queryyys,eventNextStep,
  savehandle,queryhandle,savefinish,queryfinish,saveTroubleCheck,queryTroubleCheck,
  saveTroubleHandle,queryTroubleHandle,saveDemandCheck,queryDemandCheck,
  saveTroubleConfirm,queryTroubleConfirm,saveDemandTrack,queryDemandTrack
} from '@/api/system/ywrygl/operation'
import notification from 'ant-design-vue/es/notification'
import { listUesDevice, listUesDeviceSelect, delUesDevice, exportUesDevice, getInitData, importTemplate } from '@/api/system/bpm/uesDevice'
import { listUesWxryqkb,getRoleUser } from '@/api/system/ywrygl/uesWxryqkb'
import { login, loginBySms, getInfo, logout } from '@/api/login'
import moment from 'moment';
const data = []
export default {
  name: 'sjdb',
  components: {
    StepsShow,
    FormShow,
    SubmitShow,
    Options,
    AdvanceTable,
    flowView,
    ServiceKpiFlowChart
  },
  data() {
    return {
      collapseActiveKey:['0','3'],
      collapsestyle: 'background-color: ghostwhite;width:100%;margin:0%;',
      form3arr:[],
      form2arr:[],
      steptemp2:'服务绩效考核登记',
      openedcoll:['2'],
      nextbtn:true,
      redoBtn:false,
      nextUserMap5:new Map(),
      nextUserMap2:new Map(),
      nextUserId2:null,
      nextUserArr5:[],
      nextUserArr2:[],
      nextUserArr3:[],
      nextForm2:false,
      nextUserMap:new Map(),
      nextUserId:null,
      nextUserArr:[],
      nextForm:false,
      nextForm5:false,
      form3:null,
      form2:null,
      form4:null,
      form5:null,
      form6:null,
      form7:null,
      form8:null,
      form9:null,
      form10:null,
      form11:null,
      form12:null,
      form13:null,
      form14:null,
      form15:null,
      form16:null,
      reopenkkk:false,
      refreshint99:false,
      haveSaved:false,
      flowId:"f853265058405285888",
      flowshow:false,
      taskId:'',
      nextstr:'提交',
      userroles:[],
      step2obj:null,
      step3obj:null,
      step4obj:null,
      refId:null,
      currentStep:'1',
      nextPerformUser:[],
      opUserId:null,
      opUserName:null,
      opDeptId:null,
      opDeptName:null,
      yysjlshuser:[],
      sjkshuser:[],
      zdhkshuser:[],
      sjcluser:[],
      sjkUserList:[],
      zdhkUserList:[],
      yysjlUserList:[],
      sjclUserList:[],
      selAcc:true,
      yysshShow:false,
      zdhkshShow:false,
      sjkshShow:false,
      selNextForm:false,
      kkk:true,
      columns: [
        {
          show: true,
          title: '事件前类型',
          dataIndex: 'orderTypePre',
          align: 'center',
          scopedSlots: { customRender: 'bpm2' }
        },
        {
          show: true,
          title: '事件前编号',
          dataIndex: 'orderIdPre',
          align: 'center',
          scopedSlots: { customRender: 'bpm2' }
        },
        {
          show: true,
          title: '事件后类型',
          dataIndex: 'orderTypeSuf',
          align: 'center',
          scopedSlots: { customRender: 'bpm' }
        },
        {
          show: true,
          title: '事件后编号',
          dataIndex: 'orderIdSuf',
          align: 'center',
          scopedSlots: { customRender: 'bpm' }
        },
       
      ],
      showHandleResult:false,
      flowLogs: [],
      mykey: '1',
      dataSources: [],
      refreshint0: false,
      current: 0,
      data,
      form: this.$form.createForm(this),
      commonkey:'1',
      activeKey: ['1'],
      disable: false,
      stepInfoId: '',
      stepId: '',
      // title: '',
      Steps: [],
      todoDtoId: '',
      taskInfo: {},
      doc: {},
      subIndex: 0,
      visible: false,
      refreshint1:false,
      deviceName: '',
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        event_no: undefined,
        add_time: undefined,
        occur_time: undefined,
        application_user: undefined,
        application_unit: undefined,
        application_dept: undefined,
        event_source: undefined,
        application_user_phone: undefined,
        mobile_phone: undefined,
        event_type: undefined,
        event_object: undefined,
        revisit_way: undefined,
        event_effect: undefined,
        event_emergent: undefined,
        event_prior: undefined,
        opinion: undefined,
        register_user: undefined,
        register_unit: undefined,
        register_dept: undefined,

      },
      dataSource: [],
      selectedRowKeys: [],
      selectedRows: [],
      selectedRowKeys2: [],
      selectedRows2: [],
      commonrelationList:[],
      commonType:'event',
      commonId:null,
      fid:null,
    }
  },
  watch: {
    activeKey(key) {
    },
    
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
    rowSelection2() {
      return {
        selectedRowKeys: this.selectedRowKeys2,
        onChange: this.onSelectChange2,
      }
    },
  },
  activated() {
    
    if(sessionStorage.getItem(this.$route.path)==2){
      
      this.refreshint99=false
      this.kkk=false
      this.form={}
      this.doc=null
      this.flowLogs = new Array()
      this.disable = false
      this.refreshint0 = false
      if (null != this.$route.query.xid && "" != this.$route.query.xid
        && null != this.$route.query.xpid && "" != this.$route.query.xpid
        && null != this.$route.query.fid && "" != this.$route.query.fid) {
        this.reopenkkk=false
        this.stepInfoId = this.$route.query.xpid
        this.refId = this.$route.query.xpid
        this.todoDtoId = this.$route.query.xid
        this.fid = this.$route.query.fid
        this.refreshint1=false
        this.readStep()
      } else {
        this.refreshint1=true
        // this.readyStep()
      }
      this.kkk=true
      
      sessionStorage.setItem(this.$route.path,1)
    
    }
  },
  created() {
    this.refreshint99=false
    this.haveSaved=false
    this.doc=null
    this.form={}
    this.kkk=false
    this.flowLogs = new Array()
    this.disable = false
    this.refreshint0 = false
    this.currentStep = '1'
    
   
      getInfo().then((res)=>{
       
       this.opUserId=res.user.id
       this.opUserName=res.user.name
       this.opDeptId=res.user.deptId
       this.opDeptName=res.user.deptName
       this.userroles = res.roles
       if (null != this.$route.query.xid && "" != this.$route.query.xid
        && null != this.$route.query.xpid && "" != this.$route.query.xpid
        && null != this.$route.query.fid && "" != this.$route.query.fid) {
        this.stepInfoId = this.$route.query.xpid
        this.todoDtoId = this.$route.query.xid
        this.fid = this.$route.query.fid
        this.refreshint1=false
        this.readStep()
      } else {
        this.refreshint1=true
   //     this.readyStep()
      }
     })
    //  this.kkk=true
    
    
    sessionStorage.setItem(this.$route.path,1)
    
    
  },
  methods: {
    subimtNext5(){
      this.submitStep45('s855141578572824576','服务绩效考核确认','common')
    },
    rb2ndBtnCtl(e){      
      if(e==1){
        this.redoBtn=false
        this.nextbtn=true
      }else{
        this.redoBtn=true
        this.nextbtn=false
      }
    },
    submitStep45(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      _this.nextPerformUser = []
      nextParam.nextPerformUser =[]
  
      nextParam.nextStepid = sid
      nextParam.nextStepName = name
      nextParam.pathType=path
    
    
 
         
      for(var i=0;i<that.nextUserId.length;i++){
        that.nextUserArr5.forEach(item=>{
         
          if(item.value == that.nextUserId[i]){
            let eu = {
              userId:item.value,
              userName:item.label,
              agentsDeptId:"s855141578572824576",
              agentsDeptName:"服务绩效考核确认",
            }
            that.nextPerformUser.push(eu)
          }
       })
      }
    nextParam.nextPerformUser = _this.nextPerformUser
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      nextParam.stepId=this.doc.STEP_INFO.id
      nextParam.opinion='_this.myoption'
      eventNextStep(nextParam).then(()=>{
        _this.$message.success('提交成功')
        _this.nextForm5 =false
        _this.$multiTab.closeCurrentPage()
        
      })
    },
    submitStep5(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      _this.nextPerformUser = []
      nextParam.nextPerformUser =[]
  
      nextParam.nextStepid = sid
      nextParam.nextStepName = name
      nextParam.pathType=path
    
      for(var i=0;i<that.nextUserId.length;i++){
        that.nextUserArr5.forEach(item=>{
         
          if(item.value == that.nextUserId[i]){
            let eu = {
              userId:item.value,
              userName:item.label,
              agentsDeptId:"s855140985003311104",
              agentsDeptName:"业务负责人审核",
            }
            that.nextPerformUser.push(eu)
          }
       })
      }
        nextParam.nextPerformUser = _this.nextPerformUser
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      nextParam.stepId=this.doc.STEP_INFO.id
      nextParam.opinion='_this.myoption'
      eventNextStep(nextParam).then(()=>{
        _this.$message.success('提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    submitStep4(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      _this.nextPerformUser = []
      
      nextParam.nextPerformUser =[]
      nextParam.nextStepid = sid
      nextParam.nextStepName = name
      nextParam.pathType=path
    
    
        that.nextUserArr2.forEach(item=>{
        
         
            let eu = {
              userId:item.value,
              userName:item.label,
              agentsDeptId:"s855141465133678592",
              agentsDeptName:"自动化科申诉复核",
            }
            that.nextPerformUser.push(eu)
          
        })
      nextParam.nextPerformUser = _this.nextPerformUser
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      nextParam.stepId=this.doc.STEP_INFO.id
      nextParam.opinion='_this.myoption'
      eventNextStep(nextParam).then(()=>{
        _this.$message.success('提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    submitStep2(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      // nextParam.nextPerformUser = _this.nextPerformUser
      
      nextParam.nextPerformUser =[]
        nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType=path
      
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      nextParam.stepId=this.doc.STEP_INFO.id
      nextParam.opinion='_this.myoption'
      eventNextStep(nextParam).then(()=>{
        _this.$message.success('提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    submitStep1(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      // nextParam.nextPerformUser = _this.nextPerformUser
      if(that.currentStep=='1'){
        for(var i=0;i<that.nextUserId.length;i++){
          that.nextUserArr.forEach(item=>{          
              if(item.value == that.nextUserId[i]){
                let eu = {
                  userId:item.value,
                  userName:item.label,
                  agentsDeptId:"s855140985003311104",
                  agentsDeptName:"服务绩效考核审核",
                }
                that.nextPerformUser.push(eu)
              }
          })
        }
        for(var i=0;i<that.nextUserId2.length;i++){
          that.nextUserArr2.forEach(item=>{
          
            if(item.value == that.nextUserId2[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:"s855140985003311105",
                agentsDeptName:"自动化科审核",
              }
              that.nextPerformUser.push(eu)
            }
        })
        }
      }
      nextParam.nextPerformUser = _this.nextPerformUser
        nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType=path
      
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      nextParam.stepId=this.doc.STEP_INFO.id
      nextParam.opinion='_this.myoption'
      eventNextStep(nextParam).then(()=>{
        _this.$message.success('提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    submitStep3(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      // nextParam.nextPerformUser = _this.nextPerformUser
      if(that.currentStep=='3'){
        for(var i=0;i<that.nextUserId.length;i++){
          that.nextUserArr.forEach(item=>{          
              if(item.value == that.nextUserId[i]){
                let eu = {
                  userId:item.value,
                  userName:item.label,
                  agentsDeptId:"s855141266814402560",
                  agentsDeptName:"服务商确认",
                }
                that.nextPerformUser.push(eu)
              }
          })
        }
        
      }
      nextParam.nextPerformUser = _this.nextPerformUser
        nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType=path
      
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      nextParam.stepId=this.doc.STEP_INFO.id
      nextParam.opinion='_this.myoption'
      eventNextStep(nextParam).then(()=>{
        _this.$message.success('提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    submitStep12(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      nextParam.nextPerformUse=[]
      _this.nextPerformUser=[]
      // 
      for(var i=0;i<that.nextUserId.length;i++){
        that.nextUserArr.forEach(item=>{
         
          if(item.value == that.nextUserId[i]){
            let eu = {
              userId:item.value,
              userName:item.label,
              agentsDeptId:"s855140985003311104",
              agentsDeptName:"业务负责人审核",
            }
            that.nextPerformUser.push(eu)
          }
       })
      }
      for(var i=0;i<that.nextUserId2.length;i++){
        that.nextUserArr2.forEach(item=>{
         
          if(item.value == that.nextUserId2[i]){
            let eu = {
              userId:item.value,
              userName:item.label,
              agentsDeptId:"s855140985003311105",
              agentsDeptName:"自动化科审核",
            }
            that.nextPerformUser.push(eu)
          }
       })
      }
      nextParam.nextPerformUser = _this.nextPerformUser
        nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType=path
        nextParam.stepId=this.doc.STEP_INFO.id
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      
      nextParam.opinion='_this.myoption'
      eventNextStep(nextParam).then(()=>{
        _this.$notification.success({
          message: '提交成功',
          description: ``
        })
        _this.nextForm2=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    subimtNext(){
      this.nextForm=false
      if(this.currentStep=='1'){
        this.submitStep12('s855140985003311104','业务负责人审核','common')
        this.nextForm2 = false
      }else if(this.currentStep=='2'){
        
      }else if(this.currentStep=='3'){
        this.submitStep3('s855141266814402560','服务商确认','common')
      }else if(this.currentStep=='4'){
        this.submitStep1('s855141578572824576','服务绩效考核确认','common')
      }
        
      
    },
    async showNext(){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      if(this.currentStep=='1'){
         let resxult = await this.saveStepForm()
         if(resxult){
          this.nextForm2=true
          // this.submitStep1('s853266417233952768','业务科室领导审核','common')
         }
      }else if(this.currentStep=='2'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        
          let resxult2 = await this.saveCheckForm('2',2)
          if(resxult2){
            this.submitStep2('s855141266814402560','服务商确认','common')
            // this.nextForm=true
          }
        
      }else if(this.currentStep=='3'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult2 = await this.saveCheckForm('3',2)  
        if(resxult2){
          //let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
          
         // if(resxult){
            // this.submitStep1('s853268090425704448','自动化科审核','common')
            this.nextForm=true
          //}
        }
      }else if(this.currentStep=='4'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
       
          let resxult2 = await this.saveCheckForm('4',2)
          if(resxult2){
            let vform = this.$refs.StepForm.getFrom4()
            if(vform.isAppeal==1){
              this.submitStep4('s855141465133678592','自动化科申诉复核','common')
            }else{
              this.nextUserId=[]
              this.nextForm5 = true
              
             // this.submitStep45('s855141578572824576','服务绩效考核确认','common')
            }

            // 
           
          }
        
      }else if(this.currentStep=='5'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
     
          let resxult2 = await this.saveCheckForm('5')
          if(resxult2){
            this.nextUserId=[]
              this.nextForm5 = true
           // this.submitStep5('s855141578572824576','服务绩效考核确认','common')
            
          }
        
      }else if(this.currentStep=='6'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        
          let resxult2 = await this.saveCheckForm('6')
          if(resxult2){
            this.submitStep2('s855141626371112960','已关闭','end')
          }
        
      }
      // else if(this.currentStep=='7'){
      //   setTimeout(function(){
      //     that.refreshint99=false
      //   },3000)
      //   let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
      //   if(resxult){
      //     let resxult2 = await this.saveCheckForm('7')
      //     if(resxult2){
      //       this.submitStep1('s855141626371112960','办结','common')
      //     }
      //   }
      // }
      
    },
    newSaveForm(){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      if(this.currentStep=='1'){
        this.saveStepForm()
      }else if(this.currentStep=='2'){       
        this.saveCheckForm('2',1)
      }else if(this.currentStep=='3'){       
        this.saveCheckForm('3',1)
      
      }else if(this.currentStep=='4'){  
        this.saveCheckForm('4',1)
        
      }else if(this.currentStep=='5'){  
        this.saveCheckForm('5',1)
      }else if(this.currentStep=='6'){  
        this.saveCheckForm('6',1)
      }
     

  
      
    },
    
    
    
   async saveCheckForm(type,status){
    let that = this
    let vform = null
    if(type=='2'){
      vform = this.$refs.StepForm.getFrom2()
    }else if(type=='3'){
      vform = this.$refs.StepForm.getFrom3()
    }else if(type=='4'){
      vform = this.$refs.StepForm.getFrom4()
    }else if(type=='5'){
      vform = this.$refs.StepForm.getFrom5()
    }else if(type=='6'){
      vform = this.$refs.StepForm.getFrom6()
    }
    if (undefined != vform && null != vform && "" != vform) {
      vform.instanceId =that.doc.TASK_INFO.id // that.doc.TASK_INFO.id
      vform.assessNo=that.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].id
      vform.taskId = that.doc.STEP_INFO.id
      vform.taskName = that.doc.STEP_INFO.name
      if(type=='3'){
        vform.verifyStatus =status
        vform.ctime = moment().format("YYYY-MM-DD HH:mm:ss")
        return await saveExpertVerify(vform).then(res=>{
          that.$message.success("保存成功")
        // callback(false)
          that.refreshint99=false 
          return true
        })
      }
      if(type=='2'){
        vform.verifyStatus =status
        vform.ctime = moment().format("YYYY-MM-DD HH:mm:ss")
        return await saveDirectorVerify(vform).then(res=>{
          that.$message.success("保存成功")
        // callback(false)
          that.refreshint99=false 
          return true
        })
      }
      if(type=='5'){
        vform.verifyStatus =status
        vform.ctime = moment().format("YYYY-MM-DD HH:mm:ss")
        return await saveQualityReview(vform).then(res=>{
          that.$message.success("保存成功")
        // callback(false)
          that.refreshint99=false 
          return true
        })
      }
      if(type=='6'){
        vform.verifyStatus =status
        vform.ctime = moment().format("YYYY-MM-DD HH:mm:ss")
        return await saveQualityFinallyConfirm(vform).then(res=>{
          that.$message.success("保存成功")
        // callback(false)
          that.refreshint99=false 
          return true
        })
      }
      if(type=='4'){
        vform.verifyStatus =status
        vform.ctime = moment().format("YYYY-MM-DD HH:mm:ss")
        return await saveQualityConfirm(vform).then(res=>{
          that.$message.success("保存成功")
        // callback(false)
          that.refreshint99=false 
          return true
        })
      }
      return true
    }else{
      that.$notification.error({
        message: '保存失败',
        description: ``
      })
      // callback(false)
      that.refreshint99=false 
      return false
    }

   }, 
   async saveFormInSubmit(zdhkshuser,zdhkywryshuser,ksldshuser,scbldshuser,zxldshuser){
    let param = {
      MAIN_TABLE: {

      },
      SUB_TABLE: {
      },
      PENDING_TABLE: {}
    }
    param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)] = this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)]
    param.PENDING_TABLE={
      todoDto:{
        id: this.doc.TASK_INFO.todoDto.id,
        isDelete:-1
      }
    }
    if(null!=zdhkshuser&&""!=zdhkshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].zdhkshuser = zdhkshuser
    }
    if(null!=zdhkshuser&&""!=zdhkywryshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].zdhkywryshuser = zdhkywryshuser
    }
    if(null!=ksldshuser&&""!=ksldshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].ksldshuser = ksldshuser
    }
    if(null!=scbldshuser&&""!=scbldshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].scbldshuser = scbldshuser
    }
    if(null!=zxldshuser&&""!=zxldshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].zxldshuser = zxldshuser
    }
    delete param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].timeout_time
    delete param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].gmt_create

    
    return await saveformdataPost(param).then((res) => {
      this.$notification.success({
        message: '保存成功',
        description: ``
      })
    
      this.refreshint99=false 
      return true
    })
    
   },
   async saveStepForm() {
      if(this.currentStep=='1'){
        let vform = this.$refs.StepForm.getFrom()
        if (undefined != vform && null != vform && "" != vform) {
          let param = {
            MAIN_TABLE: {

            },
            SUB_TABLE: {
            },
            PENDING_TABLE: {}
          }
          let parr = new Array()
          this.makeDocFormDataMainTable(vform)
          param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)] = this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)]
          param.PENDING_TABLE={
            todoDto:{
              id: this.doc.TASK_INFO.todoDto.id,
              isDelete:-1
            }
          }

         return await saveformdataPost(param).then((res) => {
            this.$notification.success({
              message: '保存成功',
              description: ``
            })
          
            this.refreshint99=false 
            return true
          })
          
          
        }else{
          this.$notification.error({
              message: '保存失败',
              description: ``
            })
            // callback(false)
            this.refreshint99=false 
          return false
        }
      }
      
      
      
    },
    async rollBack(){
      let _this = this
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      if(this.currentStep=='2'){
        await this.saveCheckForm('2',2)        
      }else if(this.currentStep=='3'){       
        await this.saveCheckForm('3',2)
      }
      let nextParam = _this.doc.TASK_INFO
      // nextParam.nextPerformUser = _this.nextPerformUser
      
        nextParam.nextStepid = 's855140667926511616'
        nextParam.nextStepName = '服务绩效考核登记'
        nextParam.pathType='back'
      
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      
      nextParam.opinion='_this.myoption'
      returnTodo(nextParam).then(()=>{
        _this.$message.success('提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
   
    handleBpm(record){
      if(record.orderIdSuf!=this.doc.TASK_INFO.id){
        if(record.orderTypeSuf=='problem'){
          this.$router.push({ name: 'Wtdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypeSuf=='trouble'){
          this.$router.push({ name: 'Gzdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypeSuf=='event'){
          this.$router.push({ name: 'Sjdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypeSuf=='demand'){
          this.$router.push({ name: 'Xqdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }
      }
      
    },
    handleBpm2(record){
      if(record.orderIdPre!=this.doc.TASK_INFO.id){
        if(record.orderTypePre=='problem'){
          this.$router.push({ name: 'Wtdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypePre=='trouble'){
          this.$router.push({ name: 'Gzdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypePre=='event'){
          this.$router.push({ name: 'Sjdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypePre=='demand'){
          this.$router.push({ name: 'Xqdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }
      }
    },
    resetsearch(){
      this.commonkey='1'
      this.commonType='event'
      this.commonId=null
      this.commonrelationList=[]
    },
    mykeychange(e){
      if(e==1){
        this.commonType='event'
      }else if(e==2){
        this.commonType='trouble'
      }else if(e==3){
        this.commonType='problem'
      }else if(e==4){
        this.commonType='demand'
      }
      this.search()
    },
    search(){
      this.commonrelationList=[]
      let queryP={
        orderTypePre:this.commonType,
        orderIdPre:this.commonId,
        orderTypeSuf:'event',
        orderIdSuf:this.doc.TASK_INFO.id
      }
      queryCommonrelationList(queryP).then((res2)=>{
        this.commonrelationList=res2.rows
        
      })
      let queryP2={
        orderTypePre:'event',
        orderIdPre:this.doc.TASK_INFO.id,
        orderTypeSuf:this.commonType,
        orderIdSuf:this.commonId
      }
      commonrelationList(queryP2).then((res2)=>{
        res2.rows.forEach(item=>{
          this.commonrelationList.push(item)
        })
        
      })
    },
    setInsertOrder() {
      this.$refs.SubmitShow.setInsertOrder()
    },
    setExmaine() {
      this.$refs.SubmitShow.setExmaine()
    },
    setdisposal() {
      this.$refs.SubmitShow.setdisposal()
      
    },
    getBpmLogFun() {
      getbpmlog(this.taskInfo.todoDto.parentDocId).then((res) => {
        this.flowLogs = []
        res.data.forEach(element => {
          this.flowLogs.push(element)
        });        
      })
    },
    delSel() {
      var arr = new Array()

      for (var j = 0; j < this.dataSource.length; j++) {
        if (this.selectedRowKeys2.indexOf(j) == -1) {
          arr.push(this.dataSource[j])
        }
      }
      this.dataSource = arr
    },
    handleInputChange(row) {
      // 当下拉框的值改变时，这个方法会被调用
      // row 参数是当前行的数据
      // 在这里，您可以根据需要更新其他属性或发送请求到后端
    },
    handleDelete(record) {

      if (record) {
        const index = this.dataSource.findIndex((item) => item.id == record.id)
        if (index !== -1) {
          this.dataSource.splice(index, 1)
        } else {
          console.error('未找到要删除的数据项')
        }
      } else {
        console.error('尝试删除的行数据未定义')
      }
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
      console.log('selectedRowKeys changed: ', selectedRowKeys)
    },
    onSelectChange2(selectedRowKeys) {
      this.selectedRowKeys2 = selectedRowKeys
      console.log('selectedRowKeys changed: ', selectedRowKeys)
    },


    showModal() {
      this.visible = true
      this.listUesWxryqkbFun()
    },
    searchPerson() {
      this.listUesWxryqkbFun()
    },
    listUesWxryqkbFun() {
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        fdxm: this.name,
      }
      listUesWxryqkb(params).then((response) => {
        this.dataSources = response.data.list
        this.pageNum = response.data.pages
        this.pageSize = response.data.pageSize
        this.total = response.data.total
      })
    },
    hideModal() {
      this.dataSource = this.selectedRowKeys.map((index) => this.dataSources[index])
      this.visible = false
    },

    
    makeDocFormDataMainTable(vform){
      this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh=vform.fd_bh
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bt=vform.fd_bt
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh=vform.fd_bh
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].gmt_create=null
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].assess_time=vform.assess_time
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].provider_id=vform.provider_id
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].provider_name=vform.provider_name
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].contract_id=vform.contract_id
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].contract_name=vform.contract_name
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].director_id=vform.director_id
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].director_name=vform.director_name
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].score_id=vform.score_id
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].score_name=vform.score_name
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].assess_type=vform.assess_type
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].assess_object=vform.assess_object
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].assess_content=vform.assess_content
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].target1_id=vform.target1_id
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].target1_name=vform.target1_name
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].target2_id=vform.target2_id
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].target2_name=vform.target2_name
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].clause_id=vform.clause_id
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].clause_name=vform.clause_name
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].assess_value=vform.assess_value
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].status=vform.status
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].remark=vform.remark
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].attachment=vform.attachment
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register=vform.register
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].apply_time=vform.apply_time
        
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].FID
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_isdel
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].gmt_create
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_archive_datetime
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].update_by
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].creation_time
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].add_time
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].remind_time
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].timeout_time
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].main_id
        
        let xd = new Date()
        // this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_archive_datetime = moment().format('YYYY-MM-DD')add_time
    },
    showerro(){
      this.$message.error('保存错误')
    },
    getStep5RoleUsers(){
      getRoleUser('servicequality-confirmer').then(res=>{
        this.nextUserMap5=new Map()
        this.nextUserArr5=[]
        this.nextUserId=null
       
      res[Object.keys(res)].forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        if(!this.nextUserMap5.has(item.id)){
           this.nextUserArr5.push(x)
          this.nextUserMap5.set(item.id,'0')
        }
        
      })
      



    })
    },
    getStep3RoleUsers(){
      getRoleUser('servicequality-provider-confirmer').then(res=>{
        this.nextUserMap=new Map()
        this.nextUserArr=[]
        this.nextUserId=null
       
      res[Object.keys(res)].forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        if(!this.nextUserMap.has(item.id)){
           this.nextUserArr.push(x)
          this.nextUserMap.set(item.id,'0')
        }
        
      })
      



    })
    },

    getStep1RoleUsers(){
      getRoleUser('three,five,nine,eleven,eight').then(res=>{
        this.nextUserMap=new Map()
        this.nextUserArr=[]
        this.nextUserId=null
        this.nextUserMap2=new Map()
        this.nextUserArr2=[]
        this.nextUserArr3=[]
        this.nextUserId2=null
      res.three.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        if(!this.nextUserMap.has(item.id)){
           this.nextUserArr.push(x)
          this.nextUserMap.set(item.id,'0')
        }
        
      })
      res.five.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        if(!this.nextUserMap.has(item.id)){
           this.nextUserArr.push(x)
          this.nextUserMap.set(item.id,'0')
        }
      })
      res.nine.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        if(!this.nextUserMap.has(item.id)){
           this.nextUserArr.push(x)
          this.nextUserMap.set(item.id,'0')
        }
      })
      res.eleven.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        if(!this.nextUserMap.has(item.id)){
           this.nextUserArr.push(x)
          this.nextUserMap.set(item.id,'0')
        }
      })


      res.eight.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        if(!this.nextUserMap2.has(item.id)){
           this.nextUserArr2.push(x)
          this.nextUserMap2.set(item.id,'0')
        }
      })
    })
    },
    
    handleDel(key) {
      let arr = new Array()
      for (var i = 0; i < this.dataSource.length; i++) {
        if (this.dataSource[i].xid != key) {
          arr.push(this.dataSource[i])
        }
      }
      this.dataSource = arr
    },
    async readStep() {
      this.kkk=false
      this.reopenkkk=false
      // this.stepInfoId=this.$route.params.xpid
      // this.todoDtoId = this.$route.params.xid
      let odata = {
        title:"根据TASK_INFO.id、TASK_INFO.todoDto.id获取表单权限",
        id: this.fid,
        parentDocId: this.stepInfoId,
        isOpened: "1"
      }
     
      await this.getDocStep(odata)
      
       getStepChartGet({
          parentDocId: this.stepInfoId,
          maintable:'itsm_quality_assess_datalink'
        }).then(async (response) => {
          this.Steps = response
          let xi = -1
          let xii = 0
          for (var i = 0; i < this.Steps.length; i++) {
            if (this.Steps[i].state >= 2) {
              xi = i
              xii ++
            }
          }
          this.current = xii
          this.currentStep=String(this.current+1)
    
          if(xii>=this.Steps.length){
            this.current=xii+1
          }
          if (xi > 0) {
            this.disable = true;
          }
      
          // if(this.currentStep==6){
          //   this.currentStep="7"
          //   this.current=6
          // }
          if(this.refreshint1==false){
            // this.$refs.StepForm.setExamShow(true)
          }
          this.steptemp2 = this.doc.STEP_INFO.stepName 
          if(this.doc.STEP_INFO.stepName=='业务负责人审核'){
            this.currentStep="2"
            this.current=1
          }else  if(this.doc.STEP_INFO.stepName=='自动化科审核'){
            this.currentStep="3"
            this.current=2
          }else  if(this.doc.STEP_INFO.stepName=='服务商确认'){
            this.currentStep="4"
                this.current=3
          }else  if(this.doc.STEP_INFO.stepName=='自动化科申诉复核'){
            this.currentStep="5"
            this.current=4
          
          }else  if(this.doc.STEP_INFO.stepName=='服务绩效考核确认'){
            this.currentStep="6"
            this.current=5
          
          }else{
            this.currentStep="1"
            this.current=0
          }
    
          if(this.currentStep==1){
            this.getStep1RoleUsers()
          }else  if(this.currentStep==3){
            this.getStep3RoleUsers()
          }else if(this.currentStep==4||this.currentStep==5){
            this.getStep5RoleUsers()
          }

          if(this.currentStep=='6'){
            this.nextstr="办结"
          }
          this.refreshint1 = true
          let yys = {
            mainId:this.doc.TASK_INFO.id
          }
          this.refreshint0 = true

          let yys22 = {
            instanceId:this.doc.TASK_INFO.id,
            verifyStatus:2
          }
          await queryDirectorVerify(yys22).then(res=>{
            this.form2arr = res.rows              
          })

          let yys32 = {
              instanceId:this.doc.TASK_INFO.id,
              verifyStatus:2
            }

            await queryExpertVerify(yys32).then(res=>{
              this.form3arr = res.rows
            })
          
          
          if(this.currentStep=='1'){
           
            this.flowshow = true       
            this.reopenkkk = true 
            this.kkk=true
          }else{
            if(this.currentStep=='2'){
              
            }else if(this.currentStep=='3'){
              
            }else if(this.currentStep=='4'){
              let param2 ={
                stepId:'s855140985003311105',
                refId: this.doc.TASK_INFO.id
              }
              getLastFlowInfo(param2).then(res=>{
                let x = { label: res.data.sendUserCn, value: res.data.sendUser ,userId:res.data.sendUser,userName:res.data.sendUserCn}
                this.nextUserArr2=[]
                this.nextUserArr2.push(x)
                        
              })
              let param3 ={
                stepId:'s855141266814402560',
                refId: this.doc.TASK_INFO.id
              }
              getLastFlowInfo(param3).then(res=>{
                let x = { label: res.data.sendUserCn, value: res.data.sendUser ,userId:res.data.sendUser,userName:res.data.sendUserCn}
                this.nextUserArr3=[]
                this.nextUserArr3.push(x)
                        
              })
            }else if(this.currentStep=='5'){
              let param2 ={
                stepId:'s855141266814402560',
                refId: this.doc.TASK_INFO.id
              }
              getLastFlowInfo(param2).then(res=>{
                let x = { label: res.data.sendUserCn, value: res.data.sendUser ,userId:res.data.sendUser,userName:res.data.sendUserCn}
                this.nextUserArr3=[]
                this.nextUserArr3.push(x)
                        
              })
            }
            let yys = {
              instanceId:this.doc.TASK_INFO.id,
              verifyStatus:1
            }

            await queryExpertVerify(yys).then(res=>{
              this.form3 = res.rows[0]
              if(this.currentStep=='3'
             
              ){
                this.flowshow = true       
                this.reopenkkk = true 
                this.kkk=true
              }      
              
            })

            let yys2 = {
              instanceId:this.doc.TASK_INFO.id,
              verifyStatus:1
            }
            await queryDirectorVerify(yys2).then(res=>{
              this.form2 = res.rows[0]
                   
              if(this.currentStep=='2'
             
              ){
                this.flowshow = true       
                this.reopenkkk = true 
                this.kkk=true
              }      
              
            })
            

            let dds = {
               instanceId:this.doc.TASK_INFO.id
            }
            await queryQualityConfirm(dds).then(res2=>{
              let arr2 = res2.rows
              if(null!=arr2&&arr2.length>0){
                arr2.forEach(item=>{
                  this.form4 = item
                })
              }
              if(this.currentStep=='4'
              ){
                this.flowshow = true       
                this.reopenkkk = true 
                this.kkk=true
              }     
            })

           
            await queryQualityReview(dds).then(res4=>{
             
              this.form5 = res4.rows[0]
              
              if(this.currentStep=='5'
              ){
                this.flowshow = true       
                this.reopenkkk = true 
                this.kkk=true
              }     
            })

            await queryQualityFinallyConfirm(dds).then(res4=>{
             
             let arr4 = res4.rows
             if(null!=arr4&&arr4.length>0){
               arr4.forEach(item=>{
                 this.form6 = item
               })
             }
             if(this.currentStep=='6'
             ){
               this.flowshow = true       
               this.reopenkkk = true 
               this.kkk=true
             }  
           })
           
          }


          
          
          this.getBpmLogFun()
          
        })
      
    },
    async getDocStep(odata){
      await getDocInfo(odata).then((res) => {
        this.taskInfo = res.TASK_INFO
        this.stepId = res.TASK_INFO.stepId
        this.doc = null;
        // this.doc = res
        console.log(res);
        this.taskId=res.TASK_INFO.id
        let Formm = res.FORM_DATA.MAIN_TABLE[Object.keys(res.FORM_DATA.MAIN_TABLE)]
       
        res.FORM_DATA.MAIN_TABLE[Object.keys(res.FORM_DATA.MAIN_TABLE)]=Formm
      

      
      
       // delete this.doc.FORM_DATA.MAIN_TABLE.itsm_event_order.fd_complete_duration
         
        this.stepInfoId = res.TASK_INFO.id
        // this.$refs.StepForm.setForm(this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)])
        // if(this.refreshint1==true){
        //   this.$refs.StepForm.getCurrUserInfoFun()
        // }
        this.doc = res

        this.$multiTab.rename(this.$route.path,'绩效服务-'+this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh)

      })
    },

    readyStep() {
      new Promise((resolve,reject)=>{
        startflowPost({
          "fileName":"服务绩效考核",
          "id":"t855140419653074944",
          "fileFormId":"855389348562993152",
          "flowId":"f855140529560616960",
          "haveNumber":"0",
          "fileCat": "服务绩效考核",
          "fileCatId": "c180013"
        }).then( async (res) => {

          this.stepInfoId = res.TASK_INFO.id
          this.todoDtoId = res.TASK_INFO.todoDto.id

           await this.readStep()
           resolve()
        })
      }).then(rees=>{
        this.saveStepForm()
      })
    
      
    }

  },
}
</script>
<style lang="less" scoped="scoped">
//::v-deep .ant-steps-item-wait .ant-steps-item-icon{
//  background: #1677ff;
// .ant-steps-icon{
//   color: #fff;
// }
//}
::v-deep .anticon-loading {
  color: #30a000;
  font-size: 32px;
}

::v-deep .ant-steps-item-custom .ant-steps-item-tail::after {
  background-color: #50c51c;
}

.ant-advanced-search-form {
  // margin-top: 20px;
  padding: 24px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.ant-coll-all {
  margin: 0% 4%;
  // padding: 0 4%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.ant-collapse {
  // margin-left: 10px;
  margin: 0% 4%;
  // padding: 1% 1%;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  // border-radius: 6px;
  // margin-left: 10px;
}

.ant-collapse-header {
  background-color: #fafafa;
  margin: 0% 0%;
  height: 50px;
  border-radius: 6px;
}

.sqlc-font {
  font-size: 14px;
  display: flex;
  align-items: center;
}

.ant-coll-font {
  display: flex;
  height: 50px;
  padding: 2%;
  font-size: 15px;
  align-items: center;
}

.ant-collapse-foot {
  padding: 0 4%;
  background: #fff;
  border: 0px solid #d9d9d9;
}

.table-page-search-wrapper {
  // background: #fff;
  // border: 1px solid #d9d9d9;
  margin-top: 20px;
  padding: 3%;
}

.ant-advanced-search-form .ant-form-item {
  display: flex;
}

.ant-advanced-search-form .ant-form-item-control-wrapper {
  flex: 1;
}

#components-form-demo-advanced-search .ant-form {
  max-width: none;
}

#components-form-demo-advanced-search .search-result-list {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 80px;
}

.arowstyle{
  margin-top: 5px;
  margin-left: 10px;
  margin-bottom: 5px;
  margin-right:10px;
  width: 100%;
}
// 折叠面板图标
.ant-collapse-arrow {
  .anticon-down-circle,.anticon-up-circle {
    font-size:14px;
  }
}
.flowImg {
    background: url('~@/assets/images/flow/servicekpiFlow.png') no-repeat;
    background-size: contain;
    width: auto;
    height: 285px;
    margin:24px;
}
</style>
