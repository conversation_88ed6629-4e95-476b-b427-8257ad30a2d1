import request from '@/utils/request'

// 查询短信-发送表列表
export function listSmsSend (query) {
  return request({
    url: '/system/smssend/list',
    method: 'get',
    params: query
  })
}

// 查询短信-发送表详细
export function getSmsSend (id) {
  return request({
    url: '/system/smssend/' + id,
    method: 'get'
  })
}

// 新增短信-发送表
export function addSmsSend (data) {
  return request({
    url: '/system/smssend',
    method: 'post',
    data: data
  })
}

// 修改短信-发送表
export function updateSmsSend (data) {
  return request({
    url: '/system/smssend',
    method: 'put',
    data: data
  })
}

// 删除短信-发送表
export function delSmsSend (id) {
  return request({
    url: '/system/smssend/' + id,
    method: 'delete'
  })
}


// 导出短信-发送表
export function exportSmsSend (query) {
  return request({
    url: '/system/smssend/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/smssend/getInitData/' + dictTypes,
    method: 'get'
  })
}
