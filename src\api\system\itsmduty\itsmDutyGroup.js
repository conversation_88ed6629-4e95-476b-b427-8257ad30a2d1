import request from '@/utils/request'

// 查询值班管理-班组管理列表
export function listItsmDutyGroup (query) {
  return request({
    url: '/system/itsmDutyGroup/list',
    method: 'get',
    params: query
  })
}

// 查询值班管理-班组管理详细
export function getItsmDutyGroup (id) {
  return request({
    url: '/system/itsmDutyGroup/' + id,
    method: 'get'
  })
}

// 新增值班管理-班组管理
export function addItsmDutyGroup (data) {
  return request({
    url: '/system/itsmDutyGroup',
    method: 'post',
    data: data
  })
}

// 修改值班管理-班组管理
export function updateItsmDutyGroup (data) {
  return request({
    url: '/system/itsmDutyGroup',
    method: 'put',
    data: data
  })
}

// 删除值班管理-班组管理
export function delItsmDutyGroup (id) {
  return request({
    url: '/system/itsmDutyGroup/' + id,
    method: 'delete'
  })
}


// 导出值班管理-班组管理
export function exportItsmDutyGroup (query) {
  return request({
    url: '/system/itsmDutyGroup/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyGroup/getInitData/' + dictTypes,
    method: 'get'
  })
}
