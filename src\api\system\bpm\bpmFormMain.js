import request from '@/utils/request'

// 查询主表单表列表
export function listBpmFormMain (query) {
  return request({
    url: '/system/bpmFormMain/list',
    method: 'get',
    params: query
  })
}

// 查询主表单表详细
export function getBpmFormMain (id) {
  return request({
    url: '/system/bpmFormMain/' + id,
    method: 'get'
  })
}

// 新增主表单表
export function addBpmFormMain (data) {
  return request({
    url: '/system/bpmFormMain',
    method: 'post',
    data: data
  })
}

// 修改主表单表
export function updateBpmFormMain (data) {
  return request({
    url: '/system/bpmFormMain',
    method: 'put',
    data: data
  })
}

// 删除主表单表
export function delBpmFormMain (id) {
  return request({
    url: '/system/bpmFormMain/' + id,
    method: 'delete'
  })
}

// 导出主表单表
export function exportBpmFormMain (query) {
  return request({
    url: '/system/bpmFormMain/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmFormMain/getInitData/' + dictTypes,
    method: 'get'
  })
}
