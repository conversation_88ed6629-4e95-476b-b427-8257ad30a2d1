import request from '@/utils/request'

// 查询设备退出机房申请单列表
export function listUeSbtcjfsqd (query) {
  return request({
    url: '/system/ueSbtcjfsqd/list',
    method: 'get',
    params: query
  })
}

export function listUeSbtcjfsqdPending (query) {
  return request({
    url: '/system/ueSbtcjfsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function listUeSbtcjfsqdPending2 (query) {
  return request({
    url: '/system/ueSbtcjfsqd/pendinglist2',
    method: 'get',
    params: query
  })
}


// 查询设备退出机房申请单详细
export function getUeSbtcjfsqd (id) {
  return request({
    url: '/system/ueSbtcjfsqd/' + id,
    method: 'get'
  })
}

// 新增设备退出机房申请单
export function addUeSbtcjfsqd (data) {
  return request({
    url: '/system/ueSbtcjfsqd',
    method: 'post',
    data: data
  })
}

// 修改设备退出机房申请单
export function updateUeSbtcjfsqd (data) {
  return request({
    url: '/system/ueSbtcjfsqd',
    method: 'put',
    data: data
  })
}

// 删除设备退出机房申请单
export function delUeSbtcjfsqd (id) {
  return request({
    url: '/system/ueSbtcjfsqd/' + id,
    method: 'delete'
  })
}

// 导出设备退出机房申请单
export function exportUeSbtcjfsqd (query) {
  return request({
    url: '/system/ueSbtcjfsqd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/ueSbtcjfsqd/getInitData/' + dictTypes,
    method: 'get'
  })
}
