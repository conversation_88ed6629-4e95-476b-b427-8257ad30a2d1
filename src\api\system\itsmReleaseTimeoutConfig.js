import request from '@/utils/request'

// 查询发布-超时规则配置表列表
export function listItsmReleaseTimeoutConfig (query) {
  return request({
    url: '/system/itsmreleasetimeoutconfig/list',
    method: 'get',
    params: query
  })
}

// 查询发布-超时规则配置表详细
export function getItsmReleaseTimeoutConfig (id) {
  return request({
    url: '/system/itsmreleasetimeoutconfig/' + id,
    method: 'get'
  })
}

// 新增发布-超时规则配置表
export function addItsmReleaseTimeoutConfig (data) {
  return request({
    url: '/system/itsmreleasetimeoutconfig',
    method: 'post',
    data: data
  })
}

// 修改发布-超时规则配置表
export function updateItsmReleaseTimeoutConfig (data) {
  return request({
    url: '/system/itsmreleasetimeoutconfig',
    method: 'put',
    data: data
  })
}

// 删除发布-超时规则配置表
export function delItsmReleaseTimeoutConfig (id) {
  return request({
    url: '/system/itsmreleasetimeoutconfig/' + id,
    method: 'delete'
  })
}


// 导出发布-超时规则配置表
export function exportItsmReleaseTimeoutConfig (query) {
  return request({
    url: '/system/itsmReleaseTimeoutConfig/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmreleasetimeoutconfig/getInitData/' + dictTypes,
    method: 'get'
  })
}
