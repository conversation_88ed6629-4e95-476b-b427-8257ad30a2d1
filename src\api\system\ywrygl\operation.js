import request from '@/utils/request'
import axios from 'axios'

//表单加载前
export function startflowPost(data) {
  return request({
    url: '/api/bpm/runtime/startflow/starttask',
    method: 'post',
    data: data,
  })
}
//	附件列表
export function getDocInfo(data) {
  return request({
    url: '/api/bpm/runtime/startflow/openpending',
    method: 'put',
    data: data,
  })
}
//  上传附件
export function uploadPost(data) {
  return request({
    url: '/attach/upload',
    method: 'post',
    data: data,
  })
}
//  删除附件
export function attachDelete(data) {
  return request({
    url: '/api/attach',
    method: 'delete',
    data: data,
  })
}

//  待办处理人点击提交，送下一步
export function operationGotoNextStep(data) {
  return request({
    url: '/api/bpm/runtime/operation/gotoNextStep',
    method: 'put',
    data: data,
  })
}
//  流程图 
export function getStepChartGet(query) {
  
  return request({
    url: '/api/flowInfo/getStepChart',
    method: 'get',
    params: query,
  })
}

export function getStepChartGetV1(query) {
 
  return request({
    url: '/api/flowInfo/getStepChartV1',
    method: 'get',
    params: query,
  })
}

export function openflowdoc(data) {
  return request({
    url: '/api/bpm/runtime/startflow/openflowdoc',
    method: 'put',
    data: data,
  })
}
//	暂存文档
export function saveformdataPost(data) {
  return request({
    url: '/api/bpm/runtime/startflow/save/formdata',
    method: 'post',
    data: data,
  })
}

export function targetUser(data) {
  return request({
    url: '/api/bpm/runtime/operation/path/target/user',
    method: 'put',
    data: data,
  })
}

// 选择路径
export function operationGetnowscriptpath(data) {
  return request({
    url: '/api/bpm/runtime/operation/getnowscriptpath/' + data.stepId + '/undefined',
    method: 'get',
  })
}

//	选择环节
export function stepInfoGetStepInfoById(data) {
  return request({
    url: '/api/stepInfo/getStepInfoById/' + data.stepId,
    method: 'get',
  })
}
//   本人待办列表 
export function pendingGet(data) {
  return request({
    url: '/api/pending?page=0&size=100&sort=receiveDateTime%2Cdesc&curDoing=' + data.userid + '&status=0&isDelete=0 ',
    method: 'get',
  })
}

async function requestFile (url) {
  const res = await axios.get(url, {
    headers: {
      // Authorization: getToken() // token
    },
    responseType: 'blob'
  })
  return res.data
}

function blobToDataURL (blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.addEventListener('load', () => {
      resolve(reader.result) // reader.result即为包含文件内容的字符串
    })
    reader.readAsDataURL(blob)
  })
}

export function delAttchment(ids) {
  return request({
    url: 'api/attach',
    method: 'delete',
    data: ids
  })
}


export function downloadAttchment(fileid, filename) {
  // 鉴权下载文件
  
  const url = process.env.VUE_APP_BASE_API +'/api/attach/download' + '/' + fileid
  // window.open(fileDownloadApi + '/' + fileid, '_blank')
  requestFile(url).then(blob => {
    blobToDataURL(blob).then(dataURL => {
      const a = document.createElement('a')
      a.download = filename
      a.href = dataURL
      // a.target = '_blank'
      // 修复firefox中无法触发click
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    })
  })
}

export function downloadAttchment2(fileid, filename) {
  // 鉴权下载文件
  
  const url = process.env.VUE_APP_BASE_API +'/api/attach/download2' + '/' + fileid
  // window.open(fileDownloadApi + '/' + fileid, '_blank')
  requestFile(url).then(blob => {
    blobToDataURL(blob).then(dataURL => {
      const a = document.createElement('a')
      a.download = filename
      a.href = dataURL
      // a.target = '_blank'
      // 修复firefox中无法触发click
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    })
  })
}

export function downloadAttchmentTemplate() {
  // 鉴权下载文件
  
  const url = process.env.VUE_APP_BASE_API +'/releaseMange/main/downloadAttchmentTemplate' 
  // window.open(fileDownloadApi + '/' + fileid, '_blank')
  requestFile(url).then(blob => {
    blobToDataURL(blob).then(dataURL => {
      const a = document.createElement('a')
      a.download = 'model.zip'
      a.href = dataURL
      // a.target = '_blank'
      // 修复firefox中无法触发click
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    })
  })
}


export function getStepHandleInfo(parentDocId) {
  return request({
    url: '/api/flowInfo/getStepHandleInfo?parentDocId=' + parentDocId ,
    method: 'get',
  })
}


export function getbpmlog(parentDocId) {
  return request({
    url: '/api/bpm/runtime/operation/getbpmlog/' + parentDocId ,
    method: 'get',
  })
}

export function delDocTask(todoId, taskid) {
  return request({
    url: '/api/bpm/runtime/operation/delete/draft/' + todoId + '/' + taskid,
    method: 'delete'
  })
}

export function getSendHQFileRecord(parentDocId) {
  return request({
    url: '/api/bpm/runtime/tosendhq/getsendrecord/' + parentDocId,
    method: 'get'
  })
}

export function queryCommonrelationList(query) {
  return request({
    url: '/system/itsmcommonrelation/list',
    method: 'get',
    params: query,
  })
}


export function getCharts(id) {
  return request({
    url: '/api/flowInfo/getChart?id='+id,
    method: 'get'
  })
}

export function saveyys(data) {
  return request({
    url: '/system/itsmeventcheck',
    method: 'post',
    data: data,
  })
}

export function saveyys2(data) {
  return request({
    url: '/system/itsmeventcheck/add2',
    method: 'post',
    data: data,
  })
}


export function queryyys(query) {
  return request({
    url: '/system/itsmeventcheck/list',
    method: 'get',
    params: query,
  })
}

export function jumpNextStep(data) {
  return request({
    url: '/api/bpm/runtime/operation/jumpNextStep',
    method: 'put',
    data: data,
  })
}

export function eventNextStep(data) {
  return request({
    url: '/api/bpm/runtime/operation/eventNextStep',
    method: 'put',
    data: data,
  })
}

export function returnTodo(data) {
  return request({
    url: '/api/bpm/runtime/operation/returnTodo',
    method: 'put',
    data: data,
  })
}

export function receiveTodo(data) {
  return request({
    url: '/api/bpm/runtime/operation/receiveTodo',
    method: 'put',
    data: data,
  })
}

export function demandNextStep(data) {
  return request({
    url: '/api/bpm/runtime/operation/demandNextStep',
    method: 'put',
    data: data,
  })
}

export function savehandle(data) {
  return request({
    url: '/system/itsmeventhandle',
    method: 'post',
    data: data,
  })
}

export function savehandle2(data) {
  return request({
    url: '/system/itsmeventhandle/add2',
    method: 'post',
    data: data,
  })
}

export function queryhandle(query) {
  return request({
    url: '/system/itsmeventhandle/list',
    method: 'get',
    params: query,
  })
}

export function savefinish(data) {
  return request({
    url: '/system/itsmeventfinish',
    method: 'post',
    data: data,
  })
}

export function savefinish2(data) {
  return request({
    url: '/system/itsmeventfinish/add2',
    method: 'post',
    data: data,
  })
}

export function queryfinish(query) {
  return request({
    url: '/system/itsmeventfinish/list',
    method: 'get',
    params: query,
  })
}


export function saveTroubleCheck(data) {
  return request({
    url: '/system/itsmtroublecheck',
    method: 'post',
    data: data,
  })
}

export function saveTroubleCheck2(data) {
  return request({
    url: '/system/itsmtroublecheck/add2',
    method: 'post',
    data: data,
  })
}


export function queryTroubleCheck(query) {
  return request({
    url: '/system/itsmtroublecheck/list',
    method: 'get',
    params: query,
  })
}

export function saveTroubleHandle(data) {
  return request({
    url: '/system/itsmtroublehandle',
    method: 'post',
    data: data,
  })
}

export function saveOperateExecute(data) {
  return request({
    url: '/system/itsmoperationexecute',
    method: 'post',
    data: data,
  })
}

export function queryOperateExecute(query) {
  return request({
    url: '/system/itsmoperationexecute/list',
    method: 'get',
    params: query,
  })
}

export function saveOperateCheck(data) {
  return request({
    url: '/system/itsmoperationcheck',
    method: 'post',
    data: data,
  })
}

export function queryOperateCheck(query) {
  return request({
    url: '/system/itsmoperationcheck/list',
    method: 'get',
    params: query,
  })
}

export function saveTroubleHandle2(data) {
  return request({
    url: '/system/itsmtroublehandle/add2',
    method: 'post',
    data: data,
  })
}


export function saveItsmrepairhis(data) {
  return request({
    url: '/system/itsmrepairhis',
    method: 'post',
    data: data,
  })
}

export function queryItsmrepairhis(query) {
  return request({
    url: '/system/itsmrepairhis/list',
    method: 'get',
    params: query,
  })
}


export function queryTroubleHandle(query) {
  return request({
    url: '/system/itsmtroublehandle/list',
    method: 'get',
    params: query,
  })
}



export function saveTroubleConfirm(data) {
  return request({
    url: '/system/itsmtroubleconfirm',
    method: 'post',
    data: data,
  })
}


export function queryTroubleConfirm(query) {
  return request({
    url: '/system/itsmtroubleconfirm/list',
    method: 'get',
    params: query,
  })
}


export function saveTroubleFinish(data) {
  return request({
    url: '/system/itsmtroublefinish',
    method: 'post',
    data: data,
  })
}

export function saveTroubleFinish2(data) {
  return request({
    url: '/system/itsmtroublefinish/add2',
    method: 'post',
    data: data,
  })
}


export function queryTroubleFinish(query) {
  return request({
    url: '/system/itsmtroublefinish/list',
    method: 'get',
    params: query,
  })
}

export function saveProblemCheck(data) {
  return request({
    url: '/system/itsmproblemcheck',
    method: 'post',
    data: data,
  })
}

export function saveProblemCheck2(data) {
  return request({
    url: '/system/itsmproblemcheck/add2',
    method: 'post',
    data: data,
  })
}


export function queryProblemCheck(query) {
  return request({
    url: '/system/itsmproblemcheck/list',
    method: 'get',
    params: query,
  })
}

export function saveProblemHandle(data) {
  return request({
    url: '/system/itsmproblemhandle',
    method: 'post',
    data: data,
  })
}

export function saveProblemHandle2(data) {
  return request({
    url: '/system/itsmproblemhandle/add2',
    method: 'post',
    data: data,
  })
}


export function queryProblemHandle(query) {
  return request({
    url: '/system/itsmproblemhandle/list',
    method: 'get',
    params: query,
  })
}

export function saveProblemConfirm(data) {
  return request({
    url: '/system/itsmproblemconfirm',
    method: 'post',
    data: data,
  })
}

export function saveProblemConfirm2(data) {
  return request({
    url: '/system/itsmproblemconfirm/add2',
    method: 'post',
    data: data,
  })
}


export function queryProblemConfirm(query) {
  return request({
    url: '/system/itsmproblemconfirm/list',
    method: 'get',
    params: query,
  })
}

export function saveDemandCheck(data) {
  return request({
    url: '/system/itsmdemandreview',
    method: 'post',
    data: data,
  })
}

export function saveDemandCheck2(data) {
  return request({
    url: '/system/itsmdemandreview/add2',
    method: 'post',
    data: data,
  })
}


export function queryDemandCheck(query) {
  return request({
    url: '/system/itsmdemandreview/list',
    method: 'get',
    params: query,
  })
}

export function saveDemandTrack(data) {
  return request({
    url: '/system/itsmdemandtrack',
    method: 'post',
    data: data,
  })
}

export function saveDemandTrack2(data) {
  return request({
    url: '/system/itsmdemandtrack/add2',
    method: 'post',
    data: data,
  })
}

export function saveDemandTrack3(data) {
  return request({
    url: '/system/itsmdemandtrack/add3',
    method: 'post',
    data: data,
  })
}

export function deleteDemandTrack(id) {
  return request({
    url: '/system/itsmdemandtrack/delete/'+id,
    method: 'get'
  })
}

export function queryDemandTrackById(id) {
  return request({
    url: '/system/itsmdemandtrack/'+id,
    method: 'get'
  })
}


export function queryDemandTrack(query) {
  return request({
    url: '/system/itsmdemandtrack/list',
    method: 'get',
    params: query,
  })
}

export function queryDemandTracklist(query) {
  return request({
    url: '/system/itsmdemandtrack/listt',
    method: 'get',
    params: query,
  })
}

export function checkreport(id) {
  return request({
    url: '/system/itsmTrouble/checkreport?id=' +id,
    method: 'get',
  })
}

export function makeTroubleReport(id) {
  return request({
    url: '/system/itsmTrouble/testDoc?id=' +id,
    method: 'get',
  })
}

export function getReport(id) {
  return request({
    url: '/system/itsmTrouble/getReport?id=' +id,
    method: 'get',
  })
}

export function saveReport(data) {
  return request({
    url: '/system/itsmtroublereport',
    method: 'post',
    data: data,
  })
}


export function queryQualityReview(query) {
  return request({
    url: '/system/itsmqualityassessdirectorreview/list',
    method: 'get',
    params: query,
  })
}

export function saveQualityReview(data) {
  return request({
    url: '/system/itsmqualityassessdirectorreview',
    method: 'post',
    data: data,
  })
}

export function queryQualityConfirm(query) {
  return request({
    url: '/system/itsmqualityassessproviderconfirm/list',
    method: 'get',
    params: query,
  })
}

export function saveQualityConfirm(data) {
  return request({
    url: '/system/itsmqualityassessproviderconfirm',
    method: 'post',
    data: data,
  })
}

export function queryQualityFinallyConfirm(query) {
  return request({
    url: '/system/itsmqualityassessfinallyconfirm/list',
    method: 'get',
    params: query,
  })
}

export function saveQualityFinallyConfirm(data) {
  return request({
    url: '/system/itsmqualityassessfinallyconfirm',
    method: 'post',
    data: data,
  })
}

export function queryDirectorVerify(query) {
  return request({
    url: '/system/itsmqualityassessdirectorverify/list',
    method: 'get',
    params: query,
  })
}

export function saveDirectorVerify(data) {
  return request({
    url: '/system/itsmqualityassessdirectorverify',
    method: 'post',
    data: data,
  })
}

export function queryExpertReview(query) {
  return request({
    url: '/system/itsmqualityassessexpertverify/list',
    method: 'get',
    params: query,
  })
}

export function saveExpertReview(data) {
  return request({
    url: '/system/itsmqualityassessexpertverify',
    method: 'post',
    data: data,
  })
}


export function assisting(data) {
  return request({
    url: '/api/bpm/runtime/operation/assisting',
    method: 'put',
    data: data,
  })
}

export function getTreeById(id) {
  return request({
    url: '/sysDict/getTreeById?id='+id,
    method: 'get',
  })
}

export function demand2Release(id,bh) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository/demand2Release?id=' + id+ '&bh='+bh,
    method: 'get',
  })
}

export function getList4relation(type,no,step) {
  return request({
    url: '/system/itsmcommonrelation/getList4relation?type=' + type+"&no="+no+"&step="+step ,
    method: 'get',
  })
}

export function listByName(flowName) {
  return request({
    url: '/system/bpmFlowStepInfo/listByName?flowName=' + flowName ,
    method: 'get',
  })
}

export function getEvent(no) {
  return request({
    url: '/system/itsmcommonrelation/getEvent?no=' + no ,
    method: 'get',
  })
}

export function getTrouble(no) {
  return request({
    url: '/system/itsmcommonrelation/getTrouble?no=' + no ,
    method: 'get',
  })
}

export function getProblem(no) {
  return request({
    url: '/system/itsmcommonrelation/getProblem?no=' + no ,
    method: 'get',
  })
}

export function getDemand(no) {
  return request({
    url: '/system/itsmcommonrelation/getDemand?no=' + no,
    method: 'get',
  })
}

export function getRelease(no) {
  return request({
    url: '/system/itsmcommonrelation/getRelease?no=' + no,
    method: 'get',
  })
}

export function getOp(no) {
  return request({
    url: '/system/itsmcommonrelation/getOp?no=' + no,
    method: 'get',
  })
}

export function getService(no) {
  return request({
    url: '/system/itsmcommonrelation/getService?no=' + no,
    method: 'get',
  })
}

export function addRelationShip(data) {
  return request({
    url: '/system/itsmcommonrelation/add2',
    method: 'put',
    data: data,
  })
}
//  

export function getLastFlowInfo(query) {
  return request({
    url: '/api/flowInfo/getLastFlowInfo',
    method: 'get',
    params: query,
  })
}

export function removeFLowByIds(id) {
  return request({
    url: '/api/bpm/runtime/manager/removeFLowByIds/' + id,
    method: 'get',
  })
}

export function getNewFid(query) {
  return request({
    url: '/system/bpmTodo/getBpmTodo' ,
    method: 'get',
    params: query
  })
}


export function CommonExpressionList(query) {
  return request({
    url: '/system/itsmCommonExpressions/list' ,
    method: 'get',
    params: query
  })
}


export function cleanData(query) {
  return request({
    url: '/system/itsmeventmain/cleanData',
    method: 'get',
    params: query,
  })
}

export function statZYSQbycurstepname(query) {
  return request({
    url: '/system/ueSbzysqd/statbycurstepname',
    method: 'get',
    params: query,
  })
}


export function statSBRCbycurstepname(query) {
  return request({
    url: '/system/ueSbjrjfsqd/statbycurstepname',
    method: 'get',
    params: query,
  })
}

export function statSBTCbycurstepname(query) {
  return request({
    url: '/system/ueSbtcjfsqd/statbycurstepname',
    method: 'get',
    params: query,
  })
}

export function statRYYWbycurstepname(query) {
  return request({
    url: '/system/ueYwrybgsqd/statbycurstepname',
    method: 'get',
    params: query,
  })
}

export function statRYByCurrentStepAllDate(query) {
  return request({
    url: '/system/ueYwrybgsqd/statByCurrentStepAllDate',
    method: 'get',
    params: query,
  })
}

export function statYYByHT(query) {
  return request({
    url: '/system/ueYwrybgsqd/statYYByHT',
    method: 'get',
    params: query,
  })
}

export function statYYByHTAllDate(query) {
  return request({
    url: '/system/ueYwrybgsqd/statYYByHTAllDate',
    method: 'get',
    params: query,
  })
}

export function statYYByDWT(query) {
  return request({
    url: '/system/ueYwrybgsqd/statYYByDW',
    method: 'get',
    params: query,
  })
}

export function statYYByDWAllDate(query) {
  return request({
    url: '/system/ueYwrybgsqd/statYYByDWAllDate',
    method: 'get',
    params: query,
  })
}

export function statTCByCurrentStepAllDate(query) {
  return request({
    url: '/system/ueYwrybgsqd/statByCurrentStepAllDate',
    method: 'get',
    params: query,
  })
}

export function statJRByCurrentStepAllDate(query) {
  return request({
    url: '/system/ueSbjrjfsqd/statByCurrentStepAllDate',
    method: 'get',
    params: query,
  })
}

export function statZYByCurrentStepAllDate(query) {
  return request({
    url: '/system/ueSbzysqd/statByCurrentStepAllDate',
    method: 'get',
    params: query,
  })
}

export function addotherDisabledUser(query) {
  return request({
    url: '/system/itsmCommonDisableduser/addother',
    method: 'get',
    params: query,
  })
}

export function querydept(query) {
  return request({
    url: '/system/itsmCommonDisableduser/querydept',
    method: 'get',
    params: query,
  })
}

export function queryunit(query) {
  return request({
    url: '/system/itsmCommonDisableduser/queryunit',
    method: 'get',
    params: query,
  })
}


export function addotherCommonExpressions(query) {
  return request({
    url: '/system/itsmCommonExpressions/addOther',
    method: 'get',
    params: query,
  })
}


export function getYysjlshing(query) {
  return request({
    url: '/system/itsmeventcheck/getYysjlshing',
    method: 'get',
    params: query,
  })
}

export function getYysjlshed(query) {
  return request({
    url: '/system/itsmeventcheck/getYysjlshed',
    method: 'get',
    params: query,
  })
}

export function getZdhkshing(query) {
  return request({
    url: '/system/itsmeventcheck/getZdhkshing',
    method: 'get',
    params: query,
  })
}

export function getZdhkshed(query) {
  return request({
    url: '/system/itsmeventcheck/getZdhkshed',
    method: 'get',
    params: query,
  })
}

export function getSjkshing(query) {
  return request({
    url: '/system/itsmeventcheck/getSjkshing',
    method: 'get',
    params: query,
  })
}

export function getSjkshed(query) {
  return request({
    url: '/system/itsmeventcheck/getSjkshed',
    method: 'get',
    params: query,
  })
}

export function adjustworkday(date,num) {
  return request({
    url: '/system/itsmCommonTimetable/adjustworkday/'+date+'/'+num,
    method: 'get'
  })
}

export function myPlanDelay(data) {
  return request({
    url: '/system/itsmOperationOrder/myplan/delay',
    method: 'post',
    data: data,
  })
}


export function saveExpertVerify(data) {
  return request({
    url: '/system/itsmqualityassessexpertverify',
    method: 'post',
    data: data,
  })
}

export function queryExpertVerify(query) {
  return request({
    url: '/system/itsmqualityassessexpertverify/list',
    method: 'get',
    params: query,
  })
}