import request from '@/utils/request'

// 实现按周期统计分析需求工单情况 （旧）
// http://localhost:8183/system/itsmDemand/statbydate
// post
export function itsmDemandStatbydate(data) {
    return request({
      url: '/system/itsmDemand/statbydate',
      method: 'post',
      data: data
    })
  }
//需求统计分析 需求工单情况（新）
export function itsmDemandGetOrderConditions(data) {
  return request({
    url: '/system/itsmDemand/analysis/getOrderConditions',
    method: 'post',
    data: data
  })
}
// 实现按周期统计分析需求工单总情况已开发、未开发、需求取消占比 （旧）
// http://localhost:8183/system/itsmDemand/xqgdzqkzb
// post
export function itsmDemandXqgdzqkzb(data) {
    return request({
      url: '/system/itsmDemand/xqgdzqkzb',
      method: 'post',
      data: data
    })
  }
// 需求工单总量的趋势分析
// http://localhost:8183/system/itsmDemand/xqgdzlqs
// post
export function itsmDemandXqgdzlqs(data) {
    return request({
      url: '/system/itsmDemand/xqgdzlqs',
      method: 'post',
      data: data
    })
  }

// 需求工单总情况：需求处理情况占比、需求工单量趋势（新）
export function itsmDemandGetOrderOverConditions(data) {
    return request({
      url: '/system/itsmDemand/analysis/getOrderOverConditions',
      method: 'post',
      data: data
    })
  }
// 实现按周期统计分析功能模块占比  （旧）
// http://localhost:8183/system/itsmDemand/gnmkzb
// post
export function itsmDemandGnmkzb(data) {
    return request({
      url: '/system/itsmDemand/gnmkzb',
      method: 'post',
      data: data
    })
  }
// 实现按周期统计分析功能模块趋势分析
// http://localhost:8183/system/itsmDemand/gnmkqsfx
// post
export function itsmDemandGnmkqsfx(data) {
    return request({
      url: '/system/itsmDemand/gnmkqsfx',
      method: 'post',
      data: data
    })
  }

// 需求统计分析 ： 功能模块情况 (新)
export function itsmDemandGetModelConditions(data) {
    return request({
      url: '/system/itsmDemand/analysis/getModelConditions',
      method: 'post',
      data: data
    })
  }
// 实现按周期统计分析已超时未处理、未超时的需求工单的总数及占比
// http://localhost:8183/system/itsmDemand/xqgdcstj 
// post
export function itsmDemandXqgdcstj(data) {
    return request({
      url: '/system/itsmDemand/xqgdcstj',
      method: 'post',
      data: data
    })
  }
// 实现按周期统计分析需求类型，分析需求类型总情况占比
// http://localhost:8183/system/itsmDemand/xqlxzqkzb
// post
export function itsmDemandXqlxzqkzb(data) {
    return request({
      url: '/system/itsmDemand/xqlxzqkzb',
      method: 'post',
      data: data
    })
  }
// 实现按周期统计分析需求类型，分析需求类型总情况趋势分析
// http://localhost:8183/system/itsmDemand/xqlxzqkqs
// post
export function itsmDemandXqlxzqkqs(data) {
    return request({
      url: '/system/itsmDemand/xqlxzqkqs',
      method: 'post',
      data: data
    })
  }
//需求类型统计分析 (新)
  export function itsmDemandGetTypeConditions(data) {
    return request({
      url: '/system/itsmDemand/analysis/getTypeConditions',
      method: 'post',
      data: data
    })
  }
// 分析需求类型占比情况
// http://localhost:8183/system/itsmDemand/xqlxzb
// post
export function itsmDemandXqlxzb(data) {
    return request({
      url: '/system/itsmDemand/xqlxzb',
      method: 'post',
      data: data
    })
  }
// 分析需求类型趋势分析
// http://localhost:8183/system/itsmDemand/xqlxqs
// post
export function itsmDemandXqlxqs(data) {
    return request({
      url: '/system/itsmDemand/xqlxqs',
      method: 'post',
      data: data
    })
  }
// 实现按周期统计分析需求申请人TOPN（初始为：5），N自定义（5、10、15、20）
// http://localhost:8183/system/itsmDemand/xqsqrtj
// post
export function itsmDemandXqsqrtj(data) {
    return request({
      url: '/system/itsmDemand/xqsqrtj',
      method: 'post',
      data: data
    })
  }
// 需求处理人TOPN（初始为：5），N自定义（5、10、15、20）。
// http://localhost:8183/system/itsmDemand/xqclrtj
// post
export function itsmDemandXqclrtj(data) {
    return request({
      url: '/system/itsmDemand/xqclrtj',
      method: 'post',
      data: data
    })
  }
 
 //  需求处理人TopN  (新)
export function itsmDemandGetHandlerTop(data) {
    return request({
      url: '/system/itsmDemand/analysis/getHandlerTop',
      method: 'post',
      data: data
    })
  }
// 实现按周期统计分析需求申请单位（初始为：5），N自定义（5、10、15、20）
// http://localhost:8183/system/itsmDemand/xqsqdwtj
// post
export function itsmDemandXqsqdwtj(data) {
    return request({
      url: '/system/itsmDemand/xqsqdwtj',
      method: 'post',
      data: data
    })
  }
//需求申请单位TopN（新）
export function itsmDemandGetRegisterUnitTop(data) {
    return request({
      url: '/system/itsmDemand/analysis/getRegisterUnitTop',
      method: 'post',
      data: data
    })
  }

// 需求支持点击图表联查明细，导出统计数据
// http://localhost:8183/system/itsmDemand/xqdctjsj
// post
export function itsmDemandXqdctjsj(data) {
    return request({
      url: '/system/itsmDemand/xqdctjsj',
      method: 'post',
      data: data
    })
  }
//  需求处理及时率(新) 饼图
export function itsmDemandGetTimeOutConditions(data) {
  return request({
    url: '/system/itsmDemand/analysis/getTimeOutConditions',
    method: 'post',
    data: data
  })
}
// 需求申请人TOPN (新)
export function itsmDemandGetRegisterUserTop(data) {
  return request({
    url: '/system/itsmDemand/analysis/getRegisterUserTop',
    method: 'post',
    data: data
  })
}