import request from '@/utils/request'

// 查询运维人员变更记录列表
export function listUesubYwrybgsqd (query) {
  return request({
    url: '/system/uesubYwrybgsqd/list',
    method: 'get',
    params: query
  })
}

// 查询运维人员变更记录详细
export function getUesubYwrybgsqd (sid) {
  return request({
    url: '/system/uesubYwrybgsqd/' + sid,
    method: 'get'
  })
}

// 新增运维人员变更记录
export function addUesubYwrybgsqd (data) {
  return request({
    url: '/system/uesubYwrybgsqd',
    method: 'post',
    data: data
  })
}

// 修改运维人员变更记录
export function updateUesubYwrybgsqd (data) {
  return request({
    url: '/system/uesubYwrybgsqd',
    method: 'put',
    data: data
  })
}

// 删除运维人员变更记录
export function delUesubYwrybgsqd (sid) {
  return request({
    url: '/system/uesubYwrybgsqd/' + sid,
    method: 'delete'
  })
}

// 导出运维人员变更记录
export function exportUesubYwrybgsqd (query) {
  return request({
    url: '/system/uesubYwrybgsqd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesubYwrybgsqd/getInitData/' + dictTypes,
    method: 'get'
  })
}
