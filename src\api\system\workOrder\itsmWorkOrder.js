import request from '@/utils/request'

// 查询我创建的工作列表
export function listItsmWorkOrder (query) {
  return request({
    url: '/workOrder/itsmWorkOrder/list',
    method: 'get',
    params: query
  })
}

// 查询我创建的工作详细
export function getItsmWorkOrder (id) {
  return request({
    url: '/workOrder/itsmWorkOrder/' + id,
    method: 'get'
  })
}

// 新增我创建的工作
export function addItsmWorkOrder (data) {
  return request({
    url: '/workOrder/itsmWorkOrder',
    method: 'post',
    data: data
  })
}

// 修改我创建的工作
export function updateItsmWorkOrder (data) {
  return request({
    url: '/workOrder/itsmWorkOrder',
    method: 'put',
    data: data
  })
}

// 删除我创建的工作
export function delItsmWorkOrder (id) {
  return request({
    url: '/workOrder/itsmWorkOrder/' + id,
    method: 'delete'
  })
}


// 导出我创建的工作
export function exportItsmWorkOrder (query) {
  return request({
    url: '/workOrder/itsmWorkOrder/export',
    method: 'get',
    params: query
  })
}



// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/workOrder/itsmWorkOrder/getInitData/' + dictTypes,
    method: 'get'
  })
}

export function getUserList4gzdb (query) {
  return request({
    url: '/workOrder/itsmWorkOrder/getUserList4gzdb',
    method: 'get'
  })
}


export function changeResponseStatus (id,status) {
  return request({
    url: '/workOrder/itsmWorkOrder/changeResponseStatus?id='+id +"&status="+status,
    method: 'get'
  })
}

export function listMyItsmWorkOrder (query) {
  return request({
    url: '/workOrder/itsmWorkOrder/mylist',
    method: 'get',
    params: query
  })
}

export function listDbUsers () {
  return request({
    url: '/workOrder/itsmWorkOrder/listDbUsers',
    method: 'get',
  })
}

export function addSupervise (data) {
  return request({
    url: '/workOrder/itsmWorkOrder/addSupervise',
    method: 'post',
    data: data
  })
}

export function getSuperviseHistory (id) {
  return request({
    url: '/workOrder/itsmWorkOrder/getSuperviseHistory?id='+id,
    method: 'get',
  })
}

export function setDelay (id,checkUserId,checkUser,delayDate) {
  return request({
    url: '/workOrder/itsmWorkOrder/setDelay?id='+id+"&userId="+checkUserId+"&userName="+checkUser+"&delayDate="+delayDate,
    method: 'get',
  })
}

export function listYqshUsers () {
  return request({
    url: '/workOrder/itsmWorkOrder/listYqshUsers',
    method: 'get',
  })
}

export function listDelayItsmWorkOrder (query) {
  return request({
    url: '/workOrder/itsmWorkOrder/delaylist',
    method: 'get',
    params: query
  })
}

export function setCheck (data) {
  return request({
    url: '/workOrder/itsmWorkOrder/setCheck',
    method: 'post',
    data: data
  })
}

export function myExportItsmWorkOrder (query) {
  return request({
    url: '/workOrder/itsmWorkOrder/myExport',
    method: 'get',
    params: query
  })
}

export function delayExportItsmWorkOrder (query) {
  return request({
    url: '/workOrder/itsmWorkOrder/delayExport',
    method: 'get',
    params: query
  })
}