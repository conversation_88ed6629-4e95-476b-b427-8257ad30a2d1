import request from '@/utils/request'

// 查询工作日程列表
export function listItsmCommonTimetable (query) {
  return request({
    url: '/system/itsmCommonTimetable/list',
    method: 'get',
    params: query
  })
}

// 查询工作日程详细
export function getItsmCommonTimetable (name) {
  return request({
    url: '/system/itsmCommonTimetable/' + name,
    method: 'get'
  })
}

// 新增工作日程
export function addItsmCommonTimetable (data) {
  return request({
    url: '/system/itsmCommonTimetable',
    method: 'post',
    data: data
  })
}

// 修改工作日程-例外设置
export function updateItsmCommonTimetable (data) {
  return request({
    url: '/system/itsmCommonTimetable',
    method: 'put',
    data: data
  })
}

// 删除工作日程
export function delItsmCommonTimetable (name) {
  return request({
    url: '/system/itsmCommonTimetable/' + name,
    method: 'delete'
  })
}


// 导出工作日程
export function exportItsmCommonTimetable (query) {
  return request({
    url: '/system/itsmCommonTimetable/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmCommonTimetable/getInitData/' + dictTypes,
    method: 'get'
  })
}
