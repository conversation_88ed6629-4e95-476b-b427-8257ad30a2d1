<template>
  <div>
    <a-card  :bordered="false" style="margin-bottom: 10px;">  

      <a-form-model
        ref="myform"
        :model="Form"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules"
        class="table-page-search-wrapper"

        v-if="step1ctl==currentStep"
      >

      <a-row :span="24"  style="margin-top: 10px;">
        <a-col :span="8">
          <a-form-model-item label="需求编号:" prop="fd_bh" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            <a-input disabled 
              v-model="Form.fd_bh"
               style="width:350px;"
              
            />
          </a-form-model-item>
        </a-col>
      
        <a-col :span="8">
          <a-form-model-item label="开发工作量" prop="work_load" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            <a-select :disabled="disabled" placeholder="请" v-model="Form.work_load" style="width:350px;" >
              <a-select-option value="重大">
              重大
              </a-select-option>
              <a-select-option value="一般">
              一般
            </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="需求优先级" prop="priority" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            <a-select :disabled="disabled" placeholder="" v-model="Form.priority" style="width:350px;" >
              <a-select-option value="一般需求">
                一般需求
              </a-select-option>
              <a-select-option value="紧急需求">
                紧急需求
            </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :span="24"  style="margin-top: 10px;">
        <a-col :span="8">
          <a-form-model-item label="建单时间" prop="creation_time" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            <a-date-picker 
              :disabled="addDisable"
              style="width:350px;"
              v-model="Form.creation_time"
              value-format="YYYY-MM-DD HH:mm:ss"
                  :show-today="true"
                  placeholder="选择日期"
              show-time
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="申请时间" prop="register_time" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
            <a-date-picker :disabled="disabled"  style="width:350px;"
            value-format="YYYY-MM-DD HH:mm:ss"
              v-model="Form.register_time"
              :show-today="true"
              :format="CustomFormat"
                type="datetime"
                show-time
        
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="期待时间" prop="register_expect_time" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            <a-date-picker :disabled="disabled"  style="width:350px;"
              v-model="Form.register_expect_time"
              :show-today="true"
              :format="CustomFormat"
              type="datetime"
              show-time
              value-format="YYYY-MM-DD HH:mm:ss"
        
            />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :span="24"  style="margin-top: 10px;">
        
        <a-col :span="8">
          <a-form-model-item label="申请人" prop="application_user" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            <LDXK @setSelUser="setSelUser" :complainUser="Form.application_user" style="width: 350px;" :disabled="disabled" ref="ldxk" ></LDXK>
            <!-- <a-auto-complete style="width:350px;" :disabled="disabled"
                v-model="Form.application_user"
                :data-source="dataList"
                
                placeholder="请输入单位名称"
                @search="handleSearch"
                @select="handleChange" 
              >

              </a-auto-complete> -->
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="申请单位" prop="application_unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            
            <a-auto-complete
                v-model="Form.application_unit"
                :data-source="applicationUnitList"
                style="width:350px;"
                :disabled="disabled"
                class="myautocomplete"
                @change="querydept4applicationUnit"
              />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="申请部门" prop="application_dept" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
            
            <a-auto-complete
                v-model="Form.application_dept"
                :data-source="applicationDeptList"
                style="width:350px;color: rgb(0 0 0);"
                :disabled="disabled"
                
                @change="querydept4applicationDeptList"
              />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :span="24"  style="margin-top: 10px;">
        
        <a-col :span="8">
          <a-form-model-item label="联系电话" prop="proposer_phone" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            <a-input :disabled="disabled" v-model="Form.proposer_phone" placeholder="请选择手机号码" style="width:350px;" />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="所属项目"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
            <a-select :disabled="disabled" placeholder="请选择所属项目" v-model="Form.project" style="width:350px;"  >
              <a-select-option  v-for="(item,index) in xqssxmList" :key="index" :value="item.dictLabel" >{{ item.dictLabel }}
              
              </a-select-option>

            </a-select> 
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="需求类型" prop="demand_type" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
            <a-select :disabled="disabled" placeholder="请选择需求类型" v-model="Form.demand_type" style="width:350px;" >
              <a-select-option value="功能变更">
                功能变更
              </a-select-option>
              <a-select-option value="临时发布生成">
              临时发布生成
            </a-select-option>
            <a-select-option value="其他">
              其他
            </a-select-option>
            </a-select>
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :span="24"  style="margin-top: 10px;">
        
       
        <a-col :span="24">
          <a-form-model-item label="功能模块" prop="functional_module" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
            <a-cascader v-model="eventArr" :options="gzlx" style="width:100%;" @change="arr2eventstr" :disabled="disabled" />
            
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row :span="24"  style="margin-top: 10px;">
        <a-col :span="24">
          <a-form-model-item label="需求标题"  prop="fd_bt" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-input  :disabled="disabled" v-model="Form.fd_bt"  style="width:100%;" placeholder="请输入需求标题" />
            </a-form-model-item>
        </a-col>
        
      </a-row>
      <a-row :span="24"  style="margin-top: 10px;">
        <a-col :span="24">
          <a-form-model-item label="需求原因"  prop="reason" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form.reason"  style="width:100%;" placeholder="请输入需求原因" /> -->
              <a-textarea
                v-model="Form.reason"  placeholder="请输入需求原因" style="width:100%;"
                :rows="reasonAutoSizeFlagRows" @dblclick="ablClickExpandEvent('reasonAutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
        </a-col>       
      </a-row>
      <a-row :span="24"  style="margin-top: 10px;">
        <a-col :span="24">
          <a-form-model-item label="需求描述"  prop="detail" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
            <!-- <a-input :disabled="disabled" v-model="Form.detail"  style="width:100%;" placeholder="请输入需求描述" /> -->
            <a-textarea
                v-model="Form.detail"  placeholder="请输入需求描述" style="width:100%;"
                :rows="detailAutoSizeFlagRows" @dblclick="ablClickExpandEvent('detailAutoSizeFlag',$event)"   :readonly="disabled"
              />
          </a-form-model-item>
        </a-col>
      </a-row>
      <a-row v-if="step1ctl==currentStep" :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled"
                  :before-upload="handleBeforeUpload0">
                  <a-button  type="primary" :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts0"  @click="dlAttach(item.id,item.name)" closable @close="delHandl0(item.id)" >{{ item.name }}</a-tag>        
                
          </a-col>
        </a-row>
      <a-row :span="24"  style="margin-top: 10px;">
        <a-col :span="8">
          <a-form-model-item label="登记人"    prop="register_user" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
            <a-input style="width:350px;"
              v-model="Form.register_user" 
          :disabled="addDisable" 
            />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="登记单位"   prop="register_unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
              <a-input style="width:350px;"
                v-model="Form.register_unit"   
            :disabled="addDisable"
              />
            </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="登记部门"  prop="register_dept" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form.register_dept"
            :disabled="addDisable"
              />
            </a-form-model-item>
        </a-col>
      </a-row>
      
      </a-form-model> 

      <a-form-model
        ref="myform2"
        :model="Form2"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step2ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form2.result" :options="checkoptions" style="width:350px;" :disabled="disabled" @change="rnchange2"  />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form2.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row v-if="rulechange" :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form2.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form2.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion2AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion2AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload2" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts2"  @click="dlAttach(item.id,item.name)" closable @close="delCheck2(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form2.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form2.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form2.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>

    <a-form-model
        ref="myform3"
        :model="Form3"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step3ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form3.result" :options="checkoptions" style="width:100%" :disabled="disabled" @change="rnchange2"/>
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form3.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="开发负责人" prop="developmentLead" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input :disabled="disabled" v-model="Form3.developmentLead" style="width:350px;" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form3.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form3.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion3AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion3AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
       
  
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload3" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts3"  @click="dlAttach(item.id,item.name)" closable @close="delCheck3(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
       
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form3.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form3.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form3.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>


    <a-form-model
        ref="myform5"
        :model="Form5"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step5ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form5.result" :options="checkoptions" style="width:100%" :disabled="disabled" @change="rnchange" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form5.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item  style="width:100%;"  :labelCol="{span:1}" :wrapperCol="{span:21}" v-if="k8s" >
            <a-checkbox v-model="scbldsh" @change="onChange1">
              市场部领导审核
            </a-checkbox>
            <a-checkbox @change="onChange2">
              科室领导审核
            </a-checkbox>
            <a-checkbox @change="onChange3">
              中心领导审核
            </a-checkbox>
          </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form5.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form5.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion5AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion5AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
       
        
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload5" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts5"  @click="dlAttach(item.id,item.name)" closable @close="delCheck5(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form5.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form5.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form5.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>

    <a-form-model
        ref="myform4"
        :model="Form4"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step4ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form4.result" :options="checkoptions" style="width:100%" :disabled="disabled" @change="rnchange2" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form4.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form4.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form4.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion4AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion4AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        
       
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload4" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts4"  @click="dlAttach(item.id,item.name)" closable @close="delCheck4(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form4.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form4.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form4.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>

    <a-form-model
        ref="myform6"
        :model="Form6"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step6ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form6.result" :options="checkoptions" style="width:100%" :disabled="disabled" @change="rnchange2" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form6.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form6.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form6.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion6AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion6AutoSizeFlag',$event)"   :readonly="disabled"
              />
              
            </a-form-model-item>
          </a-col>
        </a-row>
        
        
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload6" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts6"  @click="dlAttach(item.id,item.name)" closable @close="delCheck6(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form6.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form6.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form6.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>

    <a-form-model
        ref="myform7"
        :model="Form7"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step7ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form7.result" :options="checkoptions" style="width:100%" :disabled="disabled" @change="rnchange2" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form7.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form7.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form7.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion7AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion7AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        
        
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload7" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts7"  @click="dlAttach(item.id,item.name)" closable @close="delCheck7(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form7.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form7.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form7.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>


    <a-form-model
        ref="myform8"
        :model="Form8"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step8ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form8.result" :options="checkoptions" style="width:100%" :disabled="disabled" @change="rnchange2" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form8.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form8.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form8.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion8AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion8AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload8" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts8"  @click="dlAttach(item.id,item.name)" closable @close="delCheck8(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form8.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form8.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form8.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>

    <a-form-model
        ref="myform9"
        :model="Form9"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules3"
        class="table-page-search-wrapper"

        v-if="step9ctl==currentStep"
      >

      <a-row  :span="24">
       
        <a-col :span="8">
          <a-form-model-item label="预计开发完成时间" prop="devFinishTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
            <a-date-picker show-time v-model="Form9.devFinishTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-form-model-item label="预计发布时间" prop="releaseTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
            <a-date-picker show-time v-model="Form9.releaseTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
          </a-form-model-item>
        </a-col>
        <a-col :span="8">
          <a-button @click="toRelease" type="primary" >到发布</a-button>
        </a-col>
      </a-row>
      
      
     
    </a-form-model>
    <a-row v-if="step9ctl==currentStep" :span="24">
      <a-table  style="margin-top:20px;margin-right: 10px;width:95%;"  :columns="columns99" 
          :dataSource="f9List" bordered :row-selection="{ selectedRowKeys: selectedRowKeys4, onChange: onSelectChange4 }"> 
          <template slot="attachmentSlot" slot-scope="text,record,index">
              <a-button v-if="record.attachmentId!=null" @click="dlAttach(record.attachmentId,record.attachment)">{{ record.attachment }}</a-button>
          </template>
          <template slot="opSlot" slot-scope="text,record,index">
              <a-button @click="reopenTrack(record.id)" :disabled="disabled">编辑 </a-button>
              <a-button @click="delTrack(record.id)" :disabled="disabled">删除</a-button>
          </template>
          
      </a-table>
    </a-row>
    <a-row v-if="step9ctl==currentStep" :span="24">
      <a-button style="width:100%" @click="newTrackForm" :disabled="disabled" type="primary">新增跟踪纪录</a-button>
        
      <a-modal v-model="newTracker" title="新增跟踪纪录" @ok="save99" >
        <a-form-model
        ref="myform99"
        :model="Form99"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules3"
        class="table-page-search-wrapper"

        v-if="step9ctl==currentStep"
        >
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="开发进度" prop="developSchedule" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-select v-model="Form99.developSchedule" style="width:100%;" >
                <a-select-option value="待处理">待处理</a-select-option>
                <a-select-option value="已开发">已开发</a-select-option>
                <a-select-option value="已发布">已发布</a-select-option>
              </a-select>
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="跟踪内容" prop="trackDirections" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form99.trackDirections" style="width:100%;" /> -->
              <a-textarea
                v-model="Form99.trackDirections"  placeholder="请输入跟踪内容" style="width:100%;"
                :rows="trackDirectionsAutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion10AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload99" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in Form99.atts"  @click="dlAttach(item.id,item.name)" closable @close="delCheck99(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        </a-form-model>
      </a-modal>
    </a-row>

    <a-form-model
        ref="myform10"
        :model="Form10"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step10ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form10.result" :options="checkoptions" style="width:100%" :disabled="disabled" @change="redochange" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form10.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form10.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form10.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion10AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion10AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload10" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts10"  @click="dlAttach(item.id,item.name)" closable @close="delCheck10(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form10.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form10.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form10.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>

    <a-form-model
        ref="myform11"
        :model="Form11"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 16 }"
        :rules="rules2"
        class="table-page-search-wrapper"

        v-if="step11ctl==currentStep"
      >
        <a-row  :span="24">
          <a-col :span="8">
            <a-form-model-item label="审核结果" prop="result" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:12}" >
              <a-radio-group v-model="Form11.result" :options="checkoptions" style="width:100%" :disabled="disabled" @change="redochange" />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核时间" prop="reviewTime" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-date-picker show-time v-model="Form11.reviewTime" valueFormat="YYYY-MM-DD HH:mm:ss" style="width:350px;" :disabled="disabled" />
            </a-form-model-item>
          </a-col>
        </a-row>
        <a-row  :span="24">
          <a-col :span="24">
            <a-form-model-item label="审核意见" prop="opinion" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <!-- <a-input :disabled="disabled" v-model="Form11.opinion" style="width:100%;" /> -->
              <a-textarea
                v-model="Form11.opinion"  placeholder="请输入审核意见" style="width:100%;"
                :rows="opinion11AutoSizeFlagRows" @dblclick="ablClickExpandEvent('opinion11AutoSizeFlag',$event)"   :readonly="disabled"
              />
            </a-form-model-item>
          </a-col>
        </a-row>
        
        <a-row  :span="24">
          <a-col :span="24"   >
            <a-form-model-item label="附件"  style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:22}" >
              <a-upload :multiple="true" :file-list="uploadfileList"  :disabled="disabled" 
              :before-upload="handleBeforeUpload11" >
                  <a-button  type="primary"  :disabled="disabled" >
                    <a-icon type="upload" />
                    上传附件
                  </a-button>
              </a-upload>
            </a-form-model-item>
            <a-tag color="purple"  :disabled="disabled" v-for="(item,index) in atts11"  @click="dlAttach(item.id,item.name)" closable @close="delCheck11(item.id)" >{{ item.name }}</a-tag>            

           
          </a-col>
        </a-row>
        <a-row :span="24"  style="margin-top: 10px;">
          <a-col :span="8">
            <a-form-model-item label="审核人"    prop="userName" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
              <a-input style="width:350px;"
                v-model="Form11.userName" 
                :disabled="addDisable" 
              />
            </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核单位"   prop="unit" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                <a-input style="width:350px;"
                  v-model="Form11.unit"   
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
          <a-col :span="8">
            <a-form-model-item label="审核部门"  prop="department" style="width:100%;"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input style="width:350px;"
                  v-model="Form11.department"
              :disabled="addDisable"
                />
              </a-form-model-item>
          </a-col>
        </a-row>
    </a-form-model>
      
  </a-card>
  </div>
</template>
<script>

import { listUser2 } from '@/api/system/user'
import {getInfo} from '@/api/login'
import { getInitData } from '@/api/system/bpm/uesDevice'
import { getRoleUser } from '@/api/system/ywrygl/uesWxryqkb'
import { downloadAttchment2,getTreeById,saveDemandTrack3,queryDemandTracklist,queryDemandTrackById,deleteDemandTrack,demand2Release,querydept,queryunit } from '@/api/system/ywrygl/operation'
import axios from 'axios'
import moment from 'moment';
import  LDXK  from '@/components/bpm/LDXK.vue'


export default {
  name: 'Formxqdj',
  components: {
    LDXK
  },
  props: {
    doc:{
      type:Object
    },
    bussType:{
      type:String
    },
    autoTitle:{
      type:String
    },
    disabled:{
      type:Boolean
    },
    currentStep:{
      type:String
    },
    steps:{
      type: Array 
    },
    keyindex:{
      type: Number
    },
    doc2:{
      type:Object
    },
    doc3:{
      type:Object
    },
    doc4:{
      type:Object
    },
    doc5:{
      type:Object
    },
    doc6:{
      type:Object
    },
    doc7:{
      type:Object
    },
    doc8:{
      type:Object
    },
    doc9:{
      type:Object
    },
    doc10:{
      type:Object
    },
    doc11:{
      type:Object
    },
    doc2arr:{
      type:Array
    },
    doc3arr:{
      type:Array
    },
    doc4arr:{
      type:Array
    },
    doc5arr:{
      type:Array
    },
    doc6arr:{
      type:Array
    },
    doc7arr:{
      type:Array
    },
    doc8arr:{
      type:Array
    },
    doc9arr:{
      type:Array
    },
    doc10arr:{
      type:Array
    },
    doc11arr:{
      type:Array
    },
  },
  
  
  data() {
    
    const ktkbtValidator = (rule, value, callback) =>{
      
      if(this.currentStep == 2){
        if(this.Form2.result == 0){
          if(value == null || value == '' || undefined ==value){
            callback(new Error('请输入审核意见'))
          }
        }
      }
      if(this.currentStep == 3){
        if(this.Form3.result == 0){
          if(value == null || value == '' || undefined ==value){
            callback(new Error('请输入审核意见'))
          }
        }
      }
      if(this.currentStep == 4){
        if(this.Form4.result == 0){
          if(value == null || value == '' || undefined ==value){
            callback(new Error('请输入审核意见'))
          }
        }
      }
      if(this.currentStep == 5){
        if(this.Form5.result == 0){
          if(value == null || value == '' || undefined ==value){
            callback(new Error('请输入审核意见'))
          }
        }
      }
      if(this.currentStep == 6){
        if(this.Form6.result == 0){
          if(value == null || value == '' || undefined ==value){
            callback(new Error('请输入审核意见'))
          }
        }
      }
      if(this.currentStep == 7){
        if(this.Form7.result == 0){
          if(value == null || value == '' || undefined ==value){
            callback(new Error('请输入审核意见'))
          }
        }
      }
      if(this.currentStep == 8){
        if(this.Form8.result == 0){
          if(value == null || value == '' || undefined ==value){
            callback(new Error('请输入审核意见'))
          }
        }
      }
      if(this.currentStep == 10){
        if(this.Form10.result == 0){
          if(value == null || value == '' || undefined ==value){
            callback(new Error('请输入审核意见'))
          }
        }
      }
      callback()
    }

    return {
      rulechange:true,
      scbldsh:false,
      k8s:true,
      xqssxmList:[],
      applicationUnitList:[],
      applicationDeptList:[],
      newTracker:false,
      columns9:[],
      f9List:[],
      eventArr:[],
      gzlx: [],
      f3show:false,
      step1ctl:'需求登记',
      step2ctl:'业务科室领导审核',
      step3ctl:'系统开发商审核',
      step4ctl:'自动化科业务人员审核',
      step5ctl:'自动化科审核',
      step6ctl:'市场部领导审核',
      step7ctl:'科室领导审核',
      step8ctl:'中心领导审核',
      step9ctl:'系统开发商处理',
      step10ctl:'自动化科业务人员确认',
      step11ctl:'需求登记人员确认',
      step12ctl:12,
      step13ctl:13,
      addDisable:true,
      form: this.$form.createForm(this),
      CustomFormat: 'YYYY-MM-DD HH:mm:ss',
      opinion11AutoSizeFlagRows:1, // 审核意见行数
      opinion11AutoSizeFlag:false, // 审核意见是否自动展开
      opinion10AutoSizeFlagRows:1, // 审核意见行数
      opinion10AutoSizeFlag:false, // 审核意见是否自动展开
      trackDirectionsAutoSizeFlagRows:1,  // 跟踪内容
      trackDirectionsAutoSizeFlag:false, // 跟踪内容是否自动展开
      opinion8AutoSizeFlagRows:1, // 审核意见行数
      opinion8AutoSizeFlag:false, // 审核意见是否自动展开
      opinion7AutoSizeFlagRows:1, // 审核意见行数
      opinion7AutoSizeFlag:false, // 审核意见是否自动展开
      opinion6AutoSizeFlagRows:1, // 审核意见行数
      opinion6AutoSizeFlag:false, // 审核意见是否自动展开
      opinion4AutoSizeFlagRows:1, // 审核意见行数
      opinion4AutoSizeFlag:false, // 审核意见是否自动展开
      opinion5AutoSizeFlagRows:1, // 审核意见行数
      opinion5AutoSizeFlag:false, // 审核意见是否自动展开
      opinion3AutoSizeFlagRows:1, // 审核意见行数
      opinion3AutoSizeFlag:false, // 审核意见是否自动展开
      opinion2AutoSizeFlagRows:1, // 审核意见行数
      opinion2AutoSizeFlag:false, // 审核意见是否自动展开
      detailAutoSizeFlagRows:1,   //需求描述行数
      detailAutoSizeFlag:false,   //需求描述是否自动展开
      reasonAutoSizeFlagRows:1,   //需求原因行数
      reasonAutoSizeFlag:false,   // 需求原因是否自动展开
      Form11:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:null,
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:null,
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null,

      },
      Form10:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:'1',
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:'同意',
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null,

      },
      Form99:{
        id:null,
        demandId:null,
        developSchedule:null,
        trackDirections:null,
        devFinishTime:null,
        releaseTime:null,
        stalker:null,
        trackUnit:null,
        trackDepartment:null,
        trackTime:null,
        isRelease:null,
        attachment:null,
        atts:[],

      },
      Form9:{
        id:null,
        demandId:null,
        developSchedule:null,
        trackDirections:null,
        devFinishTime:null,
        releaseTime:null,
        stalker:null,
        trackUnit:null,
        trackDepartment:null,
        trackTime:null,
        isRelease:null,

      },
      Form8:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:'1',
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:'同意',
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null,

      },
      Form7:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:'1',
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:'同意',
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null,

      },
      Form6:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:'1',
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:'同意',
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null,

      },
      Form5:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:'1',
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:'同意',
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null,

      },
      Form4:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:'1',
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:'同意',
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null,
        ksld:[],
        scld:[],
        zxld:[],

      },
      Form3:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:'1',
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:'同意',
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null,
        zdhk:[],
        zdhkyw:[],

      },
      Form2:{
        id:null,
        registerId:null,
        taskId:null,
        taskName:null,
        result:'1',
        reviewTime:null,
        developmentLead:null,
        userId:null,
        userName:null,
        unit:null,
        department:null,
        opinion:'同意',
        priority:null,
        business:null,
        releases:null,
        attachment:null,
        attachmentId:null

      },
      Form: {
        fd_bh: '',//需求编号
        creation_time: '', //建单时间
        register_time: '', // 发生时间
        register_expect_time:'',//期待完成时间
        application_user:'',//申请人
        application_unit: '', //申请人单位
        application_dept: '', // 申请人部门
        work_load: '', //开发工作量
        priority:'',
        application_user_phone: '', //申请人电话
        proposer_phone: '', //手机号码'
        project: '', //所属项目
        demand_type:'',//需求类型
        functional_module:'',//功能模块
        event_effect:'',//影响度
        priority:'',//需求优先级
        event_prior:'',//优先级
        opinion: '', // 意见
        fd_bt:'',
        reason:'',
        detail:'',
        register_user: '',//登记人
        register_unit: '',//登记人单位
        register_dept: '',//登记人部门
      },
      checkoptions:[
        { label: '审核通过', value: '1' },
        { label: '审核不通过', value: '0' },
      ],
      columns99:[
        
        {

          title: '开发进度',
          dataIndex: 'developSchedule',
          ellipsis: true,          
          align: 'center',
         
        },
        {

          title: '跟踪说明',
          dataIndex: 'trackDirections',          
          ellipsis: true,          
          align: 'center',
         
        },
        {

          title: '跟踪时间',
          dataIndex: 'trackTime',          
          ellipsis: true,          
          align: 'center',

          },
        {

          title: '附件',
          dataIndex: 'attachment',
          scopedSlots: { customRender: 'attachmentSlot' },
          ellipsis: true,          
          align: 'center',
         
        },
        {

          title: '跟踪人',
          dataIndex: 'stalker',
          ellipsis: true,          
          align: 'center',
         
        },
        {

          title: '跟踪单位',
          dataIndex: 'trackUnit',
          ellipsis: true,          
          align: 'center',
         
        },
        {

          title: '跟踪部门',
          dataIndex: 'trackDepartment',
          ellipsis: true,          
          align: 'center',
         
        },
        {

        title: '操作',       
        scopedSlots: { customRender: 'opSlot' },
        ellipsis: true,          
        align: 'center',

        },
      ],
      rules3: {
        developSchedule: [
          { required: true, message: "开发进度不能为空"  },
        ],
        trackDirections: [
          { required: true, message: "跟踪说明不能为空"  },
        ],
        devFinishTime: [
          { required: true, message: "预计开发完成时间不能为空"  },
        ],
        releaseTime: [
          { required: true, message: "预计发布时间不能为空"  },
        ],
        trackTime: [
          { required: true, message: "跟踪时间不能为空"  },
        ],
        isRelease: [
          { required: true, message: "是否发布不能为空"  },
        ],
      },
      rules2: null,
      rules21: {
       
       result: [
         { required: true, message: "审核结果不能为空"  },
       ],
       reviewTime: [
         { required: true, message: "审核时间不能为空"  },
       ],
       opinion: [
         {  required: true, message: "审核意见不能为空"  },
       ],
       priority: [
         { required: true, message: "优先级不能为空"  },
       ],
       business: [
         { required: true, message: "是否影响业务不能为空"  },
       ],
       releases: [
         { required: true, message: "是否影响发布不能为空"  },
       ],
       developmentLead: [
         { required: true, message: "开发负责人不能为空"  },
       ],
       project: [
         { required: true, message: "项目不能为空"  },
       ],
       zdhk: [
         { required: true, message: "自动化科审核不能为空"  },
       ],
       zdhkyw: [
         { required: true, message: "自动化科业务不能为空"  },
       ],
     },
      rules20: {
       
       result: [
         { required: true, message: "审核结果不能为空"  },
       ],
       reviewTime: [
         { required: true, message: "审核时间不能为空"  },
       ],
      
       priority: [
         { required: true, message: "优先级不能为空"  },
       ],
       business: [
         { required: true, message: "是否影响业务不能为空"  },
       ],
       releases: [
         { required: true, message: "是否影响发布不能为空"  },
       ],
       developmentLead: [
         { required: true, message: "开发负责人不能为空"  },
       ],
       project: [
         { required: true, message: "项目不能为空"  },
       ],
       zdhk: [
         { required: true, message: "自动化科审核不能为空"  },
       ],
       zdhkyw: [
         { required: true, message: "自动化科业务不能为空"  },
       ],
     },
      rules : {
        creation_time: [
          { required: true, message: "建单时间不能为空"  },
        ],
        register_time: [
          { required: true, message: "申请时间不能为空" },
        ], 
        proposer_phone: [
          { required: true, message: "联系电话不能为空" },
        ], 
        register_expect_time : [
          { required: true, message: "期待完成时间不能为空"},
        ],   
        application_user: [
          { required: true, message: "申请人不能为空"  },
        ],  
        application_unit: [
          { required: true, message: "申请人单位不能为空"  },
        ], 
        application_dept: [
          { required: true, message: "申请人部门不能为空"  },
        ], 
      
        application_user_phone: [
          { required: true, message: "申请人电话不能为空"  },
        ], 
        priority:[
          { required: true, message: "需求优先级不能为空"  },
        ],
        work_load: [
          { required: true, message: "事件来源不能为空"  },
        ], 
        project: [
          { required: true, message: "所属项目不能为空"  },
        ], 
        demand_type: [
          { required: true, message: "需求类型不能为空"  },
        ], 
        functional_module: [
          { required: true, message: "功能模块不能为空"  },
        ], 
        event_effect:[
          { required: true, message: "影响度不能为空"  },
        ], 
        priority:[
          { required: true, message: "需求优先级不能为空"  },
        ],
        event_prior:[
          { required: true, message: "优先级不能为空"  },
        ], 
        fd_bt:[
          { required: true, message: "需求标题不能为空"  },
        ], 
        reason:[
          { required: true, message: "需求原因不能为空"  },
        ], 
        detail:[
          { required: true, message: "需求描述不能为空"  },
        ], 
        register_user:[
          { required: true, message: "登记人不能为空"  },
        ], 
        register_unit:[
          { required: true, message: "登记人单位不能为空"  },
        ],
        register_dept:[
          { required: true, message: "登记人部门不能为空"  },
        ], 
      },
      dataList: [],
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        userName: '',
      },
      pageNum: '1',
      pageSize: '10',
      businessArr:[],
      releasesArr:[],
      priorityArr:[],
      atts0:[],
      atts2:[],
      atts3:[],
      atts4:[],
      atts5:[],
      atts6:[],
      atts7:[],
      atts8:[],
      atts9:[],
      atts10:[],
      atts11:[],
      uploadfileList:[],      
      zdhkshrArr:[],
      zdhkywArr:[],
      ksldArr:[],
      scldArr:[],
      zxldArr:[],
      zdhkyws:[],
      zdhks:[],
      kslds:[],
      scblds:[],
      zxlds:[],
      dataList:[],
      dataList2:[],
      selectedRowKeys4: [],
      selectedRows4:[],
      
    }
  },
  created() {
    this.rules2 = this.rules21
    this.scbldsh = false
    this.zdhkyws = []
    this.zdhks = []
    getTreeById('198').then(rest=>{
      this.gzlx = rest.data
    })
    getInitData('demand_business,demand_release,demand_priority,xqssxm').then(res=>{
        this.priorityArr = res.data.demand_priority
        this.businessArr = res.data.demand_business
        this.releasesArr = res.data.demand_release
        this.xqssxmList = res.data.xqssxm 
      })
   // gzlx: [],getTreeById
    getRoleUser('fifteen,eighteen,nineteen,automationCheck,eleven').then(res=>{
      this.zdhkshrArr=new Array()
      res.automationCheck.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        this.zdhkshrArr.push(x)
      })

      this.zdhkywArr=new Array()
      res.eleven.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        this.zdhkywArr.push(x)
      })

      this.ksldArr=new Array()
      res.eighteen.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        this.ksldArr.push(x)
      })

      this.scldArr=new Array()
      res.fifteen.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        this.scldArr.push(x)
      })

      this.zxldArr=new Array()
      res.nineteen.forEach(item=>{
        let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
        this.zxldArr.push(x)
      })



    })
    if(this.bussType =='xqdj'){
      this.initxqdj()
    }
    
    if(null!=this.doc&&null!=this.doc.FORM_DATA&&null!=this.doc.FORM_DATA.MAIN_TABLE){
      this.Form = this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)]
      if(null!=this.Form.file_ids&&""!=this.Form.file_ids){
        let strs = this.Form.file_ids.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts0.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts0=[]
      }
      if(null!=this.Form.functional_module&&""!=this.Form.functional_module){
        let strs = this.Form.functional_module.split(",")
        strs.forEach(item=>{
          this.eventArr.push(item)
        })
      }else{
        this.eventArr=[]
      }
    }
    if(null!=this.doc2){
      this.Form2 = this.doc2
      if(null!=this.Form2.attachment&&""!=this.Form2.attachment){
        let strs = this.Form2.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts2.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts2=[]
      }
      if(this.Form2.result==0&&this.currentStep==this.step2ctl){
        this.$emit('rnchange2',this.Form2.result)
      }
    }
    if(null!=this.doc3){
      this.Form3 = this.doc3
      if(null!=this.Form3.attachment&&""!=this.Form3.attachment){
        let strs = this.Form3.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts3.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts3=[]
      }
      if(this.Form3.result==0&&this.currentStep==this.step3ctl){
        this.$emit('rnchange2',this.Form3.result)
      }
      this.f3show=true
      this.Form3.zdhkyw = new Array()
      this.Form3.zdhk = new Array()
      if(null!=this.Form3.taskName&&""!=this.Form3.taskName){
        
        let strs = this.Form3.taskName.split(",")
        strs.forEach(item=>{
          this.Form3.zdhkyw.push(item)
          this.zdhkyws.push(item)
        })
      }
      
      if(null!=this.Form3.attachmentId&&""!=this.Form3.attachmentId){
        let strs = this.Form3.attachmentId.split(",")
        strs.forEach(item=>{
          this.Form3.zdhk.push(item)
          this.zdhks.push(item)
        })
      }
      
    }
    if(null!=this.doc4){
      this.Form4 = this.doc4
      if(null!=this.Form4.attachment&&""!=this.Form4.attachment){
        let strs = this.Form4.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts4.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts4=[]
      }
      if(this.Form4.result==0&&this.currentStep==this.step4ctl){
        this.$emit('rnchange2',this.Form4.result)
      }
    
      
      this.Form4.ksld = new Array()
      this.Form4.scld = new Array()
      this.Form4.zxld = new Array()
      if(null!=this.Form4.attachmentId&&""!=this.Form4.attachmentId){
        let strs = this.Form4.attachmentId.split("_")
        let str= strs[0].split(",")

        str.forEach(item2=>{
          if('0'!=item2){
            this.Form4.ksld.push(item2)
            this.kslds.push(item2)
          }
        })
        let str1= strs[1].split(",")
        str1.forEach(item3=>{
          if('0'!=item3){
            this.Form4.scld.push(item3)
            this.scblds.push(item3)
          }
        })
        let str2= strs[2].split(",")
        str2.forEach(item4=>{
          if('0'!=item4){
            this.Form4.zxld.push(item4)
            this.zxlds.push(item4)
          }
        })


      }


    }
    if(null!=this.doc5){
      this.Form5 = this.doc5
      if(null!=this.Form5.attachment&&""!=this.Form5.attachment){
        let strs = this.Form5.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts5.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts5=[]
      }


      if(this.currentStep==this.step5ctl&&this.Form5.result==0){
        this.$emit('rb1stBtnCtl',this.Form5.result)
        if(this.Form5.result==0){
          this.k8s = false
        }else{
          this.k8s = true
        }
      }


    }
    if(null!=this.doc6){
      this.Form6 = this.doc6
      if(null!=this.Form6.attachment&&""!=this.Form6.attachment){
        let strs = this.Form6.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts6.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts6=[]
      }
      if(this.Form6.result==0&&this.currentStep==this.step6ctl){
        this.$emit('rnchange2',this.Form6.result)
      }
    }
    if(null!=this.doc7){
      this.Form7 = this.doc7
      if(null!=this.Form7.attachment&&""!=this.Form7.attachment){
        let strs = this.Form7.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts7.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts7=[]
      }
      if(this.Form7.result==0&&this.currentStep==this.step7ctl){
        this.$emit('rnchange2',this.Form7.result)
      }
    }
    if(null!=this.doc8){
      this.Form8 = this.doc8
      if(null!=this.Form8.attachment&&""!=this.Form8.attachment){
        let strs = this.Form8.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts8.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts8=[]
      }
      if(this.Form8.result==0&&this.currentStep==this.step8ctl){
        this.$emit('rnchange2',this.Form8.result)
      }
    }
    if(null!=this.doc9){
      this.Form9 = this.doc9
      if(null!=this.Form9.attachment&&""!=this.Form9.attachment){
        let strs = this.Form9.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts9.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts9=[]
      }
      if(null!=this.doc){
        let p = {
          demandId:this.Form9.id,
          isRelease:1
        }
        queryDemandTracklist(p).then(resp=>{
          this.f9List =[]
          resp.forEach(item=>{
            if(item.developSchedule==null||item.developSchedule==""||item.developSchedule==undefined){

            }else{
              this.f9List.push(item)
            }      
          })
        })
      }
    }
    if(null!=this.doc10){
      this.Form10 = this.doc10
      if(null!=this.Form10.attachment&&""!=this.Form10.attachment){
        let strs = this.Form10.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts10.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts10=[]
      }
      if(this.Form10.result==0&&this.currentStep==this.step10ctl){
        this.$emit('rb2ndBtnCtl',this.Form10.result)
      }
    }
    if(null!=this.doc11){
      this.Form11 = this.doc11
      if(null!=this.Form11.attachment&&""!=this.Form11.attachment){
        let strs = this.Form11.attachment.split(",")
        strs.forEach(item=>{
          let str= item.split("_")
          this.atts11.push({name:str[1],id:str[0]})
        })
      }else{
        this.atts11=[]
      }
      if(this.Form11.result==0&&this.currentStep==this.step11ctl){
        this.$emit('rb2ndBtnCtl',this.Form11.result)
      }
    }
    this.getCurrUserInfoFun()  
    if(this.disabled==true){
      this.showhistory()
    }
  },
  methods: {
    // 双击展开文本域内容可以显示更多
    ablClickExpandEvent(attr, event){
      this[attr]=!this[attr] 
      this.$nextTick(() => {
        if(this[attr]){  // 展开时候计算行高度
          // const textarea = this.$refs[attr+'myTextarea'].$el;  // 元素
          const scrollHeight = event.target.scrollHeight // 滚动条高度
          const lineHeight = 30  // 行高度
          this[attr+'Rows'] = parseInt(scrollHeight / lineHeight); // 展开自适应高度
        }else {
          this[attr+'Rows'] =1   // 收起的时候只展示一行
        }
        this.$forceUpdate()
      })
    },
    showhistory(){
      console.error(this.currentStep)
      this.Form = JSON.parse(JSON.stringify(this.Form))
      
      if(this.currentStep==this.step11ctl){
        
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step11ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form11 = this.doc11arr[ks]
        if(null!=this.Form11.attachment&&""!=this.Form11.attachment){
          let strs = this.Form11.attachment.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts11.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts11=[]
        }
      }
      if(this.currentStep==this.step10ctl){
        console.error('098810-----------')
        console.error(this.doc10arr)
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step10ctl){
              ks = ks +1
            }
          }
          
        }
        console.error(ks)
        this.Form10 = this.doc10arr[ks]
        if(null!=this.Form10.attachment&&""!=this.Form10.attachment){
          let strs = this.Form10.attachment.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts10.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts10=[]
        }
      }
      if(this.currentStep==this.step9ctl){
        console.error(this.doc9arr)
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step9ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form9 = this.doc9arr[ks]
        console.error(this.Form9)
        if(null!=this.Form9.attachment&&""!=this.Form9.attachment){
          let strs = this.Form9.attachment.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts9.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts9=[]
        }
        let p = {
          demandId:this.Form9.id,
          isRelease:2
        }
        queryDemandTracklist(p).then(resp=>{
          this.f9List =[]
          resp.forEach(item=>{
            if(item.developSchedule==null||item.developSchedule==""||item.developSchedule==undefined){

            }else{
              this.f9List.push(item)
            }
          })
        })
      }
      if(this.currentStep==this.step8ctl){
        
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step8ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form8 = this.doc8arr[ks]
        if(null!=this.Form8.attachment&&""!=this.Form8.attachment){
          let strs = this.Form8.attachment.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts8.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts8=[]
        }
      }
      if(this.currentStep==this.step7ctl){
        
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step7ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form7 = this.doc7arr[ks]
        if(null!=this.Form7.attachment&&""!=this.Form7.attachment){
          let strs = this.Form7.attachment.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts7.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts7=[]
        }
      }
      if(this.currentStep==this.step6ctl){
        
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step6ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form6 = this.doc6arr[ks]
        if(null!=this.Form6.attachment&&""!=this.Form6.attachment){
          let strs = this.Form6.attachment.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts6.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts6=[]
        }
      }
      if(this.currentStep==this.step5ctl){
        
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step5ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form5 = this.doc5arr[ks]
        if(null!=this.Form5.attachment&&""!=this.Form5.attachment){
          let strs = this.Form5.attachment.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts5.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts5=[]
        }
      }
      if(this.currentStep==this.step2ctl){
        
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step2ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form2 = this.doc2arr[ks]
        if(null!=this.Form2.checkAttachments&&""!=this.Form2.checkAttachments){
          let strs = this.Form2.checkAttachments.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts2.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts2=[]
        }
      }
      if(this.currentStep==this.step4ctl){
        
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step4ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form4 = this.doc4arr[ks]
        if(null!=this.Form4.attachment&&""!=this.Form4.attachment){
          let strs = this.Form4.attachmentId.split("_")
          let str= strs[0].split(",")

          str.forEach(item2=>{
            if('0'!=item2){
              this.Form4.ksld.push(item2)
              this.kslds.push(item2)
            }
          })
          let str1= strs[1].split(",")
          str1.forEach(item3=>{
            if('0'!=item3){
              this.Form4.scld.push(item3)
              this.scblds.push(item3)
            }
          })
          let str2= strs[2].split(",")
          str2.forEach(item4=>{
            if('0'!=item4){
              this.Form4.zxld.push(item4)
              this.zxlds.push(item4)
            }
          })
        }
 

      }
      if(this.currentStep==this.step3ctl){
        
        let ks = -1
        for(var i=0;i<=this.keyindex;i++){
          if(i<this.steps.length){
            if(this.steps[i].stepName==this.step3ctl){
              ks = ks +1
            }
          }
          
        }
        this.Form3 = this.doc3arr[ks]
        if(null!=this.Form3.attachment&&""!=this.Form3.attachment){
          let strs = this.Form3.attachment.split(",")
          strs.forEach(item=>{
            let str= item.split("_")
            this.atts3.push({name:str[1],id:str[0]})
          })
        }else{
          this.atts3=[]
        }
        
        this.f3show=true
        this.Form3.zdhkyw = new Array()
        this.Form3.zdhk = new Array()
        if(null!=this.Form3.taskName&&""!=this.Form3.taskName){
          
          let strs = this.Form3.taskName.split(",")
          strs.forEach(item=>{
            this.Form3.zdhkyw.push(item)
            this.zdhkyws.push(item)
          })
        }
        
        if(null!=this.Form3.attachmentId&&""!=this.Form3.attachmentId){
          let strs = this.Form3.attachmentId.split(",")
          strs.forEach(item=>{
            this.Form3.zdhk.push(item)
            this.zdhks.push(item)
          })
        }
      }
    },
    querydept4applicationDeptList(){
      let param={
        dept:this.Form.application_dept
      }
      querydept(param).then(res=>{
        this.applicationDeptList = []
        res.data.forEach(item=>{
          this.applicationDeptList.push(item.dept)
        })
      })
    },
    querydept4applicationUnit(){
      let param={
        unit:this.Form.application_unit
      }
      queryunit(param).then(res=>{
        this.applicationUnitList = []
        res.data.forEach(item=>{
          this.applicationUnitList.push(item.unit)
        })
      })
    },
    toRelease(){
      let that = this      
      demand2Release(this.Form.id,this.Form.fd_bh).then(res=>{
        if(null==res.msg){
          that.$message.success( '已经添加到发布库')
        }else{
          that.$message.error( res.msg)
        }
      })
    },
    save99(){
      let that = this
      that.$refs.myform99.validate((valid) => {
        if (valid){
          that.newTracker=false
          that.Form99.demandId = this.doc.TASK_INFO.id
          that.Form99.isRelease = 1
          console.error('999--------------')
          saveDemandTrack3(that.Form99).then(res=>{
            this.$emit('refreshParent')
              let p = { demandId: that.doc.TASK_INFO.id, isRelease: 1, }
              queryDemandTracklist(p).then(resp=>{
                that.f9List = resp 
              })     
          })
        }
      })
    },
    delTrack(id){
      let that =this
      deleteDemandTrack(id).then(dres=>{
        let p ={demandId:that.doc.TASK_INFO.id}
            queryDemandTracklist(p).then(resp=>{
              this.f9List =[]
              resp.forEach(item=>{
                if(item.developSchedule==null||item.developSchedule==""||item.developSchedule==undefined){

                }else{
                  this.f9List.push(item)
                }
              })
            })
      })
    },
    reopenTrack(id){
      let that = this
      this.Form99={
        id:null,
        demandId:null,
        developSchedule:null,
        trackDirections:null,
        devFinishTime:null,
        releaseTime:null,
        stalker:null,
        trackUnit:null,
        trackDepartment:null,
        trackTime:null,
        isRelease:null,
        attachment:null,
        atts:[],
      }
      queryDemandTrackById(id).then(rres=>{
        that.Form99 = rres.data
        let p = {name:that.Form99.attachment,id:that.Form99.attachmentId}
        that.Form99.atts = [p]
        that.newTracker = true
      })
      
    },
    newTrackForm(){
      
      this.Form99={
        id:null,
        demandId:null,
        developSchedule:null,
        trackDirections:null,
        devFinishTime:null,
        releaseTime:null,       
        trackTime:null,
        isRelease:null,
        attachment:null,
        atts:[],
      }
      this.newTracker = true
    },
    onSelectChange4(selectedRowKeys, selectedRows) {
      this.selectedRowKeys4 = selectedRowKeys
      this.selectedRows4 = selectedRows
    },
    onChange1(e){
      this.$emit("onChange1",e.target.checked)
    },
    onChange2(e){
      this.$emit("onChange2",e.target.checked)
    },
    onChange3(e){
      this.$emit("onChange3",e.target.checked)
    },
    handleSearch(v) {
      let that = this
      console.log(v,'v55555555');
      listUser2({
        
        user: v,
      }).then((response) => {
        console.log(response,'response');
        this.dataList = []
        this.dataList2 = []
        response.rows.map((item) =>
          that.dataList.push(
            item.user
          )
          
        )
        response.rows.map((item) =>
          that.dataList2.push(
            item
          )
          
        )
      })
    },
    setSelUser(record){
      this.Form.application_user = record.user
      this.Form.application_dept = record.dept
      this.Form.proposer_phone = record.phone
      this.Form.application_unit = record.unit
    },
    handleChange(v, options) {
      console.log(options.data.key,'options');
      this.dataList2.forEach(item=>{
        if(item.user==options.data.key){
          this.Form.application_user = item.user
          this.Form.application_dept = item.dept
          this.Form.proposer_phone = item.phone
          this.Form.application_unit = item.unit
        }
      })
      
    },
    redochange(e){
      if(e.target.value==1){
        this.Form10.opinion = '同意'
        this.Form11.opinion = '同意'
      }else{
        this.Form10.opinion = '不同意。'
        this.Form11.opinion = '不同意。'
      }
      this.$emit('rb2ndBtnCtl',e.target.value)
    },
    rnchange2(e){
      // this.rulechange = false


      if(e.target.value==1){
        this.rules2 = this.rules20
        this.Form2.opinion = '同意'
        this.Form3.opinion = '同意'
        this.Form4.opinion = '同意'
        this.Form6.opinion = '同意'
        this.Form7.opinion = '同意'
        this.Form8.opinion = '同意' 
      }else{
        this.rules2 = this.rules21
        this.Form2.opinion = '不同意。'
        this.Form3.opinion = '不同意。'
        this.Form4.opinion = '不同意。'
        this.Form6.opinion = '不同意。'
        this.Form7.opinion = '不同意。'
        this.Form8.opinion = '不同意。' 
      }
      
    
      this.$emit('rnchange2',e.target.value)
    },
    rnchange(e){
      console.error(e)
      if(e.target.value==0){
        this.rules2 = this.rules21
        this.k8s = false
        this.Form5.opinion = '不同意。'
      }else{
        this.k8s = true
        this.rules2 = this.rules20
        this.Form5.opinion = '同意'
      }
      this.$emit('rb1stBtnCtl',e.target.value)
     console.error(this.rules2)
    },
    arr2eventstr(e){
      let str = ""
      e.forEach(item=>{
        if(str==""){
          str = item
        }else{
          str = str + "," + item
        }
      })
      this.Form.functional_module = str
    },
    zdhkywChange(e){
      this.Form3.zdhkyw = e
    },
    zdhkChange(e){
      this.Form3.zdhk = e
    },
    ksldChange(e){
      this.Form4.ksld = e
    },
    scldChange(e){
      this.Form4.scld = e
    },
    zxldChange(e){
      this.Form4.zxld = e
    },
    resetForm(){
      this.eventArr=[]
      this.Form={
        fd_bh: '',//需求编号
        creation_time: '', //建单时间
        register_time: '', // 发生时间
        register_expect_time:'',//期待完成时间
        application_user:'',//申请人
        application_unit: '', //申请人单位
        application_dept: '', // 申请人部门
        work_load: '', //开发工作量
        priority:'',
        application_user_phone: '', //申请人电话
        proposer_phone: '', //手机号码'
        project: '', //所属项目
        demand_type:'',//需求类型
        functional_module:'',//功能模块
        event_effect:'',//影响度
        priority:'',//需求优先级
        event_prior:'',//优先级
        opinion: '', // 意见
        fd_bt:'',
        reason:'',
        detail:'',
        register_user: '',//登记人
        register_unit: '',//登记人单位
        register_dept: '',//登记人部门
      }
      let xd = new Date()
      if(null==this.Form.creation_time){
        this.Form.creation_time=moment().format('YYYY-MM-DD HH:mm:ss')
      }
      
      getInfo().then((res)=>{
        if(null==this.Form.register_user||''==this.Form.register_user||undefined==this.Form.register_user){
          this.Form.register_user=res.user.name
          this.Form.register_dept=res.user.sysDept.deptName
          this.Form.register_unit=res.user.companyName
        }
          
      })
    },
    setForm(fromUp){
      this.Form = fromUp
    },
    closeCurrentTab() {  
      this.$multiTab.closeCurrentPage();  
    },
    getCurrUserInfoFun(){
      getInitData('demand_business,demand_release,demand_priority,xqssxm').then(res=>{
        this.priorityArr = res.data.demand_priority
        this.businessArr = res.data.demand_business
        this.releasesArr = res.data.demand_release
        this.xqssxmList = res.data.xqssxm 
      })
      if(this.disabled == false){
        getInfo().then((res)=>{
          if(null==this.Form.register_user||''==this.Form.register_user||undefined==this.Form.register_user){
            this.Form.register_user=res.user.name
            this.Form.register_dept=res.user.sysDept.deptName
            this.Form.register_unit=res.user.companyName
          }

          if(null==this.Form2.userName||''==this.Form2.userName||undefined==this.Form2.userName){
            this.Form2.userName=res.user.name
            this.Form2.department=res.user.sysDept.deptName
            this.Form2.unit=res.user.companyName
            this.Form2.userId=res.user.id
          }else{
            if(this.Form2.userName!=res.user.name&&this.currentStep==2){
              this.Form2.userName=res.user.name
              this.Form2.department=res.user.sysDept.deptName
              this.Form2.unit=res.user.companyName
              this.Form2.userId=res.user.id
            }
          }


          if(null==this.Form3.userName||''==this.Form3.userName||undefined==this.Form3.userName){
            this.Form3.userName=res.user.name
            this.Form3.department=res.user.sysDept.deptName
            this.Form3.unit=res.user.companyName
            this.Form3.userId=res.user.id
          }else{
            if(this.Form3.userName!=res.user.name&&this.currentStep==3){
              this.Form3.userName=res.user.name
            this.Form3.department=res.user.sysDept.deptName
            this.Form3.unit=res.user.companyName
            this.Form3.userId=res.user.id
            }
          }

          if(null==this.Form4.userName||''==this.Form4.userName||undefined==this.Form4.userName){
            this.Form4.userName=res.user.name
            this.Form4.department=res.user.sysDept.deptName
            this.Form4.unit=res.user.companyName
            this.Form4.userId=res.user.id
          }else{
            if(this.Form4.userName!=res.user.name&&this.currentStep==4){
              this.Form4.userName=res.user.name
              this.Form4.department=res.user.sysDept.deptName
              this.Form4.unit=res.user.companyName
              this.Form4.userId=res.user.id
            }
          }

          if(null==this.Form5.userName||''==this.Form5.userName||undefined==this.Form5.userName){
            this.Form5.userName=res.user.name
            this.Form5.department=res.user.sysDept.deptName
            this.Form5.unit=res.user.companyName
            this.Form5.userId=res.user.id
          }else{
            if(this.Form5.userName!=res.user.name&&this.currentStep==5){
              this.Form5.userName=res.user.name
              this.Form5.department=res.user.sysDept.deptName
              this.Form5.unit=res.user.companyName
              this.Form5.userId=res.user.id
            }
          }

          if(null==this.Form6.userName||''==this.Form6.userName||undefined==this.Form6.userName){
            this.Form6.userName=res.user.name
            this.Form6.department=res.user.sysDept.deptName
            this.Form6.unit=res.user.companyName
            this.Form6.userId=res.user.id
          }else{
            if(this.Form6.userName!=res.user.name&&this.currentStep==6){
              this.Form6.userName=res.user.name
              this.Form6.department=res.user.sysDept.deptName
              this.Form6.unit=res.user.companyName
              this.Form6.userId=res.user.id
            }
          }

          if(null==this.Form7.userName||''==this.Form7.userName||undefined==this.Form7.userName){
            this.Form7.userName=res.user.name
            this.Form7.department=res.user.sysDept.deptName
            this.Form7.unit=res.user.companyName
            this.Form7.userId=res.user.id
          }else{
            if(this.Form7.userName!=res.user.name&&this.currentStep==7){
              this.Form7.userName=res.user.name
              this.Form7.department=res.user.sysDept.deptName
              this.Form7.unit=res.user.companyName
              this.Form7.userId=res.user.id
            }
          }

          if(null==this.Form8.userName||''==this.Form8.userName||undefined==this.Form8.userName){
            this.Form8.userName=res.user.name
            this.Form8.department=res.user.sysDept.deptName
            this.Form8.unit=res.user.companyName
            this.Form8.userId=res.user.id
          }else{
            if(this.Form8.userName!=res.user.name&&this.currentStep==8){
              this.Form8.userName=res.user.name
              this.Form8.department=res.user.sysDept.deptName
              this.Form8.unit=res.user.companyName
              this.Form8.userId=res.user.id
            }
          }

          if(null==this.Form9.stalker||''==this.Form9.stalker||undefined==this.Form9.stalker){
            this.Form9.stalker=res.user.name
            this.Form9.trackDepartment=res.user.sysDept.deptName
            this.Form9.trackUnit=res.user.companyName
          }else{
            if(this.Form9.stalker!=res.user.name&&this.currentStep==9){
              this.Form9.stalker=res.user.name
              this.Form9.trackDepartment=res.user.sysDept.deptName
              this.Form9.trackUnit=res.user.companyName
            }
          }
          
          if(null==this.Form10.userName||''==this.Form10.userName||undefined==this.Form10.userName){
            this.Form10.userName=res.user.name
            this.Form10.department=res.user.sysDept.deptName
            this.Form10.unit=res.user.companyName
            this.Form10.userId=res.user.id
          }else{
            if(this.Form10.userName!=res.user.name&&this.currentStep==10){
              this.Form10.userName=res.user.name
              this.Form10.department=res.user.sysDept.deptName
              this.Form10.unit=res.user.companyName
              this.Form10.userId=res.user.id
            }
          }

          if(null==this.Form11.userName||''==this.Form11.userName||undefined==this.Form11.userName){
            this.Form11.userName=res.user.name
            this.Form11.department=res.user.sysDept.deptName
            this.Form11.unit=res.user.companyName
            this.Form11.userId=res.user.id
          }else{
            if(this.Form11.userName!=res.user.name&&this.currentStep==11){
              this.Form11.userName=res.user.name
              this.Form11.department=res.user.sysDept.deptName
              this.Form11.unit=res.user.companyName
              this.Form11.userId=res.user.id
            }
          }
        })
        let xd = new Date()
        getInfo().then((res)=>{
          this.Form99.stalker=res.user.name
          this.Form99.trackDepartment=res.user.sysDept.deptName
          this.Form99.trackUnit=res.user.companyName
          this.Form99.gmtModified=moment().format('YYYY-MM-DD HH:mm:ss')
        })
        if(!this.disabled){
          if(null==this.Form.creation_time||''==this.Form.creation_time){
            this.Form.creation_time=moment().format('YYYY-MM-DD HH:mm:ss')
          }
          
        }

        if(null==this.Form2.reviewTime||''==this.Form2.reviewTime){
          this.Form2.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        if(null==this.Form3.reviewTime||''==this.Form3.reviewTime){
          this.Form3.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        if(null==this.Form4.reviewTime||''==this.Form4.reviewTime){
          this.Form4.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        if(null==this.Form5.reviewTime||''==this.Form5.reviewTime){
          this.Form5.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        if(null==this.Form6.reviewTime||''==this.Form6.reviewTime){
          this.Form6.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        if(null==this.Form7.reviewTime||''==this.Form7.reviewTime){
          this.Form7.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        if(null==this.Form8.reviewTime||''==this.Form8.reviewTime){
          this.Form8.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        if(null==this.Form9.reviewTime||''==this.Form9.reviewTime){
          this.Form9.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        if(null==this.Form10.reviewTime||''==this.Form10.reviewTime){
          this.Form10.reviewTime = moment().format('YYYY-MM-DD HH:mm:ss')
        }
        
    }
    },
    initxqdj(){
      this.getCurrUserInfoFun()

    },
    readyStep(){        
      this.$emit("readyStep")
    },
    getFrom11(){    
      let flag=false
      this.$refs.myform11.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form11
      }
    }, 
    getFrom10(){    
      let flag=false
      this.$refs.myform10.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form10
      }
    },  
    getFrom9(){    
      let flag=false
      this.$refs.myform9.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form9
      }
    }, 
    getFrom8(){    
      let flag=false
      this.$refs.myform8.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form8
      }
    },
    getFrom7(){    
      let flag=false
      this.$refs.myform7.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form7
      }
    },
    getFrom6(){    
      let flag=false
      this.$refs.myform6.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form6
      }
    },
    getFrom5(){    
      let flag=false
      this.$refs.myform5.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form5
      }
    },
    getFrom4(){    
      let flag=false
      this.$refs.myform4.validate(valid => {
        flag = valid
      })      
      if(flag){
    
        return this.Form4
      }
    },
    getFrom3(){    
      
      let flag=false
      this.$refs.myform3.validate(valid => {
        console.error(valid)
        flag = valid
      })      
      if(flag){
        let str =null
        this.Form3.zdhk.forEach(item=>{
          if(null==str){
            str=item
          }else{
            str = str +','+item
          }
        })
        this.Form3.attachmentId=str
        let str2 = null
        this.Form3.zdhkyw.forEach(item=>{
          if(null==str2){
            str2=item
          }else{
            str2 = str2 +','+item
          }
        })
        this.Form3.taskName=str2
        return this.Form3
      }
    },
    getFrom2(){    
      let flag=false
      this.$refs.myform2.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form2
      }
    },
    getFrom(){    
      let flag=false
      this.$refs.myform.validate(valid => {
        flag = valid
      })      
      if(flag){
        return this.Form
      }
    },
    valForm(){
      let flag=false
      this.$refs.myform.validate(valid => {
        flag = valid
      })      
      return flag
    },



    handleBeforeUpload2(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form2.attachment==null||this.Form2.attachment==""){
              this.Form2.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form2.attachment = this.Form2.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts2.push(p)
            this.$refs.myform2.validate()
        })
        return false
      }
    },
    handleBeforeUpload3(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form3.attachment==null||this.Form3.attachment==""){
              this.Form3.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form3.attachment = this.Form3.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts3.push(p)
            this.$refs.myform3.validate()
        })
        return false
      }
    },
    handleBeforeUpload4(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form4.attachment==null||this.Form4.attachment==""){
              this.Form4.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form4.attachment = this.Form4.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts4.push(p)
            this.$refs.myform4.validate()
        })
        return false
      }
    },
    handleBeforeUpload5(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form5.attachment==null||this.Form5.attachment==""){
              this.Form5.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form5.attachment = this.Form5.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts5.push(p)
            this.$refs.myform5.validate()
        })
        return false
      }
    },
    handleBeforeUpload6(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form6.attachment==null||this.Form6.attachment==""){
              this.Form6.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form6.attachment = this.Form6.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts6.push(p)
            this.$refs.myform6.validate()
        })
        return false
      }
    },
    handleBeforeUpload7(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form7.attachment==null||this.Form7.attachment==""){
              this.Form7.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form7.attachment = this.Form7.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts7.push(p)
            this.$refs.myform7.validate()
        })
        return false
      }
    },
    handleBeforeUpload8(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form8.attachment==null||this.Form8.attachment==""){
              this.Form8.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form8.attachment = this.Form8.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts8.push(p)
            this.$refs.myform8.validate()
        })
        return false
      }
    },
    handleBeforeUpload99(file){
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            this.Form99.attachmentId=ures.data.id
            
              this.Form99.attachment=ures.data.name
            

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            this.Form99.atts=[]
            this.Form99.atts.push(p)
            
        })
        return false
      }
    },
    handleBeforeUpload9(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form9.attachment==null||this.Form9.attachment==""){
              this.Form9.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form9.attachment = this.Form9.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts9.push(p)
            
        })
        return false
      }
    },
    handleBeforeUpload10(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form10.attachment==null||this.Form10.attachment==""){
              this.Form10.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form10.attachment = this.Form10.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts10.push(p)
            
        })
        return false
      }
    },
    handleBeforeUpload11(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form11.attachment==null||this.Form11.attachment==""){
              this.Form11.attachment=ures.data.id+'_'+ures.data.name
            }else{
              this.Form11.attachment = this.Form11.attachment +',' + ures.data.id+'_'+ures.data.name
            }

            let p={
              id:ures.data.id,
              name:ures.data.name
            }
            // if(null==this.Form2.checkAttachments){
              // this.Form2.atts = new Array()
            // }            
            
            this.atts11.push(p)
            
        })
        return false
      }
    },
    dlAttach(id,name){
      downloadAttchment2(id,name)
    },
    delCheck2(id){
      let hattr= new Array()
      for(var i=0;i<this.atts2.length;i++){
        if(this.atts2[i].id!=id){
          hattr.push(this.atts2[i])
        }
      }
      //this.atts2=hattr

      let strs = this.Form2.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form2.attachment=str
    },
    delCheck3(id){
      let hattr= new Array()
      for(var i=0;i<this.atts3.length;i++){
        if(this.atts3[i].id!=id){
          hattr.push(this.atts3[i])
        }
      }
      this.atts3=hattr

      let strs = this.Form3.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form3.attachment=str
    },
    delCheck4(id){
      let hattr= new Array()
      for(var i=0;i<this.atts4.length;i++){
        if(this.atts4[i].id!=id){
          hattr.push(this.atts4[i])
        }
      }
      //this.atts4=hattr

      let strs = this.Form4.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form4.attachment=str
    },
    delCheck5(id){
      let hattr= new Array()
      for(var i=0;i<this.atts5.length;i++){
        if(this.atts5[i].id!=id){
          hattr.push(this.atts5[i])
        }
      }
    //  this.atts5=hattr

      let strs = this.Form5.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form5.attachment=str
    },
    delCheck6(id){
      let hattr= new Array()
      for(var i=0;i<this.atts6.length;i++){
        if(this.atts6[i].id!=id){
          hattr.push(this.atts6[i])
        }
      }
    //  this.atts6=hattr

      let strs = this.Form6.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form6.attachment=str
    },
    delCheck7(id){
      let hattr= new Array()
      for(var i=0;i<this.atts7.length;i++){
        if(this.atts7[i].id!=id){
          hattr.push(this.atts7[i])
        }
      }
   //   this.atts7=hattr

      let strs = this.Form7.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form7.attachment=str
    },
    delCheck8(id){
      let hattr= new Array()
      for(var i=0;i<this.atts8.length;i++){
        if(this.atts8[i].id!=id){
          hattr.push(this.atts8[i])
        }
      }
  //    this.atts8=hattr

      let strs = this.Form8.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form8.attachment=str
    },
    delCheck99(id){
      this.Form99.attsts9=[]
      this.Form99.attachmentId=null
      this.Form99.attachment=null
    },
    delCheck9(id){
      let hattr= new Array()
      for(var i=0;i<this.atts9.length;i++){
        if(this.atts9[i].id!=id){
          hattr.push(this.atts9[i])
        }
      }
     // this.atts9=hattr

      let strs = this.Form9.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form9.attachment=str
    },
    delCheck10(id){
      let hattr= new Array()
      for(var i=0;i<this.atts10.length;i++){
        if(this.atts10[i].id!=id){
          hattr.push(this.atts10[i])
        }
      }
    //  this.atts10=hattr

      let strs = this.Form10.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form10.attachment=str
    },
    delCheck11(id){
      let hattr= new Array()
      for(var i=0;i<this.atts11.length;i++){
        if(this.atts11[i].id!=id){
          hattr.push(this.atts11[i])
        }
      }
   //   this.atts11=hattr

      let strs = this.Form11.attachment.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form11.attachment=str
    },
    handleBeforeUpload0(file) {
      if(this.fromstart==true){
        this.$message.error('请先保存基础信息');
      }else{
        const formData = new FormData()
        formData.append('file', file)
        formData.append('attachType', 'attach')
        const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
        let access_token = localStorage.getItem('access_token')
        access_token = access_token.replace(/^"|"$/g, '')
        const config = {
          headers: {
            'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
            Authorization: 'Bearer ' + access_token,
          },
        }
        axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
          }
            this.$message.success( '上传成功')
            if(this.Form.file_ids==null||this.Form.file_ids==""){
              this.Form.file_ids=ures.data.id+'_'+ures.data.name
            }else{
              this.Form.file_ids = this.Form.file_ids +',' + ures.data.id+'_'+ures.data.name
            }
            let p={
              id:ures.data.id,
              name:ures.data.name
            }
                     
            this.atts0.push(p)
            this.$refs.myform.validate()
        })
        return false
      }
    },
    delHandl0(id){
      let hattr= new Array()
      for(var i=0;i<this.atts0.length;i++){
        if(this.atts0[i].id!=id){
          hattr.push(this.atts0[i])
        }
      }
      //this.atts0=hattr

      let strs = this.Form.file_ids.split(',')
      let str="";
      for(var j=0;j<strs.length;j++){
        let ko=strs[j].split('_')
        if(ko[0]!=id){
          if(str==""){
            str=strs[j]
          }else{
            str=str+","+strs[j]
          }
        }
      }
      this.Form.file_ids=str
    },
    setFDBH(str){
      this.Form.fd_bh = str
    },
  }
  
}
</script>
<style lang="less" scoped="scoped">
.switch-container {
  /* 容器样式，如果需要的话 */
}

.switch-item {
  display: flex;
  align-items: center; /* 垂直居中 */
  margin-bottom: 10px; /* 控制开关之间的间距 */
}

.switch-label {
  margin-left: 8px; /* 控制开关和标签之间的间距 */
}

/deep/  .ant-form-item-label {
        width: 100px;
      }



//       /deep/ .ant-input[disabled] {
//     color: rgb(0 0 0);
//     background-color: #ffffff;
//     cursor: not-allowed;
//     opacity: 1;
// }




// /deep/ .ant-select-auto-complete.ant-select .ant-input[disabled] {
//     color: rgba(0, 0, 0, 1) !important;
//     cursor: not-allowed;
//     opacity: 1;
//     background-color: transparent;
// }

/deep/ .ant-select-auto-complete.ant-select {
    color: rgba(0, 0, 0, 1);
    cursor: not-allowed;
    opacity: 1;
    background-color: transparent;
}



/deep/ .ant-select-auto-complete {
    color: rgba(0, 0, 0, 1) !important;
    cursor: not-allowed;
    opacity: 1;
    background-color: transparent;
}



/deep/ .ant-cascader-picker-disabled {
  color: rgba(0, 0, 0, 1);
  background: #ffffff;
  cursor: not-allowed;
}


</style>
