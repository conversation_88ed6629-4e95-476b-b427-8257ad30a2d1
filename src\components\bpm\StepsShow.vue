<template>
  <div>
    <a-card :bordered="false" style="margin-bottom: 10px;padding: 24px;padding-bottom:0px;height:170px;overflow-x: scroll;">
      <a-steps :current="current" class="table-page-search-wrapper" type="navigation"  >
        <a-step v-for="(item,index) in Steps" style="overflow:visible;height:120px;min-width:250px;max-width:300px;text-align:left;"  >
          <template slot="icon" v-if="item.sendTime!='' && item.sendTime!=null">
            <a-icon   class="circle-wrapper1">  
              <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="100%" height="100%">  
             <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z" fill="green" />  
            </svg> 
            </a-icon>
          </template>
          <template slot="icon" v-if="item.sendTime=='' || item.sendTime==null">
            <a-icon type="loading" class="circle-wrapper"></a-icon>
          </template>
          <template slot="title"  >
            <p class="sqlc-font" style="color:rgba(0,0,0,.65);height:38px;margin-bottom:0px;">{{item.stepName}} </p>
          </template>
          <span  slot="description" style="font-size: 14px;">
            <p style="margin-bottom:0px;width:250px;">处理人:  {{item.userName}}</p>
            <p style="margin-bottom:0px;width:250px;">开始时间： {{item.receiveTime}}  </p>
            <p style="margin-bottom:0px;width:250px;">结束时间： {{ item.sendTime }}</p>
            <p style="margin-bottom:0px;width:250px;color:rgba(0,0,0,.65)">用时： {{ item.descName }}</p>
            
          </span>
        </a-step>
        
      </a-steps>
    </a-card>
  </div>
</template>
<script>
import {
  startflowPost,
  saveformdataPost,
  uploadPost,
  getDocInfo,
  operationGotoNextStep,
  getStepChartGet,
  targetUser,
  operationGetnowscriptpath,
  stepInfoGetStepInfoById,
} from '@/api/system/ywrygl/operation'

export default {
  inject: ['reload'],
  name: 'StepsShow',
  components: {
  },
  props: {
    stepInfoIds: {
      type: String
    },
    stepArr:{
      type:Array
    },
    number:{
      type:Number
    }
   
  },
  data() {
    return {
      lastDate:null,
      current: 0,
      Steps:[],
      stepInfoId: this.stepInfoIds,
    }
  },
  created() {
    this.Steps = this.stepArr
    this.current = this.number
  
  },
  methods: {
    
    emptySteps(){
      this.Steps = []
    },
    getStepChartGetFun() {
      if(''!=this.stepInfoId){
        getStepChartGet({
          parentDocId: this.stepInfoId,
        }).then((response) => {      
          this.Steps = response
          for(var i=0;i<this.Steps.length;i++){
           
           if(this.Steps[i].state>=1){
            
           
              this.current = i
                    
           }
          }
        })
      }
    },
  }
  
}
</script>
<style lang="less" scoped="scoped">
.switch-container {
  /* 容器样式，如果需要的话 */
}

.sqlc-font {
  font-size: 14px;
  display: flex;
  align-items: center;
}
.ant-steps-item-title::after {
    position: absolute;
    top: 16px;
    left: 100%;
    display: block;
    width: 9999px;
    height: 1px;
    background: green;
    content: '';
}
.circle-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 35px; /* 根据需要调整大小 */
  height: 35px; /* 根据需要调整大小 */
  border: 2px solid #c1e818;
  border-radius: 50%;
  background-color: #c1e818;
  font-size: 39px; /* 这个样式也是用于图标的，同上 */
}
.circle-wrapper1 {  
  border: 1px solid rgba(0, 0, 0, 0.25);  
  border-radius: 50%; /* 使用50%来确保是完美的圆形 */  
  width: 35px;  
  height: 35px;  
  background-color: #c1e818;  
  display: flex;  
  align-items: center;  
  justify-content: center; /* 确保SVG在中心 */  
}

::v-deep .ant-steps-item-finish > .ant-steps-item-container > .ant-steps-item-content > .ant-steps-item-title::after {
    background-color: #4CAF50;
}
::v-deep  .ant-steps-item {
  padding:0px;
}
::v-deep .table-page-search-wrapper {
  padding:0px;
  padding-left: 16px;
}
::v-deep .ant-steps-horizontal:not(.ant-steps-label-vertical) .ant-steps-item {
  margin-right:32px;
}
.switch-item {
  display: flex;
  align-items: center; /* 垂直居中 */
  margin-bottom: 10px; /* 控制开关之间的间距 */
}

.switch-label {
  margin-left: 8px; /* 控制开关和标签之间的间距 */
}
</style>
