import request from '@/utils/request'

// 查询故障信息表列表 
//  http://localhost:8183/system/itsmTrouble/pendinglist?pageNum=1&pageSize=10
// export function listItsmTroubleOrder(data) {
//   return request({
//     url: '/system/itsmTrouble/pendinglist?pageNum=' + data.pageNum + '&pageSize=' + data.pageSize + '&selectEndTime=' + data.selectEndTime + '&selectStartTime=' + data.selectStartTime,
//     method: 'get',
//   })
// }
// 查询故障信息表列表 
export function listItsmTroubleOrder(query) {
  return request({
    url: '/system/itsmTrouble/pendinglist',
    method: 'get',
    params: query
  })
}
export function listItsmTroubleOrder2(query) {
  return request({
    url: '/system/itsmTrouble/list',
    method: 'get',
    params: query
  })
}

// 查询故障信息表详细
export function getItsmTroubleOrder(id) {
  return request({
    url: '/system/itsmTroubleOrder/' + id,
    method: 'get'
  })
}

// 新增故障信息表
export function addItsmTroubleOrder(data) {
  return request({
    url: '/system/itsmTroubleOrder',
    method: 'post',
    data: data
  })
}

// 修改故障信息表
export function updateItsmTroubleOrder(data) {
  return request({
    url: '/system/itsmTroubleOrder',
    method: 'put',
    data: data
  })
}

// 删除故障信息表
export function delItsmTroubleOrder(id) {
  return request({
    url: '/system/itsmTroubleOrder/' + id,
    method: 'delete'
  })
}


// 导出故障信息表
export function exportItsmTroubleOrder(query) {
  return request({
    url: '/system/itsmTrouble/export',
    method: 'get',
    params: query
  })
}

// 故障状态统计
export function itsmTroubleGzzttj(data) {
  return request({
    url: '/system/itsmTrouble/gzzttj',
    method: 'post',
    data: data
  })
}
// 故障类型统计 
export function itsmTroubleGzlxtj(data) {
  return request({
    url: '/system/itsmTrouble/gzlxtj',
    method: 'post',
    data: data
  })
}
