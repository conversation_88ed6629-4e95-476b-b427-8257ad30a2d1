import request from '@/utils/request'

// 查询需求登记流程列表
export function listItsmDemandOrderbak (data) {
  return request({
    url: '/system/itsmDemand/pendinglist',
    method: 'post',
   data:data 
  })
}

// 查询事件工单信息表列表 raul
export function listItsmDemandOrder (query) {
  return request({
    url: '/system/itsmDemand/pendinglist',
    method: 'get',
    params: query
  })
}

export function listItsmDemandOrder2 (query) {
  return request({
    url: '/system/itsmDemand/list',
    method: 'get',
    params: query
  })
}
// //查询需求待办
// export function listItsmDemandList (data) {
//   return request({
//     url: '/system/itsmDemand/pendinglist',
//     method: 'post',
//     data:data 
//   })
// }
//状态查询
// http://localhost:8183/system/itsmDemand/xqzttj
export function listItsmDemandList (data) {
  return request({
    url: '/system/itsmDemand/xqzttj',
    method: 'post',
    data:data 
  })
}
// 查询需求登记流程详细
export function getItsmDemandOrder (id) {
  return request({
    url: '/system/itsmDemand/' + id,
    method: 'get'
  })
}

// 新增需求登记流程
export function addItsmDemandOrder (data) {
  return request({
    url: '/system/itsmDemand',
    method: 'post',
    data: data
  })
}

// 修改需求登记流程
export function updateItsmDemandOrder (data) {
  return request({
    url: '/system/itsmDemand',
    method: 'put',
    data: data
  })
}

// 删除需求登记流程
export function delItsmDemandOrder (id) {
  return request({
    url: '/system/itsmDemand/' + id,
    method: 'delete'
  })
}


// 导出需求登记流程
export function exportItsmDemandOrder (query) {
  return request({
    url: '/system/itsmDemand/export',
    method: 'get',
    params: query
  })
}

// 导出需求登记流程  （自定义列导出）
export function exportItsmDemandOrderCustomize (query) {
  return request({
    url: '/system/itsmDemand/exportcustomize',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDemand/getInitData/' + dictTypes,
    method: 'get'
  })
}
