import request from '@/utils/request'

// 查询事件工单信息表列表
export function listItsmEventOrder (query) {
  return request({
    url: '/system/itsmEvent/list',
    method: 'get',
    params: query
  })
}
// 查询待办列表
export function listItsmEventPending (query) {
  return request({
    url: '/system/itsmEvent/pendinglist',
    method: 'get',
    params: query,
   
  })
}

export function listItsmEventPending2 (query) {
  return request({
    url: '/system/itsmEvent/list',
    method: 'get',
    params: query,
   
  })
}
// //工单处理率 统计
// export function gdcllItsmEventOrder (data) {
//   return request({
//     url: '/system/itsmEvent/gdclltj',
//     method: 'post',
//     data: data
//   })
// }

//时间管理-统计分析-工单处理率 (新接口)
export function gdcllItsmEventOrder (data) {
  return request({
    url: '/system/itsmEvent/static/eventHandleRate',
    method: 'post',
    data: data
  })
}

export function gdcllItsmEventOrder4excel (data) {
  return request({
    url: '/system/itsmEvent/gdclltj/excel',
    method: 'post',
    data: data
  })
}

// 事件统计- 工单处理率 -导出  (新接口)
export function eventHandleRateExport (data) {
  return request({
    url: '/system/itsmEvent/static/eventHandleRate/export',
    method: 'post',
    data: data
  })
}

export function gdclrclltj (data) {
  return request({
    url: '/system/itsmEvent/gdclrclltj',
    method: 'post',
    data: data
  })
}

export function gdclrclltj4excel (data) {
  return request({
    url: '/system/itsmEvent/gdclrclltj/excel',
    method: 'post',
    data: data
  })
}

export function yxsjjeqk4excel (data) {
  return request({
    url: '/system/itsmEvent/yxsjjeqk/excel',
    method: 'post',
    data: data
  })
}

export function yxsjjeqk4excel2 (data) {
  return request({
    url: '/system/itsmEvent/static/eventSelfHandle/export',
    method: 'post',
    data: data
  })
}

// // 事件统计/一线事件解决情况
// export function yxsjjeqkItsmEventOrder (data) {
//   return request({
//     url: '/system/itsmEvent/yxsjjeqk',
//     method: 'post',
//     data: data
//   })
// }

// 事件统计/一线事件解决情况 （新接口）
export function yxsjjeqkItsmEventOrder (data) {
  return request({
    url: '/system/itsmEvent/static/eventSelfHandle',
    method: 'post',
    data: data
  })
}


export function fwjxhkcx (data) {
  return request({
    url: '/system/itsmqualityassessdatalink/fwjxkhtjQuery',
    method: 'post',
    data: data
  })
}

export function exportFwjxkhtjQuery (data) {
  return request({
    url: '/system/itsmqualityassessdatalink/exportFwjxkhtjQuery',
    method: 'post',
    data: data
  })
}


// 事件统计/工单TOPN 环比 同比 数量
// http://localhost:8183/system/itsmEvent/ywflqktjweek post
// export function ywflqktjweekItsmEventOrder (data) {
//   return request({
//     url: '/system/itsmEvent/ywflqktjweek',
//     method: 'post',
//     data: data
//   })
// }


// 事件统计/工单TOPN （新接口）

export function ywflqktjweekItsmEventOrder (data) {
  return request({
    url: '/system/itsmEvent/static/eventTOPN',
    method: 'post',
    data: data
  })
}
// 事件统计/运维分类统计情况

export function ywflqktjItsmEventOrder (data) {
  return request({
    url: '/system/itsmEvent/ywflqktj',
    method: 'post',
    data: data
  })
}
// // 事件统计/运维分类统计情况按周
// export function ywflqktjweekjItsmEventOrder (data) {
//   return request({
//     url: '/system/itsmEvent/ywflqktjweek',
//     method: 'post',
//     data: data
//   })
// }

// 事件统计/运维分类统计情况按周  (新接口)
export function ywflqktjweekjItsmEventOrder (data) {
  return request({
    url: '/system/itsmEvent/static/eventObjectStatic',
    method: 'post',
    data: data
  })
}



// 事件统计/服务指标完成情况（月）
// http://localhost:8183/system/itsmEvent/fwzbtjmonth
// get
// 事件统计/服务指标完成情况（周）
// http://localhost:8183/system/itsmEvent/fwzbtjweek
// get
// export function listItsmfwzbtjweek (query) {
//   return request({
//     url: '/system/itsmEvent/fwzbtjweek',
//     method: 'get',
//     params: query,
   
//   })
// }
// 事件管理-事件统计-软件运维服务指标完成情况
export function listItsmfwzbtjweek (query) {
  return request({
    url: '/system/itsmEvent/statis/getSoftDevOpsSlaConditions',
    method: 'post',
    data: query
   
  })
}

export function fwzbtjweekexcel (query) {
  return request({
    url: '/system/itsmEvent/fwzbtjweek/excel',
    method: 'get',
    params: query,
   
  })
}


// 查询事件工单信息表详细
export function getItsmEventOrder (fid) {
  return request({
    url: '/system/itsmEvent/' + fid,
    method: 'get'
  })
}

// 新增事件工单信息表
export function addItsmEventOrder (data) {
  return request({
    url: '/system/itsmEvent',
    method: 'post',
    data: data
  })
}

// 修改事件工单信息表
export function updateItsmEventOrder (data) {
  return request({
    url: '/system/itsmEvent',
    method: 'put',
    data: data
  })
}

// 删除事件工单信息表
export function delItsmEventOrder (fid) {
  return request({
    url: '/system/itsmEvent/' + fid,
    method: 'delete'
  })
}


// 导出事件工单信息表
export function exportItsmEventOrder (query) {
  return request({
    url: '/system/itsmEvent/export',
    method: 'get',
    params: query
  })
}

export function exportItsmWorkOrder2 (query) {
  return request({
    url: '/system/itsmOperationOrder/list/excel',
    method: 'get',
    params: query
  })
}

export function export4PendingItsmEventOrder (query) {
  return request({
    url: '/system/itsmEvent/export4Pending',
    method: 'get',
    params: query
  })
}

// // 获取初始化数据
// export function getInitData (dictTypes) {
//   return request({
//     url: '/system/itsmEventOrder/getInitData/' + dictTypes,
//     method: 'get'
//   })
// }
