<template>
  <div style="width: 100%;">
    <a-input-search  v-model="selUser" style="width: 100%;" @search="handleSearch" :disabled="disabled" @change="inputname"></a-input-search>
    <div class="myshine-div" v-if="tableshow" >
      <div class="myshine-div2"  >
        <div class="myshine-div3"  >
          <div><a-button @click="tableshow=false"  >关闭</a-button></div> 
        <a-table :columns="columns" :data-source="dataList2" :showHeader="false"  class="myshine-table" :scroll="{x:true,y:true}"  >
        <span slot="ldx" slot-scope="text, record">
          <a-button @click="selRow(record)" >选择</a-button>
        </span>
        </a-table>
        </div>
      </div>
    </div>
    
  </div>
</template>
<script>
import { listUser3 } from '@/api/system/user'
import AdvanceTable from '@/components/pt/table/AdvanceTable'
export default {
  name: 'LDXK',
  components: {
    AdvanceTable,
  },
  props: {
    complainUser: {
      type: String
    },
    disabled:{
      type:Boolean
    }
  },
  
  
  data() {
    
    return {
      selUser:null,
      tableshow:false,
      dataList2:[],
      dataList:[],
      columns:[
        {
          show: true,
          title: '姓名',
          dataIndex: 'user',
          align: 'center'
        },
        {
          show: true,
          title: '部门',
          dataIndex: 'dept',
          align: 'center'
        },
        {
          show: true,
          title: '单位',
          dataIndex: 'unit',
          align: 'center'
        },
        {
          show: true,
          title: '电话',
          dataIndex: 'phone',
          align: 'center'
        },
        {
          show: true,
          title: '操作',
          dataIndex: 'id',
          align: 'center',
          scopedSlots: { customRender: 'ldx' }
        },
      ],
    }
    
  },
  activated(){
    console.error('88888888888888888')
    console.error(this.complainUser)
    this.selUser = this.complainUser
  },
  created() {
    console.error('99999999999999999')
    console.error(this.complainUser)
    this.selUser = this.complainUser
  },
  methods: {
    inputname(e){
      let r= {
        user: this.selUser,
        dept:'',
        phone:'',
        unit:'',
      }
      this.$emit('setSelUser',r)
    },
    settitle(str){
      this.selUser = str
    },
    selRow(record){
      this.tableshow = false
      this.selUser = record.user
      this.$emit('setSelUser',record)
    },
    handleSearch(v) {
      let that = this
      listUser3({
        
        user: v,
      }).then((response) => {
        this.tableshow = true
        this.dataList = []
        this.dataList2 = []
        this.dataList = response.data
        this.dataList2 = response.data
  
        
      })
    },
    handleChange(v, options) {
      this.dataList2.forEach(item=>{
        if(item.user==options.data.key){
          this.Form.complain_user = item.user
          this.Form.register_user_phone = item.phone
        }
      })
      
    },
  }
  
}

</script>
<style lang="less" scoped="scoped">
.switch-container {
  /* 容器样式，如果需要的话 */
}

.switch-item {
  display: flex;
  align-items: center; /* 垂直居中 */
  margin-bottom: 10px; /* 控制开关之间的间距 */
}

.switch-label {
  margin-left: 8px; /* 控制开关和标签之间的间距 */
}
/deep/  .ant-form-item-label {
  width: 100px;
}

@keyframes myshine {
  0%{ opacity: 0;}
  50%{ opacity: 1;}
  100%{ opacity: 0;}
}
.myshine-div{
  position: absolute;
  top:50px;
  left: 0px;
  z-index: 999;
  padding:1px;
  overflow: auto;
  background-image: conic-gradient(from var(--border-gradient-angle) at 50% 50%, transparent, #4F5EBB 14%, transparent 17%);
  animation: buttonBorderSpin 9s linear infinite 0ms;

}
.myshine-div2{
  margin: 1px;
  background-image: conic-gradient(from var(--border-gradient-angle) at 50% 50%, transparent, #DD90D2 14%, transparent 17%);
  animation: buttonBorderSpin 9s linear infinite 500ms;
  overflow: auto;
}
.myshine-div3{
  margin: 1px;
  background-image: conic-gradient(from var(--border-gradient-angle) at 50% 50%, transparent, #54c4c4 14%, transparent 17%);
  animation: buttonBorderSpin 9s linear infinite 1000ms;
  overflow: auto;
}
.myshine-table {

  margin: 1px;
  width: 500px;
  height: auto;
  max-height: 400px;
  overflow: auto;
  background-color: #A6AEE8;
  border-color: #F07E48;
}

@keyframes buttonBorderSpin {
  0% {
    --border-gradient-angle: 0turn;
  }

  100% {
    --border-gradient-angle: 1turn;
  }
}

@property --border-gradient-angle {
  syntax: "<angle>";
  inherits: true;
  initial-value: 0turn;
}
 
</style>