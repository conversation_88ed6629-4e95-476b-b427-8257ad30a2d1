import request from '@/utils/request'

// 查询硬件平台账号权限申请单列表
export function listUeYjptzhqxsqd (query) {
  return request({
    url: '/system/ueYjptzhqxsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function querylistUeYjptzhqxsqd (query) {
  return request({
    url: '/system/ueYjptzhqxsqd/list',
    method: 'get',
    params: query
  })
}

// 查询硬件平台账号权限申请单详细
export function getUeYjptzhqxsqd (id) {
  return request({
    url: '/system/ueYjptzhqxsqd/' + id,
    method: 'get'
  })
}

// 新增硬件平台账号权限申请单
export function addUeYjptzhqxsqd (data) {
  return request({
    url: '/system/ueYjptzhqxsqd',
    method: 'post',
    data: data
  })
}

// 修改硬件平台账号权限申请单
export function updateUeYjptzhqxsqd (data) {
  return request({
    url: '/system/ueYjptzhqxsqd',
    method: 'put',
    data: data
  })
}

// 删除硬件平台账号权限申请单
export function delUeYjptzhqxsqd (id) {
  return request({
    url: '/system/ueYjptzhqxsqd/' + id,
    method: 'delete'
  })
}

// 导出硬件平台账号权限申请单
export function exportUeYjptzhqxsqd (query) {
  return request({
    url: '/system/ueYjptzhqxsqd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/ueYjptzhqxsqd/getInitData/' + dictTypes,
    method: 'get'
  })
}
