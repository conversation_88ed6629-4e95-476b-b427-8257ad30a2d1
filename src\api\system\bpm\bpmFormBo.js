import request from '@/utils/request'

// 查询业务表单列表
export function listBpmFormBo (query) {
  return request({
    url: '/system/bpmFormBo/list',
    method: 'get',
    params: query
  })
}

// 查询业务表单详细
export function getBpmFormBo (id) {
  return request({
    url: '/system/bpmFormBo/' + id,
    method: 'get'
  })
}

// 新增业务表单
export function addBpmFormBo (data) {
  return request({
    url: '/system/bpmFormBo',
    method: 'post',
    data: data
  })
}

// 修改业务表单
export function updateBpmFormBo (data) {
  return request({
    url: '/system/bpmFormBo',
    method: 'put',
    data: data
  })
}

// 删除业务表单
export function delBpmFormBo (id) {
  return request({
    url: '/system/bpmFormBo/' + id,
    method: 'delete'
  })
}

// 查询最大编号
export function findMaxSort () {
  return request({
    url: '/system/bpmFormBo/findMaxSort',
    method: 'get'
  })
}

// 导出业务表单
export function exportBpmFormBo (query) {
  return request({
    url: '/system/bpmFormBo/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmFormBo/getInitData/' + dictTypes,
    method: 'get'
  })
}
