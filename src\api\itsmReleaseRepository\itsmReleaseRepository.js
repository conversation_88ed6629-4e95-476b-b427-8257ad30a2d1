import request from '@/utils/request'

// 查询发布-发布库列表
export function listItsmReleaseRepository (query) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository/list',
    method: 'get',
    params: query
  })
}

// 查询发布-发布库详细
export function getItsmReleaseRepository (id) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository/' + id,
    method: 'get'
  })
}

// 新增发布-发布库
export function addItsmReleaseRepository (data) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository',
    method: 'post',
    data: data
  })
}

export function makeRelease (data) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository/makeRelease',
    method: 'post',
    data: data
  })
}

// 修改发布-发布库
export function updateItsmReleaseRepository (data) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository',
    method: 'put',
    data: data
  })
}

// 删除发布-发布库
export function delItsmReleaseRepository (id) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository/' + id,
    method: 'delete'
  })
}


// 导出发布-发布库
export function exportItsmReleaseRepository (query) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/itsmReleaseRepository/itsmReleaseRepository/getInitData/' + dictTypes,
    method: 'get'
  })
}
