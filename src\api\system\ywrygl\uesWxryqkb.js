import request from '@/utils/request'

// 查询外协人员情况表列表
export function listUesWxryqkb (query) {
  return request({
    url: '/system/uesWxryqkb/list',
    method: 'get',
    params: query
  })
}

// 查询外协人员情况表详细
export function getUesWxryqkb (id) {
  return request({
    url: '/system/uesWxryqkb/' + id,
    method: 'get'
  })
}

// 新增外协人员情况表
export function addUesWxryqkb (data) {
  return request({
    url: '/system/uesWxryqkb',
    method: 'post',
    data: data
  })
}

// 修改外协人员情况表
export function updateUesWxryqkb (data) {
  return request({
    url: '/system/uesWxryqkb',
    method: 'put',
    data: data
  })
}

// 删除外协人员情况表
export function delUesWxryqkb (id) {
  return request({
    url: '/system/uesWxryqkb/' + id,
    method: 'delete'
  })
}

// 导出外协人员情况表
export function exportUesWxryqkb (query) {
  return request({
    url: '/system/uesWxryqkb/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesWxryqkb/getInitData/' + dictTypes,
    method: 'get'
  })
}

export function getRoleUser (roleKey) {
  return request({
    url: '/system/user/userListByRoleKeys/' + roleKey,
    method: 'get'
  })
}

export function listYwrybgsqd (query) {
  return request({
    url: '/system/uesubYwrybgsqd/list',
    method: 'get',
    params: query
  })
}