import request from '@/utils/request'

export function getSteps(params) {
  return request({
    url: 'api/stepInfo',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/stepInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/stepInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/stepInfo',
    method: 'put',
    data
  })
}

export default { add, edit, del }
