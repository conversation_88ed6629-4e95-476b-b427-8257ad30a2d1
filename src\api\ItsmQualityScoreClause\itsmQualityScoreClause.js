import request from '@/utils/request'



// 查询服务绩效考核/评分细则/详细条款列表
export function listItsmQualityScore4select () {
  return request({
    url: '/system/itsmqualityscore/list4select',
    method: 'get'
  })
}

// 查询服务绩效考核/评分细则/详细条款列表
export function listItsmQualityScoreClause (query) {
  return request({
    url: '/system/itsmqualityscoreclause/list',
    method: 'get',
    params: query
  })
}

// 查询服务绩效考核/评分细则/详细条款详细
export function getItsmQualityScoreClause (id) {
  return request({
    url: '/system/itsmqualityscoreclause/' + id,
    method: 'get'
  })
}

// 新增服务绩效考核/评分细则/详细条款
export function addItsmQualityScoreClause (data) {
  return request({
    url: '/system/itsmqualityscoreclause',
    method: 'post',
    data: data
  })
}

// 修改服务绩效考核/评分细则/详细条款
export function updateItsmQualityScoreClause (data) {
  return request({
    url: '/system/itsmqualityscoreclause',
    method: 'put',
    data: data
  })
}

// 删除服务绩效考核/评分细则/详细条款
export function delItsmQualityScoreClause (id) {
  return request({
    url: '/system/itsmqualityscoreclause/' + id,
    method: 'delete'
  })
}


// 导出服务绩效考核/评分细则/详细条款
export function exportItsmQualityScoreClause (query) {
  return request({
    url: '/system/itsmqualityscoreclause/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmqualityscoreclause/getInitData/' + dictTypes,
    method: 'get'
  })
}

export function gtDict () {
  return request({
    url: '/system/itsmqualityscore/gtDict',
    method: 'get'
  })
}

export function getYEtree () {
  return request({
    url: '/system/itsmqualityscoreclause/getYEtree',
    method: 'get'
  })
}

export function getYEtree2 () {
  return request({
    url: '/system/itsmqualityscoreclause/getYEtree2',
    method: 'get'
  })
}


export function updateItsmContractphas (data) {
  return request({
    url: '/system/itsmqualitycontractphase',
    method: 'post',
    data: data
  })
}

export function listContractphas (query) {
  return request({
    url: '/system/itsmqualitycontractphase/list',
    method: 'get',
    params: query
  })
}

export function delContractphas (id) {
  return request({
    url: '/system/itsmqualitycontractphase/'+id,
    method: 'delete'
  })
}

export function listContractphas2 (query) {
  return request({
    url: '/system/itsmqualitycontractphase/list2',
    method: 'get',
    params: query
  })
}


export function listScoreDetail (query) {
  return request({
    url: '/system/itsmqualityscorecarddetail/list2',
    method: 'get',
    params: query
  })
}


export function updateItsmScorecard (data) {
  return request({
    url: '/system/itsmqualityscorecard',
    method: 'post',
    data: data
  })
}

export function updateItsmScorecardDetail (data) {
  return request({
    url: '/system/itsmqualityscorecarddetail',
    method: 'post',
    data: data
  })
}


export function listScore (query) {
  return request({
    url: '/system/itsmqualityscorecard/list',
    method: 'get',
    params: query
  })
}


export function getScorecard (id) {
  return request({
    url: '/system/itsmqualityscorecarddetail/getScorecard?id='+id,
    method: 'get',
  })
}

export function getscorecarddetail (id) {
  return request({
    url: '/system/itsmqualityscorecarddetail/list3?id='+id,
    method: 'get',
  })
}


export function downloadCARD (id) {
  return request({
    url: '/system/itsmqualityscorecard/downloadCard?id='+id,
    method: 'get',
  })
}