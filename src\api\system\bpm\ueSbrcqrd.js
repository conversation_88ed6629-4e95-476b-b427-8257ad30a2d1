import request from '@/utils/request'

// 查询设备入场确认单列表
export function listUeSbrcqrd (query) {
  return request({
    url: '/system/ueSbrcqrd/list',
    method: 'get',
    params: query
  })
}

// 查询设备入场确认单详细
export function getUeSbrcqrd (id) {
  return request({
    url: '/system/ueSbrcqrd/' + id,
    method: 'get'
  })
}

// 新增设备入场确认单
export function addUeSbrcqrd (data) {
  return request({
    url: '/system/ueSbrcqrd',
    method: 'post',
    data: data
  })
}

// 修改设备入场确认单
export function updateUeSbrcqrd (data) {
  return request({
    url: '/system/ueSbrcqrd',
    method: 'put',
    data: data
  })
}

// 删除设备入场确认单
export function delUeSbrcqrd (id) {
  return request({
    url: '/system/ueSbrcqrd/' + id,
    method: 'delete'
  })
}

// 导出设备入场确认单
export function exportUeSbrcqrd (query) {
  return request({
    url: '/system/ueSbrcqrd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/ueSbrcqrd/getInitData/' + dictTypes,
    method: 'get'
  })
}
