import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/makeCopyGroup',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/makeCopyGroup/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/makeCopyGroup',
    method: 'put',
    data
  })
}

export function getById(id) {
  return request({
    url: 'api/queryById?id=' + id,
    method: 'get'
  })
}

export function list() {
  return request({
    url: 'api/makeCopyGroup/selectGroup',
    method: 'get'
  })
}

export function listGroup() {
  return request({
    url: 'api/makeCopyGroup/selectGroupArr',
    method: 'get'
  })
}

export function listTree() {
  return request({
    url: 'api/makeCopyGroup/selectGroupTree',
    method: 'get'
  })
}

export default { add, edit, del, list, listGroup, listTree }
