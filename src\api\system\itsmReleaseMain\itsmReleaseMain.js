import request from '@/utils/request'

// 查询发布待办列表
export function listItsmReleaseMain (query) {
  return request({
    url: '/releaseMange/main/list',
    method: 'get',
    params: query
  })
}

export function listItsmReleaseMain2 (query) {
  return request({
    url: '/releaseMange/main/list2',
    method: 'get',
    params: query
  })
}

export function listItsmReleaseMain3 (query) {
  return request({
    url: '/releaseMange/main/list3',
    method: 'get',
    params: query
  })
}


// 查询发布待办详细
export function getItsmReleaseMain (id) {
  return request({
    url: '/releaseMange/main/' + id,
    method: 'get'
  })
}

// 新增发布待办
export function addItsmReleaseMain (data) {
  return request({
    url: '/releaseMange/main/',
    method: 'post',
    data: data
  })
}

// 修改发布待办
export function updateItsmReleaseMain (data) {
  return request({
    url: '/releaseMange/main/',
    method: 'put',
    data: data
  })
}

// 删除发布待办
export function delItsmReleaseMain (id) {
  return request({
    url: '/releaseMange/main/' + id,
    method: 'delete'
  })
}


// 导出发布待办
export function exportItsmReleaseMain (query) {
  return request({
    url: '/releaseMange/main//export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/releaseMange/main/getInitData/' + dictTypes,
    method: 'get'
  })
}

export function saveAttach (data) {
  return request({
    url: '/releaseMange/main/saveAttach',
    method: 'post',
    data: data
  })
}


export function saveEnv (data) {
  return request({
    url: '/releaseMange/main/saveEnv',
    method: 'post',
    data: data
  })
}

export function saveEnv2 (data) {
  return request({
    url: '/releaseMange/main/saveEnv2',
    method: 'post',
    data: data
  })
}

export function getEnvs (id) {
  return request({
    url: '/releaseMange/main/getEnvs?id=' + id,
    method: 'get'
  })
}

export function getEnvs2 (id) {
  return request({
    url: '/releaseMange/main/getEnvs2?id=' + id,
    method: 'get'
  })
}

export function delEnvs (data) {
  return request({
    url: '/releaseMange/main/delEnvs',
    method: 'post',
    data: data
  })
}

export function delEnvs2 (data) {
  return request({
    url: '/releaseMange/main/delEnvs2',
    method: 'post',
    data: data
  })
}

export function saveList (data) {
  return request({
    url: '/releaseMange/main/saveList',
    method: 'post',
    data: data
  })
}

export function getLists (id) {
  return request({
    url: '/releaseMange/main/getLists?id=' + id,
    method: 'get'
  })
}

export function delLists (data) {
  return request({
    url: '/releaseMange/main/delLists',
    method: 'post',
    data: data
  })
}


export function updateTask (data) {
  return request({
    url: '/releaseMange/main/updateTask' ,
    method: 'post',
    data: data
  })
}


export function getTaskList (id) {
  return request({
    url: '/releaseMange/main/getTaskList?id=' + id,
    method: 'get'
  })
}

export function listNextUsers (step,type) {
  return request({
    url: '/releaseMange/main/listNextUsers?step=' + step+"&type="+type,
    method: 'get'
  })
}

export function next (data) {
  return request({
    url: '/releaseMange/main/next' ,
    method: 'post',
    data: data
  })
}

export function overStep (id) {
  return request({
    url: '/releaseMange/main/overStep?id=' + id,
    method: 'get'
  })
}

export function goback (id) {
  return request({
    url: '/releaseMange/main/goback?id=' + id,
    method: 'get'
  })
}

export function setPass (id,content) {
  return request({
    url: '/releaseMange/main/setPass?id=' + id + '&content='+content,
    method: 'get'
  })
}

export function setUnPass (id,content) {
  return request({
    url: '/releaseMange/main/setUnPass?id=' + id+ '&content='+content,
    method: 'get'
  })
}

export function listValItsmReleaseMain (query) {
  return request({
    url: '/releaseMange/main/listVal',
    method: 'get',
    params: query
  })
}

export function listValItsmReleaseMain2 (query) {
  return request({
    url: '/releaseMange/main/listVal2',
    method: 'get',
    params: query
  })
}

export function listValItsmReleaseMain3 (query) {
  return request({
    url: '/releaseMange/main/listVal3',
    method: 'get',
    params: query
  })
}

export function listValItsmReleaseMain4 (query) {
  return request({
    url: '/releaseMange/main/listVal4',
    method: 'get',
    params: query
  })
}

export function setUnPassAll (query) {
  return request({
    url: '/releaseMange/main/setUnPassAll',
    method: 'get',
    params: query
  })
}

export function setPassAll (query) {
  return request({
    url: '/releaseMange/main/setPassAll',
    method: 'get',
    params: query
  })
}

export function listCheckItsmReleaseMain (query) {
  return request({
    url: '/releaseMange/main/listCheck',
    method: 'get',
    params: query
  })
}

export function listCheckItsmReleaseMain2 (query) {
  return request({
    url: '/releaseMange/main/listCheck2',
    method: 'get',
    params: query
  })
}


export function setUnPassCheckAll (query) {
  return request({
    url: '/releaseMange/main/setUnPassCheckAll',
    method: 'get',
    params: query
  })
}

export function setPassCheckAll (query) {
  return request({
    url: '/releaseMange/main/setPassCheckAll',
    method: 'get',
    params: query
  })
}

export function setPassCheck (id,content) {
  return request({
    url: '/releaseMange/main/setPassCheck?id=' + id+'&content='+content,
    method: 'get'
  })
}

export function setUnPassCheck (id,content) {
  return request({
    url: '/releaseMange/main/setUnPassCheck?id=' + id+'&content='+content,
    method: 'get'
  })
}


export function fbcgl (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/fbcgl?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function fbqdptyz (startDate,endDate,type,taskId) {
  return request({
    url: '/releaseMange/main/fbqdptyz?startDate=' + startDate+"&endDate="+endDate+"&type="+type+"&taskId="+taskId,
    method: 'get'
  })
}

export function zrdw (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/zrdw?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function fblx (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/fblx?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function gnzb (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/gnzb?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function gnzb2 (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/gnzb2?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}


export function gnzb3 (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/gnzb3?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function fbzljsl (startDate,endDate) {
  return request({
    url: '/releaseMange/main/fbzljsl?startDate=' + startDate+"&endDate="+endDate,
    method: 'get'
  })
}

export function cstj (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/cstj?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function hjcstj (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/hjcstj?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function zrqytj (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/zrqytj?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function zrrtj (startDate,endDate,type) {
  return request({
    url: '/releaseMange/main/zrrtj?startDate=' + startDate+"&endDate="+endDate+"&type="+type,
    method: 'get'
  })
}

export function zrdwbydate (startDate,endDate,type,type2) {
  return request({
    url: '/releaseMange/main/zrdwbydate?startDate=' + startDate+"&endDate="+endDate+"&type="+type+"&type2="+type2,
    method: 'get'
  })
}

export function gnjbtjByDate (startDate,endDate,type,type2) {
  return request({
    url: '/releaseMange/main/gnjbtjByDate?startDate=' + startDate+"&endDate="+endDate+"&type="+type+"&type2="+type2,
    method: 'get'
  })
}


export function getDefaultEnvs () {
  return request({
    url: '/releaseMange/main/getDefaultEnvs',
    method: 'get',
  })
}


export function getDefaultTasks (type) {
  return request({
    url: '/releaseMange/main/getDefaultTasks?type='+type,
    method: 'get',
  })
}


export function easyAdd (data) {
  return request({
    url: '/releaseMange/main/easyAdd',
    method: 'post',
    data: data
  })
}

export function getFunTypeArr () {
  return request({
    url: '/releaseMange/main/getFunTypeArr',
    method: 'get'
  })
}

export function getDevUser () {
  return request({
    url: '/releaseMange/main/getDevUser',
    method: 'get'
  })
}

export function getDevResUser () {
  return request({
    url: '/releaseMange/main/getDevResUser',
    method: 'get'
  })
}

export function getTestResUser () {
  return request({
    url: '/releaseMange/main/getTestResUser',
    method: 'get'
  })
}

export function getssfzrResUser () {
  return request({
    url: '/releaseMange/main/getssfzrResUser',
    method: 'get'
  })
}

export function checkstep3 (id,result) {
  return request({
    url: '/releaseMange/main/checkstep3?id='+id+"&result="+result,
    method: 'get'
  })
}

export function checkstep11 (id,result) {
  return request({
    url: '/releaseMange/main/checkstep11?id='+id+"&result="+result,
    method: 'get'
  })
}

export function checkstep10 (id,result) {
  return request({
    url: '/releaseMange/main/checkstep10?id='+id+"&result="+result,
    method: 'get'
  })
}

export function checkstep4 (id,result) {
  return request({
    url: '/releaseMange/main/checkstep4?id='+id+"&result="+result,
    method: 'get'
  })
}

export function checkstep5 (id,result) {
  return request({
    url: '/releaseMange/main/checkstep5?id='+id+"&result="+result,
    method: 'get'
  })
}

export function checkstep6 (id,result) {
  return request({
    url: '/releaseMange/main/checkstep6?id='+id+"&result="+result,
    method: 'get'
  })
}

export function saveReady (data) {
  return request({
    url: '/releaseMange/main/saveReady',
    method: 'post',
    data: data
  })
}

export function getReady (id) {
  return request({
    url: '/releaseMange/main/getReady?id=' + id,
    method: 'get'
  })
}

export function saveRisk (data) {
  return request({
    url: '/releaseMange/main/saveReleaseRisk',
    method: 'post',
    data: data
  })
}

export function getRisk (id) {
  return request({
    url: '/releaseMange/main/getReleaseRisk?id=' + id,
    method: 'get'
  })
}

export function delRisk (data) {
  return request({
    url: '/releaseMange/main/deleteReleaseRisk',
    method: 'post',
    data: data
  })
}

export function delReady (data) {
  return request({
    url: '/releaseMange/main/delReady',
    method: 'post',
    data: data
  })
}

export function saveReadyUser (data) {
  return request({
    url: '/releaseMange/main/saveReadyUser',
    method: 'post',
    data: data
  })
}

export function getReadyUser (id) {
  return request({
    url: '/releaseMange/main/getReadyUser?id=' + id,
    method: 'get'
  })
}

export function delReadyUser (data) {
  return request({
    url: '/releaseMange/main/delReadyUser',
    method: 'post',
    data: data
  })
}

export function saveReadyStep (data) {
  return request({
    url: '/releaseMange/main/saveReadyStep',
    method: 'post',
    data: data
  })
}

export function getReadyStep (id,type) {
  return request({
    url: '/releaseMange/main/getReadyStep?id=' + id+'&type='+type,
    method: 'get'
  })
}

export function delReadyStep (data) {
  return request({
    url: '/releaseMange/main/delReadyStep',
    method: 'post',
    data: data
  })
}

export function cancelRelease (id) {
  return request({
    url: '/releaseMange/main/cancelRelease?id=' + id,
    method: 'get'
  })
}

export function getdoc (id) {
  return request({
    url: '/releaseMange/main/testDoc?id='+id,
    method: 'get'
  })
}



export function creatDoc4Temp (id) {
  return request({
    url: '/releaseMange/main/creatDoc4Temp?id='+id,
    method: 'get'
  })
}


export function goback1st (id) {
  return request({
    url: '/releaseMange/main/goback1st?id='+id,
    method: 'get'
  })
}

export function goback7ed (id) {
  return request({
    url: '/releaseMange/main/goback7ed?id='+id,
    method: 'get'
  })
}

export function realDelAttach (id) {
  return request({
    url: '/releaseMange/main/realDelAttach?id='+id,
    method: 'get'
  })
}

// 导出一个函数，用于获取下一个用户列表
export function getRigesterUser (id) {
  // 发送一个get请求，获取下一个用户列表
  return request({
    url: '/releaseMange/main/getRigesterUser?id='+id,
    method: 'get'
  })
}

export function addLog (query) {
  return request({
    url: '/releaseMange/main/addLog',
    method: 'get',
    params: query
  })
}

export function addLog2 (query) {
  return request({
    url: '/releaseMange/main/addLog2',
    method: 'get',
    params: query
  })
}



export function getLog (query) {
  return request({
    url: '/releaseMange/main/getLog',
    method: 'get',
    params: query
  })
}

