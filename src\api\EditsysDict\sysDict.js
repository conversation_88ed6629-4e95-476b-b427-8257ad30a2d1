import request from '@/utils/request'

// 查询系统字典信息表列表
export function listSysDict (query) {
  return request({
    url: '/EditsysDict/EditsysDict/list',
    method: 'get',
    params: query
  })
}

// 查询系统字典信息表详细
export function getSysDict (pid) {
  return request({
    url: '/EditsysDict/EditsysDict/' + pid,
    method: 'get'
  })
}

// 新增系统字典信息表
export function addSysDict (data) {
  return request({
    url: '/EditsysDict/EditsysDict',
    method: 'post',
    data: data
  })
}

// 修改系统字典信息表
export function updateSysDict (data) {
  return request({
    url: '/EditsysDict/EditsysDict',
    method: 'put',
    data: data
  })
}

// 删除系统字典信息表
export function delSysDict (pid) {
  return request({
    url: '/EditsysDict/EditsysDict/' + pid,
    method: 'delete'
  })
}


// 导出系统字典信息表
export function exportSysDict (query) {
  return request({
    url: '/EditsysDict/EditsysDict/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/EditsysDict/EditsysDict/getInitData/' + dictTypes,
    method: 'get'
  })
}
