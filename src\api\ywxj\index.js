import request from '@/utils/request'
import axios from 'axios'

export function querylist(query) {
  return request({
    url: '/system/ywxj/list',
    method: 'get',
    params: query
  })
}

export function startTask() {
  return request({
    url: '/system/ywxj/startTask',
    method: 'get'
  })
}

export function endTask(uuid) {
  return request({
    url: '/system/ywxj/endTask?uuid='+uuid,
    method: 'get'
  })
}

export function downloadAttchment3(fileid, filename) {
  // 鉴权下载文件
  
  const url = process.env.VUE_APP_BASE_API +'/system/ywxj/dwFile' + '/' + filename
  // window.open(fileDownloadApi + '/' + fileid, '_blank')
  requestFile(url).then(blob => {
    blobToDataURL(blob).then(dataURL => {
      const a = document.createElement('a')
      a.download = filename
      a.href = dataURL
      // a.target = '_blank'
      // 修复firefox中无法触发click
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
    })
  })
}

async function requestFile (url) {
  const res = await axios.get(url, {
    headers: {
      // Authorization: getToken() // token
    },
    responseType: 'blob'
  })
  return res.data
}

function blobToDataURL (blob) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.addEventListener('load', () => {
      resolve(reader.result) // reader.result即为包含文件内容的字符串
    })
    reader.readAsDataURL(blob)
  })
}