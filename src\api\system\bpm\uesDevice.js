import request from '@/utils/request'

// 查询设备清单表列表
export function listUesDevice (query) {
  return request({
    url: '/system/uesDevice/list',
    method: 'get',
    params: query
  })
}

export function listUesDeviceSelect (query) {
  return request({
    url: '/system/uesDevice/selectlist',
    method: 'get',
    params: query
  })
}

// 查询设备清单表详细
export function getUesDevice (id) {
  return request({
    url: '/system/uesDevice/' + id,
    method: 'get'
  })
}

// 新增设备清单表
export function addUesDevice (data) {
  return request({
    url: '/system/uesDevice',
    method: 'post',
    data: data
  })
}

// 修改设备清单表
export function updateUesDevice (data) {
  return request({
    url: '/system/uesDevice',
    method: 'put',
    data: data
  })
}

// 删除设备清单表
export function delUesDevice (id) {
  return request({
    url: '/system/uesDevice/' + id,
    method: 'delete'
  })
}

// 下载导入模板
export function importTemplate () {
  return request({
    url: '/system/uesDevice/importTemplate',
    method: 'get'
  })
}

// 导出设备清单表
export function exportUesDevice (query) {
  return request({
    url: '/system/uesDevice/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesDevice/getInitData/' + dictTypes,
    method: 'get'
  })
}
