import request from '@/utils/request'

// 查询假期明细表列表
export function listItsmDutyHolidayDetail (query) {
  return request({
    url: '/system/itsmDutyHolidayDetail/list',
    method: 'get',
    params: query
  })
}

// 查询假期明细表详细
export function getItsmDutyHolidayDetail (id) {
  return request({
    url: '/system/itsmDutyHolidayDetail/' + id,
    method: 'get'
  })
}

// 新增假期明细表
export function addItsmDutyHolidayDetail (data) {
  return request({
    url: '/system/itsmDutyHolidayDetail',
    method: 'post',
    data: data
  })
}

// 修改假期明细表
export function updateItsmDutyHolidayDetail (data) {
  return request({
    url: '/system/itsmDutyHolidayDetail',
    method: 'put',
    data: data
  })
}

// 删除假期明细表
export function delItsmDutyHolidayDetail (id) {
  return request({
    url: '/system/itsmDutyHolidayDetail/' + id,
    method: 'delete'
  })
}


// 导出假期明细表
export function exportItsmDutyHolidayDetail (query) {
  return request({
    url: '/system/itsmDutyHolidayDetail/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyHolidayDetail/getInitData/' + dictTypes,
    method: 'get'
  })
}
