import request from '@/utils/request'

// 查询通用常用语表列表
export function listItsmCommonExpressions (query) {
  return request({
    url: '/system/itsmCommonExpressions/list',
    method: 'get',
    params: query
  })
}

// 查询通用常用语表详细
export function getItsmCommonExpressions (content) {
  return request({
    url: '/system/itsmCommonExpressions/' + content,
    method: 'get'
  })
}

// 新增通用常用语表
export function addItsmCommonExpressions (data) {
  return request({
    url: '/system/itsmCommonExpressions',
    method: 'post',
    data: data
  })
}

// 修改通用常用语表
export function updateItsmCommonExpressions (data) {
  return request({
    url: '/system/itsmCommonExpressions',
    method: 'put',
    data: data
  })
}

// 删除通用常用语表
export function delItsmCommonExpressions (content) {
  return request({
    url: '/system/itsmCommonExpressions/' + content,
    method: 'delete'
  })
}


// 导出通用常用语表
export function exportItsmCommonExpressions (query) {
  return request({
    url: '/system/itsmCommonExpressions/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmCommonExpressions/getInitData/' + dictTypes,
    method: 'get'
  })
}
