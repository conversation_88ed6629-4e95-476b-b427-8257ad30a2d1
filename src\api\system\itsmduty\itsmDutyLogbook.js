import request from '@/utils/request'

// 查询交接班管理-值班日志列表
export function listItsmDutyLogbook (query) {
  return request({
    url: '/system/itsmDutyLogbook/list',
    method: 'get',
    params: query
  })
}

// 查询交接班管理-值班日志详细
export function getItsmDutyLogbook (id) {
  return request({
    url: '/system/itsmDutyLogbook/' + id,
    method: 'get'
  })
}

// 新增交接班管理-值班日志
export function addItsmDutyLogbook (data) {
  return request({
    url: '/system/itsmDutyLogbook',
    method: 'post',
    data: data
  })
}

// 修改交接班管理-值班日志
export function updateItsmDutyLogbook (data) {
  return request({
    url: '/system/itsmDutyLogbook',
    method: 'put',
    data: data
  })
}

// 删除交接班管理-值班日志
export function delItsmDutyLogbook (id) {
  return request({
    url: '/system/itsmDutyLogbook/' + id,
    method: 'delete'
  })
}


// 导出交接班管理-值班日志
export function exportItsmDutyLogbook (query) {
  return request({
    url: '/system/itsmDutyLogbook/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyLogbook/getInitData/' + dictTypes,
    method: 'get'
  })
}
