import request from '@/utils/request'

// 查询操作表列表
export function listBpmOperate (query) {
  return request({
    url: '/system/bpmOperate/list',
    method: 'get',
    params: query
  })
}

// 查询操作表详细
export function getBpmOperate (id) {
  return request({
    url: '/system/bpmOperate/' + id,
    method: 'get'
  })
}

// 新增操作表
export function addBpmOperate (data) {
  return request({
    url: '/system/bpmOperate',
    method: 'post',
    data: data
  })
}

// 修改操作表
export function updateBpmOperate (data) {
  return request({
    url: '/system/bpmOperate',
    method: 'put',
    data: data
  })
}

// 删除操作表
export function delBpmOperate (id) {
  return request({
    url: '/system/bpmOperate/' + id,
    method: 'delete'
  })
}

// 导出操作表
export function exportBpmOperate (query) {
  return request({
    url: '/system/bpmOperate/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmOperate/getInitData/' + dictTypes,
    method: 'get'
  })
}
