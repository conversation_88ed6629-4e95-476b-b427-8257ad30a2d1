import request from '@/utils/request'

// 查询交接班管理-交接班信息列表
export function listItsmDutyHandover (query) {
  return request({
    url: '/system/itsmDutyHandover/list',
    method: 'get',
    params: query
  })
}

// 查询交接班管理-交接班信息详细
export function getItsmDutyHandover (id) {
  return request({
    url: '/system/itsmDutyHandover/' + id,
    method: 'get'
  })
}

// 新增交接班管理-交接班信息
export function addItsmDutyHandover (data) {
  return request({
    url: '/system/itsmDutyHandover',
    method: 'post',
    data: data
  })
}

// 修改交接班管理-交接班信息
export function updateItsmDutyHandover (data) {
  return request({
    url: '/system/itsmDutyHandover',
    method: 'put',
    data: data
  })
}

// 删除交接班管理-交接班信息
export function delItsmDutyHandover (id) {
  return request({
    url: '/system/itsmDutyHandover/' + id,
    method: 'delete'
  })
}


// 导出交接班管理-交接班信息
export function exportItsmDutyHandover (query) {
  return request({
    url: '/system/itsmDutyHandover/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyHandover/getInitData/' + dictTypes,
    method: 'get'
  })
}
