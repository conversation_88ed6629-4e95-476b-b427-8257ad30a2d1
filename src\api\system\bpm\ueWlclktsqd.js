import request from '@/utils/request'

// 查询网络策略开通申请单列表
export function listUeWlclktsqd (query) {
  return request({
    url: '/system/ueWlclktsqd/list',
    method: 'get',
    params: query
  })
} 

export function pendingListUeWlclktsqd (query) {
  return request({
    url: '/system/ueWlclktsqd/pendinglist',
    method: 'get',
    params: query
  })
} 

// 查询网络策略子表
export function listUesubWlclmx (query) {
  return request({
    url: '/system/uesubWlclmx/list',
    method: 'get',
    params: query
  })
}

// 查询网络策略开通申请单详细
export function getUeWlclktsqd (id) {
  return request({
    url: '/system/ueWlclktsqd/' + id,
    method: 'get'
  })
}

// 新增网络策略开通申请单
export function addUeWlclktsqd (data) {
  return request({
    url: '/system/ueWlclktsqd',
    method: 'post',
    data: data
  })
}

// 修改网络策略开通申请单
export function updateUeWlclktsqd (data) {
  return request({
    url: '/system/ueWlclktsqd',
    method: 'put',
    data: data
  })
}

// 删除网络策略开通申请单
export function delUeWlclktsqd (id) {
  return request({
    url: '/system/ueWlclktsqd/' + id,
    method: 'delete'
  })
}

// 导出网络策略开通申请单
export function exportUeWlclktsqd (query) {
  return request({
    url: '/system/ueWlclktsqd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/ueWlclktsqd/getInitData/' + dictTypes,
    method: 'get'
  })
}
