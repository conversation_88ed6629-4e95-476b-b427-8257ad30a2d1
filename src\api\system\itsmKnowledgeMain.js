import request from '@/utils/request'

// 查询知识主体表列表
export function listItsmKnowledgeMain (query) {
  return request({
    url: '/system/itsmKnowledgeMain/list',
    method: 'get',
    params: query
  })
}

// 查询知识主体表详细
export function getItsmKnowledgeMain (id) {
  return request({
    url: '/system/itsmKnowledgeMain/' + id,
    method: 'get'
  })
}

// 新增知识主体表
export function addItsmKnowledgeMain (data) {
  return request({
    url: '/system/itsmKnowledgeMain',
    method: 'post',
    data: data
  })
}

// 修改知识主体表
export function updateItsmKnowledgeMain (data) {
  return request({
    url: '/system/itsmKnowledgeMain',
    method: 'put',
    data: data
  })
}

// 删除知识主体表
export function delItsmKnowledgeMain (id) {
  return request({
    url: '/system/itsmKnowledgeMain/' + id,
    method: 'delete'
  })
}


// 导出知识主体表
export function exportItsmKnowledgeMain (query) {
  return request({
    url: '/system/itsmKnowledgeMain/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmKnowledgeMain/getInitData/' + dictTypes,
    method: 'get'
  })
}
