import request from '@/utils/request'

// 查询通用信息表列表
export function listItsmCommonMsg (query) {
  return request({
    url: '/system/itsmCommonMsg/list',
    method: 'get',
    params: query
  })
}

// 查询通用信息表详细
export function getItsmCommonMsg (id) {
  return request({
    url: '/system/itsmCommonMsg/' + id,
    method: 'get'
  })
}

// 新增通用信息表
export function addItsmCommonMsg (data) {
  return request({
    url: '/system/itsmCommonMsg',
    method: 'post',
    data: data
  })
}

// 修改通用信息表
export function updateItsmCommonMsg (data) {
  return request({
    url: '/system/itsmCommonMsg',
    method: 'put',
    data: data
  })
}

// 删除通用信息表
export function delItsmCommonMsg (id) {
  return request({
    url: '/system/itsmCommonMsg/' + id,
    method: 'delete'
  })
}


// 导出通用信息表
export function exportItsmCommonMsg (query) {
  return request({
    url: '/system/itsmCommonMsg/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmCommonMsg/getInitData/' + dictTypes,
    method: 'get'
  })
}
