import request from '@/utils/request'

// 查询itsmYjqxpOrder列表
export function listItsmYjqxpOrder (query) {
  return request({
    url: '/system/itsmrepairmain/pendinglist',
    // url: '/system/itsmrepairmain/pendinglist?selectEndTime=' + data.selectEndTime + '&selectStartTime=' + data.selectStartTime,
    method: 'get',
    params: query
  })
}

export function listItsmYjqxpOrderList (query) {
  return request({
    url: '/system/itsmrepairmain/pageList',
    // url: '/system/itsmrepairmain/pendinglist?selectEndTime=' + data.selectEndTime + '&selectStartTime=' + data.selectStartTime,
    method: 'get',
    params: query
  })
}

// 查询itsmYjqxpOrder详细
export function getItsmYjqxpOrder (id) {
  return request({
    url: '/system/itsmrepairmain/' + id,
    method: 'get'
  })
}

// 新增itsmYjqxpOrder
export function addItsmYjqxpOrder (data) {
  return request({
    url: '/system/itsmrepairmain',
    method: 'post',
    data: data
  })
}

// 修改itsmYjqxpOrder
export function updateItsmYjqxpOrder (data) {
  return request({
    url: '/system/itsmrepairmain',
    method: 'put',
    data: data
  })
}

// 删除itsmYjqxpOrder
export function delItsmYjqxpOrder (id) {
  return request({
    url: '/system/itsmrepairmain/' + id,
    method: 'delete'
  })
}

// 导出itsmYjqxpOrder
export function exportItsmYjqxpOrder (query) {
  return request({
    url: '/system/itsmrepairmain/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
// export function getInitData (dictTypes) {
//   return request({
//     url: '/itsmrepairmain/itsmrepairmain/getInitData/' + dictTypes,
//     method: 'get'
//   })
// }
