import request from '@/utils/request'

// 查询网络策略开通子表列表
// export function listUesubWlclmx (query) {
//   return request({
//     url: '/system/uesubWlclmx/list',
//     method: 'get',
//     params: query
//   })
// }

// 查询网络策略开通子表详细
export function getUesubWlclmx (id) {
  return request({
    url: '/system/uesubWlclmx/' + id,
    method: 'get'
  })
}

// 新增网络策略开通子表
export function addUesubWlclmx (data) {
  return request({
    url: '/system/uesubWlclmx',
    method: 'post',
    data: data
  })
}

// 修改网络策略开通子表
export function updateUesubWlclmx (data) {
  return request({
    url: '/system/uesubWlclmx',
    method: 'put',
    data: data
  })
}

// 删除网络策略开通子表
export function delUesubWlclmx (id) {
  return request({
    url: '/system/uesubWlclmx/' + id,
    method: 'delete'
  })
}


// 导出网络策略开通子表
export function exportUesubWlclmx (query) {
  return request({
    url: '/system/uesubWlclmx/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesubWlclmx/getInitData/' + dictTypes,
    method: 'get'
  })
}
