import request from '@/utils/request'

// 查询运维人员变更申请单列表
export function listUeYwrybgsqd (query) {
  return request({
    url: '/system/ueYwrybgsqd/list',
    method: 'get',
    params: query
  })
}

export function listUeYwrybgsqdPending (query) {
  return request({
    url: '/system/ueYwrybgsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function listUeYwrybgsqdPending2 (query) {
  return request({
    url: '/system/ueYwrybgsqd/pendinglist2',
    method: 'get',
    params: query
  })
}

// 查询运维人员变更申请单详细
export function getUeYwrybgsqd (fid) {
  return request({
    url: '/system/ueYwrybgsqd/' + fid,
    method: 'get'
  })
}

// 新增运维人员变更申请单
export function addUeYwrybgsqd (data) {
  return request({
    url: '/system/ueYwrybgsqd',
    method: 'post',
    data: data
  })
}

// 修改运维人员变更申请单
export function updateUeYwrybgsqd (data) {
  return request({
    url: '/system/ueYwrybgsqd',
    method: 'put',
    data: data
  })
}

// 删除运维人员变更申请单
export function delUeYwrybgsqd (fid) {
  return request({
    url: '/system/ueYwrybgsqd/' + fid,
    method: 'delete'
  })
}

// 导出运维人员变更申请单
export function exportUeYwrybgsqd (query) {
  return request({
    url: '/system/ueYwrybgsqd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/ueYwrybgsqd/getInitData/' + dictTypes,
    method: 'get'
  })
}
