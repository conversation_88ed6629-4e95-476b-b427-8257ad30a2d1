import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/fileInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/fileInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/fileInfo',
    method: 'put',
    data
  })
}

export function setCategory(data) {
  return request({
    url: 'api/fileInfo/setCategory',
    method: 'post',
    data
  })
}

export function getFileId(per) {
  return request({
    url: 'api/fileInfo/fileId',
    method: 'get',
    params: per
  })
}

export function getFileById(per) {
  return request({
    url: 'api/fileInfo/fileById/' + per,
    method: 'get'
  })
}

export function getNumberByFileCatId(per) {
  return request({
    url: '/api/fileInfo/getNumberByFileCatId/' + per,
    method: 'get'
  })
}

export default { add, edit, del, getFileId, getNumberByFileCatId }
