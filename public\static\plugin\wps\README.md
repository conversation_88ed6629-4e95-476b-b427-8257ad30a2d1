## Welcome to WPS OAAssist Demo

### 这个项目是什么？

这个工程为WPS文字项目常见的OA场景集成下的WPS加载项——OA助手，项目使用了丰富的WPS API的功能，可以帮助大家能够快速理解并熟悉WPS加载项机制以及和浏览器调用交互的流程。

### 工程结构

* icon 						图标文件。
* js						WPS 加载项功能逻辑的js代码。
* otherslib					vue,jQueryd等第三方库。
* template					示例模板文件。
* importTemplate.html		导入模板页面。
* index.html				加载项的默认加载页面。
* qrcode.html				插入二维码页面。
* redhead.html				插入红头页面。
* selectBookmark.html		插入标签页面。 
* selectSeal.html			插入签章页面。 
* setUserName.html			修改默认用户名页面。 
* ribbon.xml				自定义选项卡配置。 

### 注意事项

* 本工程只是演示demo
* 我们建议您结合具体的应用场景修改示例代码，这样更能够体现OA助手集成的应用场景
* 为了保护代码，建议对上线代码进行混淆
* 使用该工程的时候，必须要安装WPS专业版，请咨询QQ：3253920855
