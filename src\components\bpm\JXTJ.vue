<template>
    <div>
      <a-tabs default-active-key="2" @change="tab2c">
        
        <a-tab-pane key="2" tab="服务绩效统计">
          <a-card :bordered="false" style="margin-bottom: 10px;">
          <a-row :span="24"  style="margin-top: 10px;"> 
            <a-col :span="12">
              <a-select  style="width: 80%;margin-left: 50px;" @change="ychange">
                <a-select-option v-for="(dict, index) in khyeararr" :key="index" :value="dict" >{{ dict }}</a-select-option>          
              </a-select>
              <a-button @click="searchtj">搜索</a-button>
            </a-col>
          </a-row>
          <div v-for="(item,index) in tjlist" :key="index">
            
              
              <a-row :span="24"  style="margin-top: 10px;"> 
                <a-col :span="12" style="text-align: center;">{{ item.contractName }}</a-col>
             
                <a-col :span="12" style="text-align: center;">
                  <a-row :span="24"  style="text-align: center;"> 
                    <a-col :span="4">
                      <a-button @click="checkitem(item,99)" :type="item.show==1?'primary':'default'">{{ item.year }}</a-button>
                    </a-col>
                    
                    <a-col :span="4" v-for="(item1,index1) in item.khzqList" :key="index1" >
                      <a-button @click="checkitem(item,index1)" :type="item.show==index1+2?'primary':'default'"> {{ item1.season }}</a-button>
                    </a-col>
                  </a-row>
                </a-col>
              </a-row>
              <a-row :span="24"  style="margin-top: 10px;" v-if="1==item.show && null!=item.total"> 
                <a-col :span="24">
                  
              
                  <div  class="module-in module-in01 modlule-show"  @click="showsidemenu(item)"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计扣分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.total.down }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.total.avgDown }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计加分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.total.up }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.total.avgUp }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">合计分值:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.total.total }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.total.avgTotal }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>


                </a-col>
                
              </a-row>

              <a-row :span="24"  style="margin-top: 10px;" v-if="2==item.show && null!=item.season1"> 
                <a-col :span="24">
                <div  class="module-in module-in01 modlule-show"  @click="showsidemenu(item)" 
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计扣分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season1.down }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season1.avgDown }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计加分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season1.up }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season1.avgUp }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">合计分值:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season1.total }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season1.avgTotal }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>


                </a-col>
              </a-row>

              <a-row :span="24"  style="margin-top: 10px;" v-if="3==item.show && null!=item.season2"> 
                <a-col :span="24">
                <div  class="module-in module-in01 modlule-show"  @click="showsidemenu(item)"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计扣分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season2.down }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season2.avgDown }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计加分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season2.up }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season2.avgUp }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">合计分值:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season2.total }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season2.avgTotal }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>


                </a-col>
              </a-row>

              <a-row :span="24"  style="margin-top: 10px;" v-if="4==item.show && null!=item.season3"> 
                <a-col :span="24">
                <div  class="module-in module-in01 modlule-show"  @click="showsidemenu(item)"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计扣分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season3.down }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season3.avgDown }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计加分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season3.up }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season3.avgUp }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">合计分值:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season3.total }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season3.avgTotal }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>


                </a-col>
              </a-row>

              <a-row :span="24"  style="margin-top: 10px;" v-if="5==item.show && null!=item.season4"> 
                <a-col :span="24">
                <div  class="module-in module-in01 modlule-show"  @click="showsidemenu(item)"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计扣分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season4.down }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season4.avgDown }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">累计加分:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season4.up }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season4.avgUp }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>
                  <div  class="module-in module-in01 modlule-show"
                    style="display: flex;justify-content: center;flex-direction: column;flex-wrap: wrap;float: left;height: 150px;width: 33.2%;margin: 0.06%;">
                    <div style="left: 15px;position: relative;">
                      <p style="font-size: 15px;height: 10px;">合计分值:</p>
                      <div style="display: flex;height: 40px;">
                        <p style="font-size:25px;color: #0366c3;">{{ item.season4.total }}
                        <p style="padding-top: 10px;font-size: 15px;font-weight: bold;"
                          >
                          &nbsp;&nbsp;分</p>

                        
                        </p>
                      </div>
                      <p>环比&nbsp;&nbsp;&nbsp;{{ item.season4.avgTotal }}%&nbsp;<a-icon type="caret-up" style="color: red;" /></p>
                    </div>
                  </div>


                </a-col>
              </a-row>

          
          </div>
            </a-card>
        </a-tab-pane>
      </a-tabs>
      <a-drawer ref="drawer" width="60%" placement="right" @close="visible = false" :closable="false"
                  :visible="visible">
                  <advance-table title="信息表" tableKey="base-dashbord-index-table" rowKey="id" size="middle" :columns="columns"
                    :data-source="darwerList" :pagination="{
                      current: queryParam.pageNum,
                      pageSize: queryParam.pageSize,
                      total: total,
                      showSizeChanger: true,
                      showLessItems: true,
                      showQuickJumper: true,
                      showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，总计 ${total} 条`,
                      onChange: changeSize,
                      onShowSizeChange: onShowSizeChange,
                    }" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }">
                    <div class="table-operations" slot="button" style="float:right;">
                      <a-button type=""  v-hasPermi="['workOrder:itsmWorkOrder:export']">
                        <a-icon type="download" />导出
                      </a-button>
                    </div>
                    <span slot="bpm" slot-scope="{text, record}">
                      <a @click="handleBpm(record)"  >
                        {{text}}
                      </a>
                    </span>
                  </advance-table>
                </a-drawer>
    </div>
  </template>
  <script>
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import { getInitData } from '@/api/system/bpm/uesDevice'
  import { busDict,getContractInfo,getScore,removeJFK,getFWJXTJ ,getRightList} from '@/api/ItsmQualityProvider/itsmQualityContract.js'
  import {listContractphas2,listScoreDetail,updateItsmScorecard ,updateItsmScorecardDetail,listScore} from '@/api/ItsmQualityScoreClause/itsmQualityScoreClause.js'
import axios from 'axios'
import moment from 'moment';
  
  export default {
    components: {
      AdvanceTable,
    },
    data() {
      return {
        selectedRowKeys:[],
        khyeararr:[],
        khyear:'2025',
        labelCol: { span: 6 },
        wrapperCol: { span: 18 },
        khlx:[],
        advanced:false,
        addDisable:true,
        columns: [
        {
          title: '服务绩效编号',
          key: 'assessNo',
          dataIndex: 'assessNo',
           scopedSlots: { customRender: 'bpm' }
        },
        {
          title: '服务商',
          key: 'providerName',
          dataIndex: 'providerName',
           scopedSlots: { customRender: 'bpm' }
        },
        {
          title: '责任人',
          dataIndex: 'directorName',
           scopedSlots: { customRender: 'bpm' }
        },
        {
          title: '考核内容说明',
          dataIndex: 'assessContent',
           scopedSlots: { customRender: 'bpm' }
        },
      ],
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        mainId: undefined,
        no: undefined
      },
      darwerList: [],
      visible:false,
        khlist:[],
        khdetaillist:[],
        total:0,
        Form:{
          id:null,
          cardNo:null,
          cardName:null,
          assessType:null,
          version:null,
          deptName:null,
          cardYear:null,
          cardSeason:null,
          beginTime:null,
          endTime:null,
          scoreId:null,
          scoreName:null,
          providerId:null,
          providerName:null,
          contractId:null,
          contractName:null,
          phaseId:null,
          totalScore:null,
          grade:null,
          locked:null,
          creator:null,
          ctime:null,

        },
        providerArr:[],
        contractArr:[],
        troubleCheckStatusArr:[],
        khlx:[],
        listsh:false,
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          cardNo:null,
          cardName:null,
          version:null,
          deptName:null,
          scoreName:null,
          providerName:null,
          contractName:null,
          assessType:null,
          bst:null,
          est:null,
          bed:null,
          eed:null,
        },
        tjlist:[],
      }
    },
    created(){
      getInitData('jxfws,khlx,khdx,fwjxejzb,fwjxyjzb,fwjxkhzt').then(res=>{
        this.khlx = res.data.khlx
      })
      busDict().then(resd=>{
        this.providerArr = resd
      })
      this.handleQuery()
      this.getTJ()
      let y=2022
      for(var i=1;i<=100;i++){
        this.khyeararr.push(Number(y)+i)
      }
    },
    methods: {
      handleBpm(record){
      // this.$router.push({ name: 'Sjdj',params:{xid:record.id,xpid:record.refId} })
        this.loading=true
        this.$multiTab.openTempRoute2("fwjx/jxdj/index6",'fwjxkhdjsq6',record,'Fwjxkh')
      },
      showsidemenu(item){
        item.shownu = getRightList
        getRightList(item).then(res=>{
          this.darwerList = res.rows
          this.total = res.total
          this.visible = true
        })
      },
       onShowSizeChange(current, pageSize) {
      this.queryParam.pageSize = pageSize
      homepageList(this.queryParam).then((res) => {
        this.darwerList = res.data.list
        this.total = res.data.total
      })

    },
    changeSize(current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      homepageList(this.queryParam).then((res) => {
        this.darwerList = res.data.list
        this.total = res.data.total
      })
      
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = this.selectedRows.map(item => item.id)
      
    },
      searchtj(){
        this.getTJ()
      },
      ychange(e){
        this.khyear = e
      },
      checkitem(item,nu){
        if(nu==99){
          item.show=1
        }else if(nu==0){
          item.show=2
        }else if(nu==1){
          item.show=3
        }else if(nu==2){
          item.show=4
        }else if(nu==3){
          item.show=5
        }
      },
      tab2c(){
        this.getTJ()
        this.handleQuery()
      },
      getTJ(){
        this.tjlist =[]
        getFWJXTJ(this.khyear).then(res=>{
          this.tjlist = res.data
        })
      },
      handleQuery(){
        listScore(this.queryParam).then(res=>{
          this.khdetaillist = res.rows
          this.total = res.total
        })
      },
 
      handleDelete (row) {
      var that = this
      const ItsmQualityScoreIds = row.id || this.ids
      this.$confirm({
        title: '确认删除所选中数据?',
        onOk () {
          return removeJFK(ItsmQualityScoreIds)
            .then(() => {
        
              that.getList()
              that.$message.success(
                '删除成功',
                3
              )
          })
        },
        onCancel () {}
      })
    },
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      trankhlx(row){
        let r = ''
        this.khlx.forEach(item=>{
          if(item.dictValue == row.assessType){
            r = item.dictLabel
          }
        })
        return r
      },
      trankhdx(row){
        let r = ''
        this.khdx.forEach(item=>{
          if(item.dictValue == row.assessType){
            r = item.dictLabel
          }
        })
      },
      getList(){
        listScore(this.queryParam).then(res=>{
          this.khdetaillist = res.rows
          this.total = res.total
        })
      },
      handleQuery(){
        this.getList()
      },
      resetQuery(){},
      onSizeChange (current, size) {
        this.queryParam.pageNum = 1
        this.queryParam.pageSize = size
        this.getList()
      },
      /** 翻页操作 */
      changeSize (current, pageSize) {
        this.queryParam.pageNum = current
        this.queryParam.pageSize = pageSize
        this.getList()
      },
   
      newSaveForm(){
        if( this.listsh ==false){
          updateItsmScorecard(this.Form).then(res=>{
            this.$message.success('保存成功')
            this.Form.cardNo = res.data.cardNo
            this.Form.id = res.data.id
              this.listsh =true
              let q= {
                cardNo:this.Form.id
              }
              listScoreDetail(q).then(res=>{
                this.khdetaillist = res
              })
          
            
          })
        }else{
          updateItsmScorecard(this.Form)
          updateItsmScorecardDetail(this.khdetaillist)
          this.$message.success('保存成功')
        }
      },
      selzq(e){
        for(var i=0;i<this.khlist.length;i++){
          if(this.khlist[i].id==e){
            this.Form.cardYear = this.khlist[i].assessYear
            this.Form.cardSeason = this.khlist[i].assessCycle
          }
        }
        
      },
      changetime(e){
        this.Form.beginTime = e[0].format('YYYY-MM-DD HH:mm:ss')
        this.Form.endTime = e[1].format('YYYY-MM-DD HH:mm:ss')
      },
      searchkpxz(e){
        for(var i=0;i<this.providerArr.length;i++){
          if(this.providerArr[i].id==e){
              this.Form.providerName = this.providerArr[i].providerName
              this.Form.providerId = this.providerArr[i].id
          }
        }
        getContractInfo(e).then(rese=>{
          this.contractArr = rese.contract
        })
      },
      selContract(e){
        for(var i=0;i<this.contractArr.length;i++){
          if(this.contractArr[i].id==e){
              this.Form.contractName = this.contractArr[i].contractName
              this.Form.contractId = this.contractArr[i].id
          }
        }
        getScore(e).then(res2=>{
          this.troubleCheckStatusArr = res2.scord
        })
        let qp={
          contractId:this.Form.contract_id 
        }
        listContractphas2(qp).then(res=>{
          this.khlist = res
        })
      },
      selScore(e){
        for(var i=0;i<this.troubleCheckStatusArr.length;i++){
          if(e==this.troubleCheckStatusArr[i].id){
            this.Form.scoreId = this.troubleCheckStatusArr[i].id
            this.Form.scoreName = this.troubleCheckStatusArr[i].scoreName
          }
        }
      },
    },
  }
  </script>
 <style lang="less" scoped="scoped">
 .switch-container {
   /* 容器样式，如果需要的话 */
 }
 
 .switch-item {
   display: flex;
   align-items: center; /* 垂直居中 */
   margin-bottom: 10px; /* 控制开关之间的间距 */
 }
 
 .switch-label {
   margin-left: 8px; /* 控制开关和标签之间的间距 */
 }
 /deep/  .ant-form-item-label {
         width: 100px;
       }
 
 
 

 
 
 

 
 /deep/ .ant-select-auto-complete.ant-select {
     color: rgba(0, 0, 0, 1);
     cursor: not-allowed;
     opacity: 1;
     background-color: transparent;
 }
 

 /deep/ .ant-select-auto-complete {
     color: rgba(0, 0, 0, 1) !important;
     cursor: not-allowed;
     opacity: 1;
     background-color: transparent;
 }
 
 
 
 /deep/ .ant-cascader-picker-disabled {
   color: rgba(0, 0, 0, 1);
   background: #ffffff;
   cursor: not-allowed;
 }
 
// 表单输入框、选择框样式
/deep/ .form-item .ant-col-6.ant-form-item-control-wrapper {
  width:75%;
  .ant-calendar-picker,.ant-input-number{
    width: 100%;
  }
}
 
 </style>