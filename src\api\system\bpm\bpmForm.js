import request from '@/utils/request'

// 查询表单表列表
export function listBpmForm (query) {
  return request({
    url: '/system/bpmForm/list',
    method: 'get',
    params: query
  })
}

// 查询表单表详细
export function getBpmForm (id) {
  return request({
    url: '/system/bpmForm/' + id,
    method: 'get'
  })
}

// 新增表单表
export function addBpmForm (data) {
  return request({
    url: '/system/bpmForm',
    method: 'post',
    data: data
  })
}

// 修改表单表
export function updateBpmForm (data) {
  return request({
    url: '/system/bpmForm',
    method: 'put',
    data: data
  })
}

// 删除表单表
export function delBpmForm (id) {
  return request({
    url: '/system/bpmForm/' + id,
    method: 'delete'
  })
}

// 导出表单表
export function exportBpmForm (query) {
  return request({
    url: '/system/bpmForm/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmForm/getInitData/' + dictTypes,
    method: 'get'
  })
}
