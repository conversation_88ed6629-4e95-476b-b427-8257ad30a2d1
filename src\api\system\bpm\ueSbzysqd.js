import request from '@/utils/request'

// 查询设备资源申请单列表
export function listUeSbzysqd (query) {
  return request({
    url: '/system/ueSbzysqd/list',
    method: 'get',
    params: query
  })
}

// 查询待办设备资源申请单列表
export function pendinglistUeSbzysqd (query) {
  return request({
    url: '/system/ueSbzysqd/pendinglist',
    method: 'get',
    params: query
  })
}

// 查询设备资源申请单详细
export function getUeSbzysqd (id) {
  return request({
    url: '/system/ueSbzysqd/' + id,
    method: 'get'
  })
}

// 新增设备资源申请单
export function addUeSbzysqd (data) {
  return request({
    url: '/system/ueSbzysqd',
    method: 'post',
    data: data
  })
}

// 修改设备资源申请单
export function updateUeSbzysqd (data) {
  return request({
    url: '/system/ueSbzysqd',
    method: 'put',
    data: data
  })
}

// 删除设备资源申请单
export function delUeSbzysqd (id) {
  return request({
    url: '/system/ueSbzysqd/' + id,
    method: 'delete'
  })
}

// 导出设备资源申请单
export function exportUeSbzysqd (query) {
  return request({
    url: '/system/ueSbzysqd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/ueSbzysqd/getInitData/' + dictTypes,
    method: 'get'
  })
}
