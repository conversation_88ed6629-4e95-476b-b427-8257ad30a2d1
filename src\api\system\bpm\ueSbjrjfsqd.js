import request from '@/utils/request'

// 查询设备进入机房申请单列表
export function listUeSbjrjfsqd (query) {
  return request({
    url: '/system/ueSbjrjfsqd/list',
    method: 'get',
    params: query
  })
}

// 查询设备进入机房申请单列表
export function listUeSbjrjfsqdPending (query) {
  return request({
    url: '/system/ueSbjrjfsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function listUeSbjrjfsqdPending2 (query) {
  return request({
    url: '/system/ueSbjrjfsqd/pendinglist2',
    method: 'get',
    params: query
  })
}

// 查询设备进入机房申请单详细
export function getUeSbjrjfsqd (id) {
  return request({
    url: '/system/ueSbjrjfsqd/' + id,
    method: 'get'
  })
}

// 新增设备进入机房申请单
export function addUeSbjrjfsqd (data) {
  return request({
    url: '/system/ueSbjrjfsqd',
    method: 'post',
    data: data
  })
}

// 修改设备进入机房申请单
export function updateUeSbjrjfsqd (data) {
  return request({
    url: '/system/ueSbjrjfsqd',
    method: 'put',
    data: data
  })
}

// 删除设备进入机房申请单
export function delUeSbjrjfsqd (id) {
  return request({
    url: '/system/ueSbjrjfsqd/' + id,
    method: 'delete'
  })
}

// 导出设备进入机房申请单
export function exportUeSbjrjfsqd (query) {
  return request({
    url: '/system/ueSbjrjfsqd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/ueSbjrjfsqd/getInitData/' + dictTypes,
    method: 'get'
  })
}
