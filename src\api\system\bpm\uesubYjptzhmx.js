import request from '@/utils/request'

// 查询硬件平台账号权限子表列表
export function listUesubYjptzhmx (query) {
  return request({
    url: '/system/uesubYjptzhmx/list',
    method: 'get',
    params: query
  })
}

// 查询硬件平台账号权限子表详细
export function getUesubYjptzhmx (id) {
  return request({
    url: '/system/uesubYjptzhmx/' + id,
    method: 'get'
  })
}

// 新增硬件平台账号权限子表
export function addUesubYjptzhmx (data) {
  return request({
    url: '/system/uesubYjptzhmx',
    method: 'post',
    data: data
  })
}

// 修改硬件平台账号权限子表
export function updateUesubYjptzhmx (data) {
  return request({
    url: '/system/uesubYjptzhmx',
    method: 'put',
    data: data
  })
}

// 删除硬件平台账号权限子表
export function delUesubYjptzhmx (id) {
  return request({
    url: '/system/uesubYjptzhmx/' + id,
    method: 'delete'
  })
}


// 导出硬件平台账号权限子表
export function exportUesubYjptzhmx (query) {
  return request({
    url: '/system/uesubYjptzhmx/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesubYjptzhmx/getInitData/' + dictTypes,
    method: 'get'
  })
}
