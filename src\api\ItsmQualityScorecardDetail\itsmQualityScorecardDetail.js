import request from '@/utils/request'

// 查询计分卡明细列表
export function listItsmQualityScorecardDetail (query) {
  return request({
    url: '/system/itsmqualityscorecarddetail/list',
    method: 'get',
    params: query
  })
}

// 查询计分卡明细详细
export function getItsmQualityScorecardDetail (id) {
  return request({
    url: '/system/itsmqualityscorecarddetail/' + id,
    method: 'get'
  })
}

// 新增计分卡明细
export function addItsmQualityScorecardDetail (data) {
  return request({
    url: '/system/itsmqualityscorecarddetail',
    method: 'post',
    data: data
  })
}

// 修改计分卡明细
export function updateItsmQualityScorecardDetail (data) {
  return request({
    url: '/system/itsmqualityscorecarddetail',
    method: 'put',
    data: data
  })
}

// 删除计分卡明细
export function delItsmQualityScorecardDetail (id) {
  return request({
    url: '/system/itsmqualityscorecarddetail/' + id,
    method: 'delete'
  })
}


// 导出计分卡明细
export function exportItsmQualityScorecardDetail (query) {
  return request({
    url: '/system/itsmqualityscorecarddetail/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmqualityscorecarddetail/getInitData/' + dictTypes,
    method: 'get'
  })
}
