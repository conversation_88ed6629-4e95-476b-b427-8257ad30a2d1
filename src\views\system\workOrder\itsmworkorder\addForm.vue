<template>
<div >

      <a-form-model ref="form" :model="form" :rules="rules" slot="content" layout="inline" >
          <a-card broder="false" style="margin-bottom:10px; " >
            
            <a-row>
              <a-col :span="12">
                <a-descriptions title="工作任务填报" style="margin: 20px;"></a-descriptions>
              </a-col>
              <a-col :span="12" style="float:right;">   
                <a-button style="margin: 20px;float:right;" @click="closeThisWin">关闭</a-button>         
                <a-button type="primary" style="margin: 20px;float:right;" @click="submitForm">保存</a-button>
                <a-button type="primary" style="margin: 20px;float:right;" @click="nextStep" v-if="!delayDisabled">提交</a-button>
                <a-button type="primary" style="margin: 20px;float:right;" @click="updateDelay" v-if="delayDisabled">延期</a-button>
                <a-button style="margin: 20px;float:right;">粘贴</a-button>
              </a-col>
             
            </a-row>
          </a-card>
          <a-card broder="false" style="margin-bottom:10px;margin-top:10px;">
            <div style="display: flex; align-items: center; justify-content: space-between;">
              <div style="font-weight: 600; font-size: 16px; margin-left:10px;">督办历史</div>
              <a-button
                v-if="showSuperviseTable"
                type="primary"
                @click="superviseCollapse = !superviseCollapse"
                style="height: 32px; font-size: 12px; margin-right: 10px;margin-top:5px;margin-bottom:5px"
              >
                {{ superviseCollapse ? '收起督办历史' : '展开督办历史' }}
              </a-button>
            </div>
            <div v-show="superviseCollapse && showSuperviseTable">
              <a-table
                :columns="superviseHistoryColumns"
                :data-source="superviseHistoryList"
                :pagination="false"
                rowKey="id"
                bordered
                size="small"
              />
            </div>
          </a-card>
          <a-card broder="false" style="margin-bottom:10px;margin-top:10px; " >
            <a-row :span="24" style="margin-top:10px; margin-bottom: 10px; width: 100%;" >

              <a-col :span="8"  >
                <a-form-model-item  label=" 业务编号" prop="no" :labelCol="{span:2}" :wrapperCol="{span:6}" >
                  <a-input v-model="form.no" placeholder="请输入作业任务编号" :disabled="addDisable" style="width:350px;" />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item  label="  填报日期" prop="addTime" :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-date-picker :disabled="addDisable" style="width:350px;" 
                    v-model="form.addTime"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :show-today="true"
                    placeholder="选择日期"
                    
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">     
                工作状态: <a-tag :color="workStatus === '已完成' ? 'green' : 'red'">{{ workStatus }}</a-tag>                
              </a-col>
            </a-row>
            <a-row :span="24" style="margin-top:10px; margin-bottom: 10px; width: 100%;" >
              <a-col :span="8">
                <a-form-model-item  label="  督办类别" prop="superviseContent" :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-select v-model="form.superviseContent" style="width:350px;" :disabled="delayDisabled"   >
                    <a-select-option v-for="(dict, index) in superviseTypeOptions" :key="index" :value="dict.dictValue"> {{ dict.dictLabel }}</a-select-option>
                  </a-select>
                
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item  label=" 工作负责人" prop="workUserId" :labelCol="{span:2}" :wrapperCol="{span:6}" >                
                  <a-select v-model="form.workUserId" allowClear style="width:350px;" @change="changeWorkUser"  :disabled="delayDisabled"  >
                    <a-select-option v-for="(item, index) in usersList" :key="index" :value="item.id" >{{ item.nickName }}</a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
              </a-col>
            </a-row>
            <a-row :span="24" style="margin-top:10px; margin-bottom: 10px;margin-right: 10px;width: 100%;"  >
              <a-col :span="24" >
                <a-form-model-item label="工作内容"   prop="content" style="width:100%;" :labelCol="{span:2}" :wrapperCol="{span:22}"  >
                  <a-textarea v-model="form.content" placeholder="请输入工作内容" :rows="1"  :disabled="delayDisabled" style="width:100%;"   />
                </a-form-model-item>
              </a-col>
              
            </a-row>
            <a-row :span="24" style="margin-top:10px; margin-bottom: 10px;margin-right: 10px;width: 100%;">
              <a-col :span="8">
                <a-form-model-item  label="计划开始时间" prop="plannedStartTime"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                  <a-date-picker :disabled="delayDisabled" 
                    v-model="form.plannedStartTime"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :show-today="true"
                    show-time
                    placeholder="选择日期"
                    style="width:350px;"
                  />
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item  label="计划结束时间" prop="plannedEndTime"  :labelCol="{span:2}" :wrapperCol="{span:6}"  >
                  <a-date-picker :disabled="delayDisabled" 
                    v-model="form.plannedEndTime"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :show-today="true"
                    placeholder="选择日期"
                    show-time
                    style="width:350px;"
                  />
                
                </a-form-model-item>
              </a-col>
              <a-col :span="8">
                <a-form-model-item v-if="delayDisabled" label="延期时间" prop="plannedOldTime"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                  <a-date-picker :disabled="!delayDisabled" 
                    v-model="form.plannedOldTime"
                    valueFormat="YYYY-MM-DD HH:mm:ss"
                    :show-today="true"
                    show-time
                    placeholder="选择日期"
                    style="width:350px;"
                  />
                
                </a-form-model-item>
              </a-col>
              </a-row>
              <a-row style="margin-top:10px; margin-bottom: 10px;margin-right: 10px;width: 100%;" :span="24" >
                <a-col :span="24">
                  <a-form-model-item  label="  上传附件" extra="1、仅能上传zip,  rar,7z类型文件;
                  2、最多可上传1个文件;
                  3、附件名称最长100个字符:" :labelCol="{span:2}" :wrapperCol="{span:6}" >
                  <a-tag v-for="(item,index) in fileList" closable  @close="delAttach(index)" @change="dlAttach" @click="dlAttach(item.id,item.name)" >{{item.tag}}</a-tag>
                    <a-upload :file-list="uploadfileList" :disabled="delayDisabled" 
                  :before-upload="handleBeforeUpload">
                    <a-button :disabled="delayDisabled">
                      <a-icon type="upload" />
                      上传附件
                    </a-button>
                  </a-upload>
                  </a-form-model-item>
                  
                </a-col>
              </a-row>
              
              <a-row style="margin-top:10px; margin-bottom: 10px;margin-right: 10px;width: 100%;" :span="24" >
                <a-col :span="8">
                    <a-form-model-item label="填报人" prop="addUser"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                      <a-input v-model="form.addUser" placeholder="请输入填报人" :disabled="addDisable" style="width:350px;"  />
                    </a-form-model-item>
                </a-col> 
                <a-col :span="8">
                    <a-form-model-item label=" 填报人单位" prop="addUnit"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                      <a-input v-model="form.addUnit" placeholder="请输入填报人单位" :disabled="addDisable" style="width:350px;"  />
                    </a-form-model-item>
                </a-col> 
                <a-col :span="8">
                  <a-form-model-item v-if="delayDisabled" label="延期审核人" prop="checkUserId"  :labelCol="{span:2}" :wrapperCol="{span:6}" >
                    <a-select v-model="form.checkUserId" allowClear style="width:350px;" @change="changeCheckUser" :disabled="!delayDisabled">
                      <a-select-option v-for="(item, index) in yqUserList" :key="index" :value="item.id" >{{ item.nickName }}</a-select-option>
                    </a-select>
                  </a-form-model-item>
              </a-col>
            </a-row>
          </a-card>
          <a-card broder="false" style="margin-bottom:10px;margin-top:10px;margin-left:10px;" v-if="showExecuteInfo">
          <a-descriptions title="工作执行信息" style="margin: 20px;"></a-descriptions>
          <a-row :span="24" style="margin-top:10px;margin-left:30px;margin-right: 10px;width: 100%;">
            <a-col :span="8">
              <a-form-model-item label="工作执行结果" prop="executeResult" :labelCol="{span:2}" :wrapperCol="{span:6}">
                <a-select v-model="form.executeResult" :disabled="true" style="width:350px;" >
                  <a-select-option v-for="(dict, index) in executeResultOptions" :key="index" :value="dict.dictValue">
                    {{ dict.dictLabel }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="执行情况说明" prop="executeContent" :labelCol="{span:2}" :wrapperCol="{span:6}" >
                <a-input v-model="form.executeContent" :disabled="true" placeholder="请输入工作执行情况说明"  style="width:350px;" />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="执行人" prop="executeUser" :labelCol="{span:2}" :wrapperCol="{span:6}">
                <a-input v-model="form.executeUser" :disabled="true" style="width:350px;" />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :span="24" style="margin-top:10px;margin-left:30px;margin-right: 10px;width: 100%;">
            <a-col :span="8">
              <a-form-model-item label="实际开始时间" prop="startTime" :labelCol="{span:2}" :wrapperCol="{span:6}">
                <a-date-picker v-model="form.startTime" valueFormat="YYYY-MM-DD HH:mm:ss" :disabled="true" style="width:350px;" />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="实际结束时间" prop="endTime" :labelCol="{span:2}" :wrapperCol="{span:6}">
                <a-date-picker v-model="form.endTime" valueFormat="YYYY-MM-DD HH:mm:ss" :disabled="true" style="width:350px;" />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item label="执行操作时间" prop="executeTime" :labelCol="{span:2}" :wrapperCol="{span:6}">
                <a-date-picker v-model="form.executeTime" valueFormat="YYYY-MM-DD HH:mm:ss" :disabled="true" style="width:350px;" />
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-card>
      </a-form-model>

</div>
</template>
<script>
// import { listItsmWorkOrder, delItsmWorkOrder, exportItsmWorkOrder, getInitData,getUserList4gzdb } from '@/api/system/workOrder/itsmWorkOrder'
import { listYqshUsers,listItsmWorkOrder, delItsmWorkOrder, exportItsmWorkOrder,getItsmWorkOrder, addItsmWorkOrder, listDbUsers,
  updateItsmWorkOrder, getInitData,getUserList4gzdb ,changeResponseStatus,setDelay} from '@/api/system/workOrder/itsmWorkOrder'
import { getInfo  } from '@/api/login'
import axios from 'axios'
import { downloadAttchment } from '@/api/system/ywrygl/operation'
import moment from 'moment';
import { getSuperviseHistory } from '@/api/system/workOrder/itsmWorkOrder'
export default {
  name: 'CreateForm',
  props: {
    

  },
  components: {
    

  },
  data () {
    return {
    workStatus: '工作中',
    showExecuteInfo: false,
    superviseCollapse: true,
    showSuperviseTable: false,
    superviseHistoryColumns: [
      { title: '督办人', dataIndex: 'superviseUser', align: 'center' },
      { title: '督办时间', dataIndex: 'superviseTime', align: 'center' },
      { title: '详细内容', dataIndex: 'content', align: 'center' },
      { title: '添加时间', dataIndex: 'addTime', align: 'center' }
    ],
    superviseHistoryList: [],
      delayDisabled:false,
      labelAlign:"right",
      shPath:false,
      fileList:[],
      usersList:[],
      dbUserList:[],
      responseStatusOptions: [],
      // 审核结果字典
      checkResultOptions: [],
      // 审核状态字典
      checkStatusOptions: [],
      // 执行状态字典
      executeStatusOptions: [],
      // 作业状态字典
      statusOptions: [],
      // 督办类别
      superviseTypeOptions:[],
      uploadfileList:[],
      addDisable:true,
      open: false,
      closeDialog: true,
      spinning: false,
      delayTime: 100,
      labelCol: { span: 4 },
      wrapperCol: { span: 14 },
      loading: false,
      disabled: false,
      total: 0,
      id: undefined,
      formTitle: '添加我创建的工作',
      // 表单参数
      form: {
        status:0,
        addTime: '2024-06-03',
        executeResult: '',         // 工作执行结果
        executeContent: '',        // 工作执行情况说明
        startTime: '',             // 实际开始时间
        endTime: '',               // 实际结束时间
        executeTime: '',           // 执行操作时间
        executeUser: '',   
      },
      executeResultOptions: [],     // 工作执行结果字典选项
      addDisable: false,  
      rules: {
        superviseContent: [
          { required: true, message: "督办类别不能为空"  },
        ],
        executeUser: [
          { required: true, message: "工作负责人不能为空"  },
        ],
        content: [
          { required: true, message: "工作内容不能为空"  },
        ],
        plannedStartTime: [
          { required: true, message: "计划开始时间不能为空"  },
        ],
        plannedEndTime: [
          { required: true, message: "计划结束时间不能为空"  },
        ],
        workUserId: [
          { required: true, message: "工作负责人不能为空"  },
        ],
      },
    
    }
  },
  filters: {},
  activated(){
    
    this.initFun()
  this.showExecuteInfo = !!this.$route.query.fromQuery
  getInitData('execute_result').then(response => {
    this.executeResultOptions = response.data.execute_result
  })
  // 如果有工单id，获取工单详情
  if (this.$route.params.xid) {
    getItsmWorkOrder(this.$route.params.xid).then(res => {
      this.form = {
        ...this.form,
        ...res.data
      }
    })
  }
  if (this.$route.params.xid && this.$route.params.xid !== "") {
    // ...原有获取工单代码...
    getSuperviseHistory(this.$route.params.xid).then(res2 => {
      this.superviseHistoryList = res2.data
      this.showSuperviseTable = true
      this.showExecuteInfo = true
    })
  }
    if(null!=this.$route.params.xid&&""!=this.$route.params.xid){
      getItsmWorkOrder(this.$route.params.xid).then((res)=>{
        this.form = res.data
        if(null!=res.data.attachPath){
          let arr = res.data.attachPath.split(',')
          for(var i=0;i<arr.length;i++){
            let strs= arr[i].split('/')
              this.fileList.push({
                uid:i,
                name:arr[i],
                tag:strs[strs.length-1],
                status:'done',
                path:arr[i]
              })   
          }
        }
      })
    }
    if(null!=this.$route.params.cpid&&""!=this.$route.params.cpid){
      getItsmWorkOrder(this.$route.params.cpid).then((res)=>{
        this.form = res.data
        this.form.id=undefined
        if(null!=res.data.attachPath){
          let arr = res.data.attachPath.split(',')
          for(var i=0;i<arr.length;i++){
            if(null!=arr[i]&&""!=arr[i]){
              let strs= arr[i].split('/')
              this.fileList.push({
                uid:i,
                name:arr[i],
                tag:strs[strs.length-1],
                status:'done',
                path:arr[i]
              })
            }   
          }
          this.getCurrUserInfoFun()
        }
        
      })
    }  
    if(null!=this.$route.params.dpid&&""!=this.$route.params.dpid){
      
      getItsmWorkOrder(this.$route.params.dpid).then((res)=>{
        this.form = res.data
        this.delayDisabled=true
        if(null!=res.data.attachPath){
          let arr = res.data.attachPath.split(',')
          for(var i=0;i<arr.length;i++){
            if(null!=arr[i]&&""!=arr[i]){
              let strs= arr[i].split('/')
              this.fileList.push({
                uid:i,
                name:arr[i],
                tag:strs[strs.length-1],
                status:'done',
                path:arr[i]
              })
            }   
          }
          this.getCurrUserInfoFun()
          this.rules={
            
            plannedOldTime: [
                { required: true, message: "延期时间不能为空"  },
            ],
            checkUserId: [
                { required: true, message: "延期审核人不能为空"  },
            ],
            
          
          }
        }
        
      })
    }
   
  },
  created () {
    
  },
  computed: {},
  watch: {
    'form.executeResult'(val) {
    this.updateWorkStatus()
  }
  },
  mounted () {},
  methods: {
    dlAttach(id,name){
      downloadAttchment(id,name)
    },
    updateWorkStatus() {
    if (this.form.executeResult === '1') {
      this.workStatus = '已完成'
    } else {
      this.workStatus = '工作中'
    }
  },
    changeCheckUser(e){
      for(var i=0;i<this.yqUserList.length;i++){
        if(this.yqUserList[i].id==e){
          this.form.checkUser=this.yqUserList[i].nickName
        }
      }
    },
    updateDelay(){
      
      setDelay(this.form.id,this.form.checkUserId,this.form.checkUser,this.form.plannedOldTime).then((res)=>{
        this.delayModelShowHide=false
        this.$multiTab.closeCurrentPage()
      })
    },
    closeThisWin(){
      this.$multiTab.closeCurrentPage()
    },
    initFun(){
      this.reset()
      this.getCurrUserInfoFun()
      let xd = new Date()
      this.form.addTime=moment().format('YYYY-MM-DD HH:mm:ss')
      getInitData('response_status,audit_result,audit_status,execute_status,job_status,supervise_type').then(response => {
        this.responseStatusOptions = response.data.response_status
        this.checkResultOptions = response.data.audit_result
        this.checkStatusOptions = response.data.audit_status
        this.executeStatusOptions = response.data.execute_status
        this.statusOptions = response.data.job_status
        this.superviseTypeOptions = response.data.supervise_type
      })
      getUserList4gzdb().then((res)=>{
        this.usersList = res.data
      })
      listDbUsers().then((res)=>{
        this.dbUserList = res.data
      })
      listYqshUsers().then((res)=>{
      this.yqUserList = res.data
    })
    },
    changeWorkUser(e){
      this.form.workUserId=e
      for(var i=0;i<this.usersList.length;i++){
        if(this.usersList[i].id==e){
          this.form.workUser=this.usersList[i].nickName
        }
      }
      console.error(this.form)
    },
    delAttach(index){
      this.fileList.splice(index,1)
    },
    onClose () {
      this.open = false
      this.reset()
      this.$emit('close')
    },
    // 取消按钮
    cancel () {
      this.open = false
      this.reset()
      this.$emit('close')
    },
    // 表单重置
    reset () {
      this.fileList=[],
      this.showExecuteInfo = false
      this.showSuperviseTable = false
      this.superviseHistoryList = []
      this.form = {
        id: undefined,
        mainId: undefined,

        no: undefined,

        responseStatus: undefined,

        content: undefined,

        superviseUser: undefined,

        superviseContent: undefined,

        superviseTime: undefined,

        workUser: undefined,

        workUserId: undefined,

        plannedStartTime: undefined,

        plannedEndTime: undefined,

        plannedOldTime: undefined,

        addUser: undefined,

        addUserId: undefined,

        addUnit: undefined,

        addUnitId: undefined,

        addTime: undefined,

        taskUser: undefined,

        taskUserId: undefined,

        flowNodeName: undefined,

        checkId: undefined,

        checkUserId: undefined,

        checkUser: undefined,

        checkUnitId: undefined,

        checkUnit: undefined,

        checkContent: undefined,

        checkResult: undefined,

        checkStatus: undefined,

        checkTime: undefined,

        executeId: undefined,

        executeResult: undefined,

        executeStatus: undefined,

        startTime: undefined,

        endTime: undefined,

        executeContent: undefined,

        executeUser: undefined,

        executeUserId: undefined,

        executeTime: undefined,

        fallbackMsg: undefined,

        timeoutMsg: undefined,

        status: '0',
        attachPath: undefined,

      }
    },
    getCurrUserInfoFun(){
      
        getInfo().then((res)=>{
          this.form.addUser=res.user.name
          this.form.addUnit=res.user.sysDept.deptName
          this.form.addUserId=res.user.id
          this.form.addUnitId=res.user.sysDept.id
        })
        
      
    },
    handleBeforeUpload(file) {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('attachType', 'attach')
      const url = process.env.VUE_APP_BASE_API + '/api/attach/upload'
      let access_token = localStorage.getItem('access_token')
      access_token = access_token.replace(/^"|"$/g, '')
      const config = {
        headers: {
          'Content-Type': 'multipart/form-data; boundary=----WebKitFormBoundaryHZA0jlEc5lVdHtye',
          Authorization: 'Bearer ' + access_token,
        },
      }
      axios.post(url, formData, config).then((ures) => {
          if(ures.data.code==500){
            this.$notification.error({
              message: '上传失败',
              description: ``
            })
            return 
          }
          this.$notification.success({
            message: '上传成功',
            description: ``
          })
        ures.data.tag=ures.data.name
        this.fileList.push(ures.data)
      })
      return false
    },
    /** 新增按钮操作 */
    handleAdd () {
      this.reset()      
      this.open = true
      this.formTitle = '添加我创建的工作'
    },
    /** 复制按钮操作 */
    handleCopy (id) {
      this.formTitle = '复制我创建的工作'
      this.reset()
      this.open = true
      this.spinning = !this.spinning
      const itsmWorkOrderId = id
      getItsmWorkOrder(itsmWorkOrderId).then(response => {
        this.form = response.data
        this.form.id = undefined
        this.spinning = !this.spinning
      })
    },
    /** 修改按钮操作 */
    handleUpdate (row) {
      this.reset()
      this.open = true
      this.spinning = !this.spinning
      const itsmWorkOrderId = row.id
      getItsmWorkOrder(itsmWorkOrderId).then(response => {
        this.form = response.data
        this.formTitle = '修改我创建的工作'
        this.spinning = !this.spinning
      })
    },

     nextStep(){
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.attachPath=''
          for(var i=0;i<this.fileList.length;i++){
            this.form.attachPath = this.form.attachPath +"," + this.fileList[i].path
          }
          
          this.getCurrUserInfoFun()
          const saveForm = JSON.parse(JSON.stringify(this.form))
          
          if (this.form.id !== undefined) {
            updateItsmWorkOrder(saveForm).then(response => {
              this.$message.success('更新成功', 3)              
              this.nextStep2ChangeStatus()
            })
          } else {
          addItsmWorkOrder(saveForm).then(response => {              
              this.$message.success('新增成功', 3)
              this.form = response.data
              this.nextStep2ChangeStatus()
            })
          }
          
        } else {
          this.disabled = false
          return false
        }
      })
    },

    nextStep2ChangeStatus(){
     
        let statusStr = ''
        for(var j=0;j<this.responseStatusOptions.length;j++){
          if(this.responseStatusOptions[j].dictLabel=='已接单'){
            statusStr = this.responseStatusOptions[j].dictValue
          }            
        }
         changeResponseStatus(this.form.id,statusStr).then((res)=>{
          this.$multiTab.closeCurrentPage()
        })
    },

     nextStep2User(){
      
      
    },
    /** 提交按钮 */
    submitForm: function (closeDialog) {
      
      this.$refs.form.validate(valid => {
        if (valid) {
          this.form.attachPath=''
          for(var i=0;i<this.fileList.length;i++){
            if(''==this.form.attachPath){
              this.form.attachPath =this.fileList[i].path
            }else{
              this.form.attachPath = this.form.attachPath +"," + this.fileList[i].path
            }
          }
          this.getCurrUserInfoFun()
          const saveForm = JSON.parse(JSON.stringify(this.form))
          
          if (this.form.id !== undefined) {
            updateItsmWorkOrder(saveForm).then(response => {
              this.$message.success('更新成功', 3)             
              this.$multiTab.closeCurrentPage()
            })
          } else {
            addItsmWorkOrder(saveForm).then(response => {
              this.$message.success('新增成功', 3)             
              this.$multiTab.closeCurrentPage()
            })
          }
        } else {
          this.disabled = false
          return false
        }
      })
    },
    back () {
      const index = '/workOrder/itsmworkorder/index'
      this.$router.push(index)
    }

  }
}
</script>
<style lang="less" scoped>

      /deep/  .ant-form-item-label {
        width: 100px;
      }
      
</style>
