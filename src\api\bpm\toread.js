import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/toread',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/toread/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/toread',
    method: 'put',
    data
  })
}

export function getList(pers) {
  return request({
    url: 'api/toread',
    method: 'get',
    params: pers
  })
}

export function getById(id) {
  return request({
    url: 'api/queryById?id=' + id,
    method: 'get'
  })
}

export default { add, edit, del, getList }
