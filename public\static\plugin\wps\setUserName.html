<html>
<head>
	<meta http-equiv="Content-Type" content="text/html" ;>
	<meta charset="utf-8">
	<title>OA助手WPS用户名设置</title>
	<script type="text/javascript" src="js/common/enum.js"></script>
	<script type="text/javascript">
		function changeUserName(){
			var l_Name = document.getElementById("txtUserName").value
			if (l_Name === "") {
				alert("请输入有效的用户名称");
				return;
			}
			wps.PluginStorage.setItem(constStrEnum.WPSInitUserName, l_Name);
			wps.UpdateRibbon("btnUserName")
			//wps.WpsApplication().ActiveDocument.Activate();
			window.close();
		}
	</script>
</head>

<body>
	<p>请在下面输入WPS Office的默认用户名称:</p>
	<hr>
	<form method="POST" action="">
		<p>WPS用户名:<input type="text" id="txtUserName" size="20"></p>
		<p><input id="btnSet" type="button" value="确认" name="B1" onclick="changeUserName()"></p>
	</form>
</body>

</html>