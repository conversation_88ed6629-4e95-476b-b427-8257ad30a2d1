//问题统计
//问题统计工作台 实现现按周期统计分析问题工单情况
// http://localhost:8183/system/itsmProblem/wtgdqk post
import request from '@/utils/request'

//问题统计分析--》 问题工单情况（旧）
export function itsmProblemWtgdqk(data) {
    return request({
      url: '/system/itsmProblem/wtgdqk',
      method: 'post', 
      data: data
    })
  }

//问题统计分析--》 问题工单情况（新）、问题分类情况（新）  
export function itsmProblemGetOrderConditions(data) {
    return request({
      url: '/system/itsmProblem/analysis/getOrderConditions',
      method: 'post', 
      data: data
    })
  }
  
  //问题统计分析--》 问题分类情况（旧）
  //统计问题分类程序问题、功能问题工单数数及环比
  //http://localhost:8183/system/itsmProblem/wtfltjhb post
  
  export function itsmProblemWtfltjhb(data) {
    return request({
      url: '/system/itsmProblem/wtfltjhb',
      method: 'post',
      data: data
    })
  }
  //实现按周期统计问题工单总情况的问题处理情况占比
  // http://localhost:8183/system/itsmProblem/wtclqk  
  export function itsmProblemWtclqk(data) {
    return request({
      url: '/system/itsmProblem/wtclqk',
      method: 'post',
      data: data
    })
  }
  
  // 实现按周期统计问题工单总情况的问题处理情况趋势分析。
  // http://localhost:8183/system/itsmProblem/wtclqkqs  
  export function itsmProblemWtclqkqs(data) {
    return request({
      url: '/system/itsmProblem/wtclqkqs',
      method: 'post',
      data: data
    })
  }
   
  // 实现按周期统计分析问题分类，分析问题分类总情况占比（旧）
  // http://localhost:8183/system/itsmProblem/wtflzqkzb 
  export function itsmProblemWtflzqkzb(data) {
    return request({
      url: '/system/itsmProblem/wtflzqkzb',
      method: 'post',
      data: data
    })
  }
  
  // 实现按周期统计分析问题分类，分析问题分类总情况趋势分析（旧）
  // http://localhost:8183/system/itsmProblem/wtflzqkqs post
  export function itsmProblemWtflzqkqs(data) {
    return request({
      url: '/system/itsmProblem/wtflzqkqs',
      method: 'post',
      data: data
    })
  }
  // 问题分类总情况、问题分类总趋势（新）
  export function itsmProblemGetTypeConditions(data) {
    return request({
      url: '/system/itsmProblem/analysis/getTypeConditions',
      method: 'post',
      data: data
    })
  }
    
  // 分析程序问题占比情况
  // http://localhost:8183/system/itsmProblem/cxwtzb
  // post
  export function itsmProblemCxwtzb(data) {
    return request({
      url: '/system/itsmProblem/cxwtzb',
      method: 'post',
      data: data
    })
  }
  
  // 分析程序问题趋势分析
  // http://localhost:8183/system/itsmProblem/cxwtqs
  // post
  export function itsmProblemCxwtqs(data) {
    return request({
      url: '/system/itsmProblem/cxwtqs',
      method: 'post',
      data: data
    })
  }
  
  // 分析功能问题占比情况
  // http://localhost:8183/system/itsmProblem/gnwtzb
  // post
  export function itsmProblemGnwtzb(data) {
    return request({
      url: '/system/itsmProblem/gnwtzb',
      method: 'post',
      data: data
    })
  }
  // 分析功能问题趋势分析
  // http://localhost:8183/system/itsmProblem/gnwtqs
  // post
  export function itsmProblemGnwtqs(data) {
    return request({
      url: '/system/itsmProblem/gnwtqs',
      method: 'post',
      data: data
    })
  }
  
  // 问题处理情况占比（旧）
  // 实现按周期统计分析工单总情况已解决（统计规则：取已关闭的工单）、未解决（统计规则：取未关闭的工单）的占比情况
  // http://localhost:8183/system/itsmProblem/gdzqkzb
  // post
  export function itsmProblemGdzqkzb(data) {
    return request({
      url: '/system/itsmProblem/gdzqkzb',
      method: 'post',
      data: data
    })
  }

  // 问题工单量趋势情况（旧）
  // http://localhost:8183/system/itsmProblem/wtgdlqsqk
  // post
  export function itsmProblemWtgdlqsqk(data) {
    return request({
      url: '/system/itsmProblem/wtgdlqsqk',
      method: 'post',
      data: data
    })
  }

    // 问题处理情况占比、问题工单量趋势（新）
  export function itsmProblemGetOrderOverConditions(data) {
    return request({
        url: '/system/itsmProblem/analysis/getOrderOverConditions',
        method: 'post',
        data: data
      })
  }

  // 实现按周期统计分析问题来源占比。(旧)
  // http://localhost:8183/system/itsmProblem/wtlyzb
  // post
  export function itsmProblemWtlyzb(data) {
    return request({
      url: '/system/itsmProblem/wtlyzb',
      method: 'post',
      data: data
    })
  }
  // 实现按周期统计分析问题来源趋势。（旧）
  // http://localhost:8183/system/itsmProblem/wtlyqs
  // post
  export function itsmProblemWtlyqs(data) {
    return request({
      url: '/system/itsmProblem/wtlyqs',
      method: 'post',
      data: data
    })
  }

  // 问题来源统计分析（新）
  export function itsmProblemGetSourceConditions(data) {
    return request({
      url: '/system/itsmProblem/analysis/getSourceConditions',
      method: 'post',
      data: data
    })
  }
  // 实现按周期统计分析未超时、已超时未处理超时的问题工单占比情况。
  // http://localhost:8183/system/itsmProblem/wtcsqkfx
  // post
  export function itsmProblemWtcsqkfx(data) {
    return request({
      url: '/system/itsmProblem/wtcsqkfx',
      method: 'post',
      data: data
    })
  }
  // 实现按周期统计分析问题登记人TOPN  问题登记人TOPN(旧)
  // http://localhost:8183/system/itsmProblem/wtdjr
  // post
  export function itsmProblemWtdjr(data) {
    return request({
      url: '/system/itsmProblem/wtdjr',
      method: 'post',
      data: data
    })
  }
  
  //问题处理及时率（新）
  export function itsmProblemGetTimeOutConditions(data) {
    return request({
      url: '/system/itsmProblem/analysis/getTimeOutConditions',
      method: 'post',
      data: data
    })
  }

  //问题登记人TOPN（新）
  export function itsmProblemGetRegisterUserTop(data) {
    return request({
      url: '/system/itsmProblem/analysis/getRegisterUserTop',
      method: 'post',
      data: data
    })
  }
  //问题处理人（新）
  export function itsmProblemGetHandlerTop(data) {
    return request({
      url: '/system/itsmProblem/analysis/getHandlerTop',
      method: 'post',
      data: data
    })
  }
  //问题登记单位TOPN（新）
  export function itsmProblemGetHandleUnitTop(data) {
    return request({
      url: '/system/itsmProblem/analysis/getHandleUnitTop',
      method: 'post',
      data: data
    })
  }
  // 实现按周期统计分析问题处理人TOPN，N自定义（5、10、15、20）。
  // http://localhost:8183/system/itsmProblem/wtclr
  // post
  export function itsmProblemWtclr(data) {
    return request({
      url: '/system/itsmProblem/wtclr',
      method: 'post',
      data: data
    })
  }
  
  // 实现按周期统计分析问题登记单位TOPN，N自定义（5、10、15、20）。
  // http://localhost:8183/system/itsmProblem/wtdjdw
  // post
  
  export function itsmProblemWtdjdw(data) {
    return request({
      url: '/system/itsmProblem/wtdjdw',
      method: 'post',
      data: data
    })
  }