import request from '@/utils/request'

// 查询工作列表
export function listitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/list',
    method: 'get',
    params: query
  })
}

export function list4excel (query) {
  return request({
    url: '/system/itsmOperationOrder/list/excel',
    method: 'get',
    params: query
  })
}

// 查询我创建的待办工作列表
export function pendingListitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/pendinglist',
    method: 'get',
    params: query
  })
}

// 查询本人填写和负责的工作计划列表
export function myListitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/mylist',
    method: 'get',
    params: query
  })
}

// 查询本人待审核的工作计划列表
export function mycheckListitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/mychecklist',
    method: 'get',
    params: query
  })
}

// 查询我创建的工作详细
export function getitsmOperationOrder (id) {
  return request({
    url: '/system/itsmOperationOrder/' + id,
    method: 'get'
  })
}

// 新增我创建的工作
export function additsmOperationOrder (data) {
  return request({
    url: '/system/itsmOperationOrder',
    method: 'post',
    data: data
  })
}

// 修改我创建的工作
export function updateitsmOperationOrder (data) {
  return request({
    url: '/system/itsmOperationOrder',
    method: 'put',
    data: data
  })
}

// 删除我创建的工作
export function delitsmOperationOrder (id) {
  return request({
    url: '/system/itsmOperationOrder/' + id,
    method: 'delete'
  })
}

// // 导出外协人员情况表
// export function exportUesWxryqkb (query) {
//   return request({
//     url: '/system/uesWxryqkb/export',
//     method: 'get',
//     params: query
//   })
// }

// 导出我创建的工作
export function exportitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/workOrder/itsmWorkOrder/getInitData/' + dictTypes,
    method: 'get'
  })
}

export function getUserList4gzdb (query) {
  return request({
    url: '/system/itsmOperationOrder/getUserList4gzdb',
    method: 'get'
  })
}


export function changeResponseStatus (id,status) {
  return request({
    url: '/system/itsmOperationOrder/changeResponseStatus?id='+id +"&status="+status,
    method: 'get'
  })
}

export function listMyitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/mylist',
    method: 'get',
    params: query
  })
}

export function listDbUsers () {
  return request({
    url: '/system/itsmOperationOrder/listDbUsers',
    method: 'get',
  })
}

export function addSupervise (data) {
  return request({
    url: '/system/itsmOperationOrder/addSupervise',
    method: 'post',
    data: data
  })
}

export function getSuperviseHistory (id) {
  return request({
    url: '/system/itsmOperationOrder/getSuperviseHistory?id='+id,
    method: 'get',
  })
}

export function setDelay (id,checkUserId,checkUser,delayDate) {
  return request({
    url: '/system/itsmOperationOrder/setDelay?id='+id+"&userId="+checkUserId+"&userName="+checkUser+"&delayDate="+delayDate,
    method: 'get',
  })
}

export function listYqshUsers () {
  return request({
    url: '/system/itsmOperationOrder/listYqshUsers',
    method: 'get',
  })
}

export function listDelayitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/delaylist',
    method: 'get',
    params: query
  })
}

export function setCheck (data) {
  return request({
    url: '/system/itsmOperationOrder/setCheck',
    method: 'post',
    data: data
  })
}

export function myExportitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/myExport',
    method: 'get',
    params: query
  })
}

export function delayExportitsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/delayExport',
    method: 'get',
    params: query
  })
}

export function zyjhfzrList () {
  return request({
    url: '/system/user/userListByRoleKey/zyjhfzr',
    method: 'get'
  })
}

export function zyjhywsh () {
  return request({
    url: '/system/user/userListByRoleKey/zyjhywsh',
    method: 'get'
  })
}

export function gotoNextStepBatch (data) {
  return request({
    url: '/api/bpm/runtime/operation/gotoNextStepBatch',
    method: 'post',
    data: data
  })
}

export function gotoNextStepBatch2 (data) {
  return request({
    url: '/api/bpm/runtime/operation/gotoNextStepBatch',
    method: 'put',
    data: data
  })
}

export function statByStatus (startDate,endDate) {
  return request({
    url: '/system/itsmOperationOrder/statByStatus?startDate=' + startDate+"&endDate="+endDate,
    method: 'get'
  })
}

export function statByResult (startDate,endDate) {
  return request({
    url: '/system/itsmOperationOrder/statByResult?startDate=' + startDate+"&endDate="+endDate,
    method: 'get'
  })
}

export function statByExecution (startDate,endDate) {
  return request({
    url: '/system/itsmOperationOrder/statByExecution?startDate=' + startDate+"&endDate="+endDate,
    method: 'get'
  })
}

export function statByTimeOut (startDate,endDate) {
  return request({
    url: '/system/itsmOperationOrder/statByTimeOut?startDate=' + startDate+"&endDate="+endDate,
    method: 'get'
  })
}

export function getbpmlog (id) {
  return request({
    url: '/api/bpm/runtime/operation/getbpmlog/' + id,
    method: 'get'
  })
}