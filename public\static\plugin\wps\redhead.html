<html>
<meta charset="utf-8">
<title>套用红头</title>
</head>

<body onload="getAllTemplelists()">
    <script type="text/javascript" src='js/main.js'></script>
    <script type="text/javascript" src="./otherslib/lib/jquery.min.js"></script>
    <select id="templates" size=8 style='width:360px'>
        <!--这里ajax动态拉取。-->
    </select>
    <br />
    <br />
    <br />
    <div style="text-align: center;">
        <button type="button" onClick="cancel()">取消</button>
        <button type="button" onClick="selectTemplate()">确定</button>
        <!--  <button type="button" onClick="request()">请求</button> -->
    </div>
</body>

</html>

<script>

    function request() {
        // var wpsApp = wps.WpsApplication();
        // var activeDoc = wpsApp.ActiveDocument;
        // const a = GetDocParamsValue(activeDoc, constStrEnum.InsertRedHeaderFlag);
        // const b = GetDocParamsValue(activeDoc, constStrEnum.ToOFDFlag);
        // console.log(a,b)

        // var xmlhttp = new XMLHttpRequest();
        // xmlhttp.onreadystatechange = function () {
        //     //当接受到响应时回调该方法
        //     if (xmlhttp.readyState == 4 && (xmlhttp.status == 200 || xmlhttp.status == 0)) {
        //         console.log(xmlhttp)
        //     }
        // }
        // var wpsApp = wps.WpsApplication();
        // var activeDoc = wpsApp.ActiveDocument;

        // var totalPath = "http://mss.ecwcone.com:3000/ecw/api/fileInfo/fileById/1387347336122941441";
        // var token = GetDocParamsValue(activeDoc, constStrEnum.Token);
        // xmlhttp.open("get", totalPath, true); //以POST方式请求该接口
        // xmlhttp.setRequestHeader("Authorization",token);
        // xmlhttp.setRequestHeader("Content-Type","application/json;charset=UTF-8"); //添加Content-type
        // xmlhttp.send(); //发送请求参数间用&分割


        // let params={
        //     type:'executeFunc1',
        //     message:"当前时间为：" + currentTime()
        // }
        /**
         * WebNotify:
         * 参数1：发送给业务系统的消息
         * 参数2：是否将消息加入队列，是否防止丢失消息，都需要设置为true
         */
        // wps.OAAssist.WebNotify(JSON.stringify(params),true);


    }

    function selectTemplate() {
        var wpsApp = wps.WpsApplication();
        var obj = document.getElementById("templates");
        var index = obj.selectedIndex;
        if (index == -1) { //添加未选中数据时的异常处理
            alert("请先选择红头文件后再进行套红头！");
            return;
        }

        var wpsApp = wps.WpsApplication();
        var activeDoc = wpsApp.ActiveDocument;
        var token = GetDocParamsValue(activeDoc, constStrEnum.Token);

        var xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function () {
            //当接受到响应时回调该方法
            if (xmlhttp.readyState == 4 && (xmlhttp.status == 200 || xmlhttp.status == 0)) {
                const resObj = JSON.parse(xmlhttp.responseText);

                var path = OA_DOOR.basePath + '/api/office/download/' + resObj.storageId + '?token=' + token;
                // 这几个参数是为了套红后，另存为的红头文件，草稿文件就不能动了
                SetDocParamsValue(activeDoc, "insertFileUrl", path);
                SetDocParamsValue(activeDoc, "bkInsertFile", "BodyContent");
                SetDocParamsValue(activeDoc, constStrEnum.uploadFileName, "official.docx");
                SetDocParamsValue(activeDoc, constStrEnum.uploadPath, GetDocParamsValue(activeDoc, constStrEnum.redUploadPath));
                InsertRedHeadDoc(activeDoc);

                window.opener = null;
                window.open('', '_self', '');
                window.close();

            }
        }
        var redId = obj.options[index].getAttribute("value");
        var totalPath = OA_DOOR.basePath + "/api/templateOffice/queryById?id=" + redId;

        xmlhttp.open("get", totalPath, true); //以POST方式请求该接口
        xmlhttp.setRequestHeader("Authorization", token);
        xmlhttp.setRequestHeader("Content-Type", "application/json;charset=UTF-8"); //添加Content-type
        xmlhttp.send(); //发送请求参数间用&分割


    }

    function cancel() {
        window.opener = null;
        window.open('', '_self', '');
        window.close();
    }

    function getAllTemplelists() {
        var wpsApp = wps.WpsApplication();
        var activeDoc = wpsApp.ActiveDocument;
        var token = GetDocParamsValue(activeDoc, constStrEnum.Token);

        var TempInfo = GetDocParamsValue(activeDoc, constStrEnum.TempInfo);

        resultJson = TempInfo;  //JSON.parse(strData);
        for (var i = 0; i < resultJson.length; i++) {
            var element = resultJson[i]
            var myOption = document.createElement("option"); //动态创建option标签
            var suffix = element.tempName.split('.')[1];

            myOption.value = element.tempId; //红头文档id
            myOption.text = element.tempName; //红头文档名称
            templates.add(myOption);
        }

    }

    function search() {
        var xmlhttp = new XMLHttpRequest();
        xmlhttp.onreadystatechange = function () {
            //当接受到响应时回调该方法
            if (xmlhttp.readyState == 4 && (xmlhttp.status == 200 || xmlhttp.status == 0)) {
                var text = xmlhttp.responseText; //使用接口返回内容，响应内容
                var resultJson = JSON.parse(text) //将json字符串转换成对象
                templates.options.length = 0;
                for (var i = 0; i < resultJson.length; i++) {
                    var element = resultJson[i]
                    var myOption = document.createElement("option"); //动态创建option标签
                    myOption.value = element.tempId; //红头文档id
                    myOption.text = element.tempName; //红头文档名称
                    templates.add(myOption);
                }
            }
        }

        var searchPath = wps.PluginStorage.getItem("searchRedHeadPath") || OA_DOOR.redHeadsPath

        var totalPath = searchPath + "?content=" + document.getElementById("content").value;

        xmlhttp.open("get", totalPath, true); //以POST方式请求该接口
        xmlhttp.setRequestHeader("Content-type",
            "application/x-www-form-urlencoded;charset=UTF-16LE"); //添加Content-type
        xmlhttp.send(); //发送请求参数间用&分割
    }

</script>