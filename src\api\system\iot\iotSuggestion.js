import request from '@/utils/request'

// 查询意见建议列表
export function listIotSuggestion (query) {
  return request({
    url: '/system/iotSuggestion/list',
    method: 'get',
    params: query
  })
}

// 查询意见建议详细
export function getIotSuggestion (id) {
  return request({
    url: '/system/iotSuggestion/' + id,
    method: 'get'
  })
}

// 新增意见建议
export function addIotSuggestion (data) {
  return request({
    url: '/system/iotSuggestion',
    method: 'post',
    data: data
  })
}

// 修改意见建议
export function updateIotSuggestion (data) {
  return request({
    url: '/system/iotSuggestion',
    method: 'put',
    data: data
  })
}

// 删除意见建议
export function delIotSuggestion (id) {
  return request({
    url: '/system/iotSuggestion/' + id,
    method: 'delete'
  })
}

// 导出意见建议
export function exportIotSuggestion (query) {
  return request({
    url: '/system/iotSuggestion/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/iotSuggestion/getInitData/' + dictTypes,
    method: 'get'
  })
}
