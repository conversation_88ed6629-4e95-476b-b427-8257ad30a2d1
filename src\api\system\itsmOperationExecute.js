import request from '@/utils/request'

// 查询工作计划执行表列表
export function listItsmOperationExecute (query) {
  return request({
    url: '/system/itsmOperationExecute/list',
    method: 'get',
    params: query
  })
}

// 查询工作计划执行表详细
export function getItsmOperationExecute (result) {
  return request({
    url: '/system/itsmOperationExecute/' + result,
    method: 'get'
  })
}

// 新增工作计划执行表
export function addItsmOperationExecute (data) {
  return request({
    url: '/system/itsmOperationExecute',
    method: 'post',
    data: data
  })
}

// 修改工作计划执行表
export function updateItsmOperationExecute (data) {
  return request({
    url: '/system/itsmOperationExecute',
    method: 'put',
    data: data
  })
}

// 删除工作计划执行表
export function delItsmOperationExecute (result) {
  return request({
    url: '/system/itsmOperationExecute/' + result,
    method: 'delete'
  })
}


// 导出工作计划执行表
export function exportItsmOperationExecute (query) {
  return request({
    url: '/system/itsmOperationExecute/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmOperationExecute/getInitData/' + dictTypes,
    method: 'get'
  })
}
