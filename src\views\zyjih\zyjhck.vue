<template>
  <div>
    <a-card :bordered="false" style="margin-bottom: 10px;">
      <!-- 条件搜索 -->
      <div class="table-page-search-wrapper">
        <a-form :labelCol="labelCol" :wrapperCol="wrapperCol" ref="queryForm">
          <a-row :gutter="24">
            <a-col :span="8" >
              <a-form-item label="作业计划编号"  >
                <a-input v-model="queryParam.operationNo" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery"/>
              </a-form-item>            
            </a-col>
            
            <a-col :span="8" >
              <a-form-item label="作业主题"  >
                <a-input v-model="queryParam.systemName" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery"/>
              </a-form-item>          
            </a-col>
            <a-col :span="8">
            <a-form-model-item  label="作业类型"      >
              <a-select v-model="queryParam.plantype"   >
                <a-select-option v-for="(dict, index) in operationPlantypeArr" :key="index" :value="dict.dictValue"> {{ dict.dictLabel }}</a-select-option>
              </a-select>
              
              </a-form-model-item>
            </a-col>
      


        
          </a-row>
          <a-row :gutter="24" v-if="advanced">
            <a-col :span="8">
              <a-form-model-item  label="作业性质"   >                
                <a-select v-model="queryParam.nature" allowClear    >
                  <a-select-option v-for="(item, index) in operationNatureArr" :key="index" :value="item.dictValue" >{{ item.dictLabel }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item  label="作业单位"     >                
                <a-select v-model="queryParam.operationUnit" allowClear      >
                  <a-select-option v-for="(item, index) in operationCompanyArr" :key="index" :value="item.dictLabel" >{{ item.dictLabel }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item  label="作业负责人"   >
            
                <a-select v-model="queryParam.operationUserId " allowClear        >
                  <a-select-option v-for="(item, index) in zyjhfzrListArr" :key="index" :value="item.id" >{{ item.name }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
           
            
          </a-row>
          <a-row :gutter="24" v-if="advanced">
            
            <a-col :span="8">
              <a-form-model-item  label="是否开票"      >            
                <a-select v-model="queryParam.billing" allowClear    >
                  <a-select-option v-for="(item, index) in operationBillingArr" :key="index" :value="item.dictLabel" >{{ item.dictLabel }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          
            <a-col :span="8" >
              <a-form-model-item label="作业对象"     >
                <a-input v-model="queryParam.object" placeholder="请输入工作对象"   />
              </a-form-model-item>
            </a-col>
            <a-col :span="8" >
              <a-form-model-item label="作业内容"    >
                <a-input v-model="queryParam.content"    />
              </a-form-model-item>
            </a-col>
          
          
          </a-row>
          <a-row :gutter="24" v-if="advanced">
            
            <a-col :span="8">
              <a-form-model-item  label="计划开始时间"      >            
                <a-date-picker 
                allowClear 
                v-model="queryParam.plannedStartTimest"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'开始日期'"
                  style="width:47%"
                  
                /> ~
                <a-date-picker  
                allowClear 
                v-model="queryParam.plannedStartTimeed"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'结束日期'"
                  style="width:47%"
                  
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item  label="计划结束时间"      >            
               <a-date-picker 
                allowClear 
                v-model="queryParam.plannedEndTimest"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'开始日期'"
                  style="width:47%"
                  
                /> ~
                <a-date-picker  
                allowClear 
                v-model="queryParam.plannedEndTimeed"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'结束日期'"
                  style="width:47%"
                  
                />
              </a-form-model-item>
            </a-col>
                <a-col :span="8" >
                  <a-form-model-item label="作业状态"    >
                    <a-select v-model="queryParam.status" allowClear    >
                      <a-select-option  value="1" >计划中</a-select-option>
                      <a-select-option  value="2" >延期中</a-select-option>
                      <a-select-option  value="3" >已完成</a-select-option>
                    </a-select>
                  </a-form-model-item>
                </a-col>
          
          
          </a-row>
          <a-row :gutter="24" v-if="advanced" >
           <a-col :span="8" >
            <a-form-model-item label="当前环节"    >
              <a-select v-model="queryParam.flowNodeName" allowClear    >
                <a-select-option  value="填报" >填报</a-select-option>
                <a-select-option  value="业务人员审核" >业务人员审核</a-select-option>
                <a-select-option  value="领导审核" >领导审核</a-select-option>
                <a-select-option  value="执行" >执行</a-select-option>
                <a-select-option  value="已关闭" >已关闭</a-select-option>
              </a-select>
            </a-form-model-item>
           </a-col>
           <a-col :span="8" >
            <a-form-model-item label="超时状态"    >
              <a-select v-model="queryParam.timeoutStatus" allowClear    >
                <a-select-option  value="未超时" >未超时</a-select-option>
                <a-select-option  value="即将超时" >即将超时</a-select-option>
                <a-select-option  value="已超时" >已超时</a-select-option>
              </a-select>
            </a-form-model-item>
           </a-col>
           <a-col :span="8" >
            <a-form-model-item label="作业结果"    >
              <a-select v-model="queryParam.executeResult" allowClear    >
                <a-select-option  value="001" >完成</a-select-option>
                <a-select-option  value="002" >取消</a-select-option>
                <a-select-option  value="003" >失败</a-select-option>
              </a-select>
            </a-form-model-item>
           </a-col>
          </a-row>
          <a-row :gutter="24" v-if="advanced" >
            <a-col :span="8" >
              <a-form-model-item label="作业执行情况说明"    >
                <a-input   v-model="queryParam.executeContent"   />
              </a-form-model-item>
            </a-col>
            <a-col :span="8" >
              <a-form-model-item label="审核结果"    >
                <a-select v-model="queryParam.checkResult" allowClear    >
                  <a-select-option  value="通过" >通过</a-select-option>
                  <a-select-option  value="不通过" >不通过</a-select-option>
              
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item  label="实际开始时间"      >            
                <a-date-picker 
                allowClear 
                v-model="queryParam.startTimest"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'开始日期'"
                  style="width:47%"
                  
                /> ~
                <a-date-picker  
                allowClear 
                v-model="queryParam.startTimeed"
                  format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'结束日期'"
                  style="width:47%"
                  
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24" v-if="advanced" >
            <a-col :span="8">
              <a-form-model-item  label="实际结束时间"      >            
                <a-date-picker 
                allowClear 
                v-model="queryParam.endTimest"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'开始日期'"
                  style="width:47%"
                  
                /> ~
                <a-date-picker  
                allowClear 
                v-model="queryParam.endTimeed"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'结束日期'"
                  style="width:47%"
                  
                />
              </a-form-model-item>
            </a-col>
             <a-col :span="8">

              <a-form-model-item  label="执行操作时间"      >            
                <a-date-picker 
                allowClear 
                v-model="queryParam.executeOperationTimest"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'开始日期'"
                  style="width:47%"
                  
                /> ~
                <a-date-picker  
                allowClear 
                v-model="queryParam.executeOperationTimeed"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'结束日期'"
                  style="width:47%"
                  
                />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item  label="填报人"      > 
              <a-input v-model="queryParam.addUser"   />        
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24" v-if="advanced" >
            <a-col :span="8">
              <a-form-model-item  label="填报单位"      > 
              <a-input v-model="queryParam.addUnit"   />        
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item  label="审核人"      > 
              <a-input v-model="queryParam.checkUser"   />        
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item  label="审核时间"      >            
                <a-date-picker 
                allowClear 
                v-model="queryParam.checkTimest"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'开始日期'"
                  style="width:47%"
                  
                /> ~
                <a-date-picker  
                allowClear 
                v-model="queryParam.checkTimeed"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'结束日期'"
                  style="width:47%"
                  
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24" v-if="advanced" >
            <a-col :span="8">
              <a-form-model-item label="审核说明"   >
                <a-input  v-model="queryParam.checkContent"   />
              </a-form-model-item>
            </a-col>
            <a-col :span="8">
              <a-form-model-item  label="填报时间"      >            
                <a-date-picker 
                allowClear 
                v-model="queryParam.addTimest"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'开始日期'"
                  style="width:47%"
                  
                /> ~
                <a-date-picker  
                allowClear 
                v-model="queryParam.addTimeed"
                  format="YYYY-MM-DD HH:mm:ss"
                   value-format="YYYY-MM-DD HH:mm:ss"
                  :placeholder="'结束日期'"
                  style="width:47%"
                  
                />
              </a-form-model-item>
            </a-col>
          </a-row>
          <a-row :gutter="24">
            <a-col>
              <span class="table-page-search-submitButtons" style="float: right;">
                <a-button type="primary" @click="handleQuery"><a-icon type="search" />查询</a-button>
                <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false" class="table-card">
    
      <advance-table
        title="作业计划查询"
        :pagination="{
          current: queryParam.pageNum,
          pageSize: queryParam.pageSize,
          total: total,
          showSizeChanger: true,
          showLessItems: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，总计 ${total} 条`,
          onChange: changeSize,
          onShowSizeChange: onShowSizeChange
        }"
        tableKey="base-itsmWorkOrder-index-table"
        @change="handleTableChange"
        rowKey="id"
        size="middle"
        @refresh="getList"
        :columns="columns"
        :data-source="itsmWorkOrderList"
        :loading="loading"
        :format-conditions="true"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <div  class="table-operations" slot="button" style="float:left;">
         
  
     
          <a-button type="" @click="handleExport" >
            <a-icon type="download" />导出
          </a-button>

          <a-button v-if="!multiple" type="primary" @click="copyitem" >
            <a-icon type="download" />复制
          </a-button>
        </div>

        <span slot="responseStatus" slot-scope="{record}">
          {{ responseStatusFormat(record) }}
        </span>
        <span slot="checkResult" slot-scope="{record}">
          {{ checkResultFormat(record) }}
        </span>
        <span slot="fmtcheckStatus" slot-scope="{record}">
          {{ checkStatusFormat(record) }}
        </span>
        <span slot="executeStatus" slot-scope="{record}">
          {{ executeStatusFormat(record) }}
        </span>
        <span slot="fmtstatus" slot-scope="{record}">
          {{ statusFormat(record) }}   
        </span>
        <span slot="fmtTimeOutStatus" slot-scope="{record}">
          {{ timeoutStatusFormat(record) }}  
        </span>
        <span slot="fmtexecuteResult" slot-scope="{record}">
          {{ executeResultFormat(record) }}
        </span>
        <span slot="fmtSuperviseContent" slot-scope="{record}">
          {{ superviseContentFormat(record) }}  
        </span>
        
        <span slot="transcrd" slot-scope="{record}">
          {{ trancr(record) }}  
        </span>
        <span slot="transPlanType" slot-scope="{record}">
          {{ transTypePlan(record) }}  
        </span>
        <span slot="bpm" slot-scope="{text,record}">
          <a @click="handleJump2Edit(record)" style="color:#2f54eb;">
           {{text}}
          </a>
        </span>
        <span slot="fmtopuser" slot-scope="{text,record}">
          
           {{ fmatopuser(record) }}
         
        </span>
        <!-- <span slot="bpm" slot-scope="{text, record}">
          <a @click="handleBpm(record)" v-hasPermi="['system:itsmWorkOrder:edit']">
            {{text}}
          </a>
        </span> -->
        <span slot="operation" slot-scope="{text, record}">
          
          <a @click="handleDelete(record)" v-hasPermi="['zyjh:itsmOperationOrder:remove']">
            删除
          </a>
        </span>
      </advance-table>
    </a-card>
    <a-modal v-model="superviseShowHide" title="添加督办信息" :mask="true" @ok="setupSupervise"  width="80%" >
      <a-row style="margin:10px;">        
      <a-col :span="4">
          审核人员：
      </a-col>
      <a-col :span="18">
        <a-select v-model="superviseUser" allowClear style="width:50%;" >
          <a-select-option v-for="(item, index) in zyjhfzrListArr" :key="index" :value="item.id" >{{ item.name }}</a-select-option>
        </a-select>
      </a-col>
     </a-row>
     <a-row style="margin:10px;">
      <a-col :span="4">
        意见:
      </a-col>
      <a-col :span="18">
        <a-textarea v-model="superviseDetail" :rows="2" style="width:50%;" />
      </a-col>
     </a-row>
     
     
    
      
    
    </a-modal>
  </div>
</template>
<script>
import { parseTime, resetForm, addDateRange, selectDictLabel, selectDictLabels, download, downloadTask, downloadByPath, handleTree, appendTreeNode, removeTreeNode, expandTree } from '@/utils/aidex'
import { delitsmOperationOrder, listitsmOperationOrder, delItsmWorkOrder, exportItsmWorkOrder, getInitData,listDbUsers,addSupervise,getSuperviseHistory,zyjhfzrList,gotoNextStepBatch,zyjhywsh } from '@/api/system/operationOrder/itsmWorkOrder'
import { listItsmEventOrder, delItsmEventOrder, exportItsmEventOrder ,exportItsmWorkOrder2} from '@/api/system/itsmEventOrder'
import AdvanceTable from '@/components/pt/table/AdvanceTable'
import ItsmWorkOrderAddForm from '@/views/system/workOrder/itsmworkorder/modules/ItsmWorkOrderAddForm'
import ItsmWorkOrderEditForm from '@/views/system/workOrder/itsmworkorder/modules/ItsmWorkOrderEditForm'
import moment from 'moment'
import { saveMenu ,removeMenuByName,findByCode} from '@/api/system/menu'
import addForm9 from '@/views/system/itsmOperationOrder/order/addForm9'


export default {
  name: 'ItsmWorkOrder',
  components: {
    AdvanceTable,
    ItsmWorkOrderAddForm,
    ItsmWorkOrderEditForm,
    addForm9

  },
  data () {
    return {
      advanced:false,
      operationBillingArr:[],
      operationCompanyArr:[],
      operationNatureArr:[],
      operationTypeArr:[],
      operationPlantypeArr:[],
      opresult:[],
      zyjhywshArr:[],
      zyjhfzrListArr:[],
      options:[],
      searchUserList:[],
      dateSelType:'日',
      dateSelTypeArr:['日','月','周'],
      dateSelTypeIndex:0,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 选中的数据集合
      selectedRows: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 查询参数
      queryParam: {
        
      },
      superviseTime:null,
      superviseDetail:null,
      superviseUser:'',
      cjgzsh:false,
      wdgzsh:false,
      superviseShowHide:false,
      showAddModal: false,
      showEditModal: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 选中的主键集合
      selectedRowKeys: [],
      // 选中的数据集合
      selectedRows: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // label的百分比
      labelCol: { span: 6 },
      // 内容区域的百分比
      wrapperCol: { span: 18 },
      // 我创建的工作表格数据
      itsmWorkOrderList: [],
      // 接单状态字典
      responseStatusOptions: [],
      // 审核结果字典
      checkResultOptions: [],
      // 审核状态字典
      checkStatusOptions: [],
      // 执行状态字典
      executeStatusOptions: [],
      // 作业状态字典
      statusOptions: [],
      // 督办类别
      superviseTypeOptions:[],
      // 超时状态字典
      timeOutOptions:[],

      executeResultOptions:[],

      dbUserList:[],
      superviseHistoryList:[],
      // 查询参数
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        mainId: undefined,
        params: new Map(),
        no: undefined

      },
      superviseHistoryColumns:[
        {
          title: '督办人',
          dataIndex: 'superviseUser',
          ellipsis: true,          
          align: 'center',
           
        },
        {
          title: '督办时间',
          dataIndex: 'superviseTime',
          ellipsis: true,          
          align: 'center',
           
        },
        {
          title: '详细内容',
          dataIndex: 'content',
          ellipsis: true,          
          align: 'center',
           
        },
        {
          title: '添加时间',
          dataIndex: 'addTime',
          ellipsis: true,          
          align: 'center',
           
        }
      ],
     
      columns: [
        {
          title: '作业计划编号',
          dataIndex: 'operationNo',
          ellipsis: true,
          // scopedSlots: { customRender: 'jump2addForm' },
          align: 'center',
          sorter: true,
          scopedSlots: { customRender: 'bpm' },
          width: '160px',
          fixed: 'left', // 固定在左侧
        },

        {
          title: '当前环节',
          dataIndex: 'flowNodeName',
          align: 'center',
          width: '160px',
        },
        {
          title: '作业主题',
          dataIndex: 'systemName',
          align: 'center',
          width: '160px',
        },
        {
          title: '作业性质',
          dataIndex: 'nature',
          align: 'center',
          width: '160px',
        },
        {
          title: '作业单位',
          dataIndex: 'operationUnit',
          align: 'center',
          width: '160px',
        },
        {
          title: '作业负责人',
          dataIndex: 'operationUser',
          scopedSlots: { customRender: 'fmtopuser' }, 
          align: 'center',
          width: '160px',
        },
        {
          title: '超时状态',
          dataIndex: 'timeoutStatus',
          align: 'center',
          width: '90px'
        },
        {
          title: '开始时间',
          dataIndex: 'planned_start_time',
          align: 'center',
          width: '160px',
        },
        {
          title: '结束时间',
          dataIndex: 'planned_end_time',
          align: 'center',
          width: '160px',
        },
        {
          title: '是否开票',
          dataIndex: 'billing',
          align: 'center',
          width: '80px',
        },
        {
          title: '作业状态',
          dataIndex: 'status',
          align: 'center',
          width: '90px',
          scopedSlots: { customRender: 'fmtstatus' }, 
          
        },
        {
          title: '审核状态',
          dataIndex: 'checkStatus',
          align: 'center',
          width: '90px',
          scopedSlots: { customRender: 'fmtcheckStatus' }, 
          
        },
        {
          title: '填报单位',
          dataIndex: 'addUnit',
          align: 'center',
          width: '160px',
        },
        {
          title: '审核人',
          dataIndex: 'checkUser',
          align: 'center',
          width: '120px',
        },
        {
          title: '审核时间',
          dataIndex: 'checkTime',
          align: 'center',
          width: '160px',
        },
        

        {
          title: '审核结果',
          dataIndex: 'checkResult',
          align: 'center',
          scopedSlots: { customRender: 'checkResult' },  
          width: '120px'
        },
        
        {
          title: '当前处理人',
          dataIndex: 'taskUser',
          align: 'center',
          width: '120px'
        },
        {
          title: '填报时间',
          dataIndex: 'addTime',
          ellipsis: true,
          align: 'center',
          width: '160px'
        },
        {
          title: '作业计划类型',
          dataIndex: 'plantype',
          scopedSlots: { customRender: 'transPlanType' },  
          align: 'center',
          width: '120px'
        },
        {
          title: '作业结果',
          dataIndex: 'executeResult',
          scopedSlots: { customRender: 'transcrd' },  
          align: 'center',
          width: '90px'
        },
        {
          title: '填报人',
          dataIndex: 'addUser',
          align: 'center',
          width: '120px'
        },
        {
          title: '操作',
          dataIndex: 'ops',
          scopedSlots: { customRender: 'operation' },  
          align: 'center',
          width: '160px',
          fixed: 'right', // 固定在左侧
        },
        

      ]
    }
  },
  activated () {    
    
    
    getInitData('response_status,audit_result,audit_status,execute_status,job_status,supervise_type,timeout_status,execute_result,operation_plantype,opertation_result,operation_type,operation_company,operation_nature,operation_billing').then(response => {
      this.responseStatusOptions = response.data.response_status
      this.checkResultOptions = response.data.audit_result
      this.checkStatusOptions = response.data.audit_status
      this.executeStatusOptions = response.data.execute_status
      this.statusOptions = response.data.job_status
      this.superviseTypeOptions = response.data.supervise_type
      this.timeOutOptions = response.data.timeout_status 
      this.executeResultOptions = response.data.execute_result
      this.operationPlanTypeArr = response.data.operation_plantype
      this.opresult = response.data.opertation_result
      this.operationTypeArr = response.data.operation_type
      this.operationCompanyArr = response.data.operation_company
      this.operationNatureArr = response.data.operation_nature 
      this.operationBillingArr = response.data.operation_billing
      zyjhywsh().then((res)=>{
        this.zyjhywshArr = res.rows
      })
      zyjhfzrList().then((res)=>{
        this.zyjhfzrListArr=res.rows
        this.getList()
      })
      
    })

  },
  methods: {
   
    fmatopuser(row){
      let flabel = ''
      this.zyjhfzrListArr.forEach((item)=>{
        if(item.id==row.operationUserId){
          flabel = item.name
        }
      })
      return flabel
    },
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    copyitem(){
      console.error(this.selectedRows)
       this.loading=true
          this.$multiTab.openTempRoute3("zyjih/index7",'zyjhck',this.selectedRows[0],'Zyjhgl')
    },
    trancr(row){
      
      let flag = ''
      
      this.opresult.forEach((item)=>{
        if(item.dictValue == row.checkResult){
          flag = item.dictLabel
        }
      })
      return flag;
    },
    transTypePlan(row){
      let flag = ''
      
      this.operationPlanTypeArr.forEach((item)=>{
        if(item.dictValue == row.plantype){
          flag = item.dictLabel
        }
      })
      return flag;
    },
    formatPlanStartDate(e){
      this.queryParam.params['beginPlannedStartTime']=e.format('YYYY-MM-DD')
    },
    formatPlanEndDate(e){
      this.queryParam.params['endPlannedStartTime']=e.format('YYYY-MM-DD')
    },
    formatWeekStart(e){
      
      this.queryParam.params['beginPlannedStartTime'] = moment(this.queryParam.beginPlannedStartTime).weekday(0).format('YYYY-MM-DD')
    },
    formatWeekEnd(e){
      this.queryParam.params['endPlannedStartTime'] = moment(this.queryParam.endPlannedStartTime).weekday(6).format('YYYY-MM-DD')
    },


    formatMonthStart(e){
      
      this.queryParam.params['beginPlannedStartTime'] = moment(this.queryParam.beginPlannedStartTime).startOf('month').format('YYYY-MM-DD')
    },
    formatMonthEnd(e){
      this.queryParam['endPlannedStartTime'] = moment(this.queryParam.endPlannedStartTime).endOf('month').format('YYYY-MM-DD')
    },
    changeExecuteRange(Moment,dateString){
      this.form.beginExecuteTime = dateString[0]
      this.form.endExecuteTime = dateString[1]
    },
    changeCheckRange(Moment,dateString){
      this.queryParam.beginCheckTime = dateString[0]
      this.queryParam.endCheckTime = dateString[1]
    },
    changeRealEndRange(Moment,dateString){
      this.queryParam.beginExecuteTime = dateString[0]
      this.queryParam.endExecuteTime = dateString[1]
    },
    changeRealBeginRange(Moment,dateString){
      this.queryParam.beginStartTime = dateString[0]
      this.queryParam.endStartTime = dateString[1]
    },
    changeRealEndRange(Moment,dateString){
      this.queryParam.beginEndTime = dateString[0]
      this.queryParam.endEndTime = dateString[1]
    },
    changePlanEndRange(Moment,dateString){
      this.queryParam.beginPlannedEndTime = dateString[0]
      this.queryParam.endPlannedEndTime = dateString[1]
    },
    changePlanBeginRange(Moment,dateString){
      this.queryParam.beginPlannedStartTime = dateString[0]
      this.queryParam.endPlannedStartTime = dateString[1]
    },
    changeDateRange(Moment,dateString){
      this.queryParam.beginAddTime = dateString[0]
      this.queryParam.endAddTime = dateString[1]
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParam.pageNum = 1
      this.getList()
    },
    handleSupervise(){
      for(var i=0;i<this.selectedRows.length;i++){
        if(this.selectedRows[i].plantype==2){
          this.$message.error('临时作业计划无法批量审核')
          return
        }
      }
      this.superviseShowHide=true
    },
    /** 查询我创建的工作列表 */
    getList () {
      this.loading = true
      listitsmOperationOrder(this.queryParam).then(response => {
         this.itsmWorkOrderList = response.rows
         this.total = response.total
        //this.itsmWorkOrderList = response.data.list
       // this.total = response.data.total
        this.itsmWorkOrderList = response.rows
        this.total = response.total
        // this.itsmWorkOrderList = response.data.list
        // this.total = response.data.total
        this.loading = false
      })
    },

    checkResultContentFormat (row) {

    },

    superviseContentFormat(row){
      if(row.superviseContent){
        return selectDictLabel(this.superviseTypeOptions,row.superviseContent)
      }else{
        return ''
      }
    },
    //翻译审核结果
    // checkResultFormat(row){
    //   debugger
    //   if(row.checkResult){
    //     return selectDictLabel(this.checkResultOptions,row.checkResult)
    //   }else{
    //     return ''
    //   }
    // },
    //翻译执行结果
    executeResultFormat(row){
      if(row.executeResult){
        return selectDictLabel(this.executeResultOptions,row.executeResult)
      }else{
        return ''
      }
    },
    //翻译作业状态
    jobStatusFormat(row){
      if(row.jobStatus){
        return selectDictLabel(this.jobStatusOptions,row.jobStatus)
      }else{
        return ''
      }
    },

    //翻译超时状态字典
    timeoutStatusFormat(row){
      if(row.timeoutStatus){
        return selectDictLabel(this.timeOutOptions,row.timeoutStatus)
      }else{
        return ''
      }
    },
    //翻译督办类型字典
    superviseTypeFormat(row){
      if(row.superviseType){
        return selectDictLabel(this.superviseTypeOptions,row.superviseType)
      }else{
        return ''
      }
    },
    // 接单状态字典翻译
    responseStatusFormat (row) {
      if (row.responseStatus) {
        return selectDictLabel(this.responseStatusOptions, row.responseStatus)
      } else {
        return ''
      }
    },
    // 审核结果字典翻译
    checkResultFormat (row) {
      if (row.checkResult) {
        return selectDictLabel(this.checkResultOptions, row.checkResult)
      } else {
        return ''
      }
    },
    // 审核状态字典翻译
    checkStatusFormat (row) {
      if (row.checkStatus) {
        return selectDictLabel(this.checkStatusOptions, row.checkStatus)
      } else {
        return ''
      }
    },
    // 执行状态字典翻译
    executeStatusFormat (row) {
      if (row.executeStatus) {
        return selectDictLabel(this.executeStatusOptions, row.executeStatus)
      } else {
        return ''
      }
    },
    // 作业状态字典翻译
    statusFormat (row) {
      if (row.status) {
        return selectDictLabel(this.statusOptions, row.status)
      } else {
        return ''
      }
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParam.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.queryParam = {
        pageNum: 1,
        pageSize: 10,
        mainId: undefined,

        no: undefined

      }
      this.handleQuery()
    },
    /** 翻页操作 */
    onShowSizeChange (current, pageSize) {
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    /** 翻页操作 */
    onSizeChange (current, size) {
      this.queryParam.pageNum = 1
      this.queryParam.pageSize = size
      this.getList()
    },
    /** 翻页操作 */
    changeSize (current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    /** 翻页操作 */
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = this.selectedRows.map(item => item.id)
      // this.single = selectedRowKeys.length !== 1
      // this.multiple = !selectedRowKeys.length
      if(null!=this.ids&&this.ids.length>0){
        if(this.ids.length==1){
          this.multiple=false
        }else{
          this.multiple=true
        }
      }else{
        this.multiple=true
      }
    },
    /** 查询折叠和展开操作 */
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    handleAdd () {
      this.$router.push({name:'Zyjhtb'})
    },
    handleCopy () {
      this.$router.push({name:'Zyjhtb',params:{cpid:this.selectedRows[0].id,cxpid:this.selectedRows[0].refId}})
    },
    handleJump2Edit(row){
      
          //  this.$multiTab.openNew(row.id,row.refId)      
       
          this.loading=true
          this.$multiTab.openTempRoute2("zyjih/index6",'zyjhck',row,'Zyjhgl')
       
        
    },
    otherway(row){
      this.loading=true
      this.$multiTab.openTempRoute("system/itsmOperationOrder/order/addForm9",'zyjhbj',row,'Zyjhgl')
      //this.loading=false
      // let xxx=this.$route.path.split('/')
      // let ppath=""
      // if(xxx.length>1){
      //   ppath=xxx[xxx.length-2]
      // }else{
      //   ppath=xxx[0]
      // }
     
      // findByCode(ppath).then(res=>{
      //    
      // })
      // let x = Math.random()*100
      
      //   let nn={
      //     parentId:"846052844065783808",
      //     menuName:"assa2" +x,
      //     icon:"bug",
      //     menuType:"C",
      //     treeSort:110,
      //     isFrame:"1",
      //     isCache:"0",
      //     visible:"1",
      //     status:"0",
      //     component:"system/itsmOperationOrder/order/addForm9",
      //     menuCode:"assa2" +x,
      //     path:"assa2" +x
      //   }

      //   saveMenu(nn).then(res=>{
      //     this.$store.dispatch('GetInfo').then((res)=>{
      //         const roles = res.roles
      //         this.$store.dispatch('GenerateRoutes', { roles }).then(()=>{
      //           this.findRoute(this.$store.getters.addRouters,"Assa2" +x,row.id,row.refId,'Zyjhgl')              
      //         })
      //     })
      //   })
    },
    // findRoute(list,fname,id,xid,pname){
    //   if(null!=list&&list.length>0){
    //     list.forEach(item=>{
    //       if(item.name==fname){
    //         this.$router.addRoute(pname,item)
    //         this.$router.push({name:fname,query:{xid:id,xpid:xid}})
    //         removeMenuByName(fname)
    //         return
    //       }else{
    //         if(null!=item.children&&item.children.length>0){
    //            this.findRoute(item.children,fname,id,xid,pname)
    //         }
    //       }
    //     })
    //   }
    // },
    /** 重置按钮操作 */
    resetQuery () {
      this.queryParam = {
        pageNum: 1,
        pageSize: 10,
        orderByColumn: 'a.create_time',
        isAsc: 'desc'
      }
    },
    handleUpdate (record, ids) {
      this.showEditModal = true
      this.$nextTick(() => (
        this.$refs.itsmWorkOrderEditForm.handleUpdate(record, ids)
      ))
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      var that = this
      const itsmWorkOrderIds = row.id || this.ids
      this.$confirm({
        title: '确认删除所选中数据?',
        onOk () {
          return delitsmOperationOrder(itsmWorkOrderIds)
            .then(() => {
              that.onSelectChange([], [])
              that.getList()
              that.$message.success(
                '删除成功',
                3
              )
          })
        },
        onCancel () {}
      })
    },
    /** 导出按钮操作 */
    handleExport () {
      var that = this
      this.$confirm({
        title: '是否确认导出?',
        content: '此操作将导出当前条件下所有数据而非选中数据',
        onOk () {
          return exportItsmWorkOrder2(that.queryParam)
            .then(response => {
             that.download(response.msg)
              that.$message.success(
                '导出成功',
                3
              )
          })
        },
        onCancel () {}
      })
    },
    handleTableChange (pagination, filters, sorter) {
      if (sorter.field !== undefined && sorter.field !== null && sorter.field !== '') {
        this.queryParam.orderByColumn = 'a.' + sorter.field
        this.queryParam.isAsc = sorter.order
      }
      this.getList()
    },
    
    setupSupervise(){
      let str=null
      for(var i=0;i<this.selectedRowKeys.length;i++){
        if(null==str){
          str=this.selectedRowKeys[i]
        }else{
          str=str+","+this.selectedRowKeys[i]
        }
      }
      if(null!=this.superviseUser&&""!=this.superviseUser&&undefined!=this.superviseUser){
        let superviseUserId= this.superviseUser   
        let superName=null    
        for(var i=0;i<this.zyjhfzrListArr.length;i++){
          if(null!=this.zyjhfzrListArr[i].id){
            if(this.zyjhfzrListArr[i].id==superviseUserId){
              superName=this.zyjhfzrListArr[i].name
            }
          }
          
        }
        let param = {
              opinion:this.superviseDetail,
              refids:str,
              userList:[
                {id:superviseUserId,name:superName}
              ],
              fileinfoid:"t855027886262845440",
              checkname:"通过",
              curstepid:"s855025537020592128",
              nextstepid:"s855026911640813568"
            }
        gotoNextStepBatch(param).then((res)=>{
          this.superviseShowHide=false
          this.$message.success('批量审核成功')
          this.getList()
        })
        
      }else{
        this.$message.error('请选择督办人')
      }
      
    }
  }
}
</script>
