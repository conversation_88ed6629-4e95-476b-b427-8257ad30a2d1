import request from '@/utils/request'

// 查询流程路径表列表
export function listBpmFlowPathInfo (query) {
  return request({
    url: '/system/bpmFlowPathInfo/list',
    method: 'get',
    params: query
  })
}

// 查询流程路径表详细
export function getBpmFlowPathInfo (id) {
  return request({
    url: '/system/bpmFlowPathInfo/' + id,
    method: 'get'
  })
}

// 新增流程路径表
export function addBpmFlowPathInfo (data) {
  return request({
    url: '/system/bpmFlowPathInfo',
    method: 'post',
    data: data
  })
}

// 修改流程路径表
export function updateBpmFlowPathInfo (data) {
  return request({
    url: '/system/bpmFlowPathInfo',
    method: 'put',
    data: data
  })
}

// 删除流程路径表
export function delBpmFlowPathInfo (id) {
  return request({
    url: '/system/bpmFlowPathInfo/' + id,
    method: 'delete'
  })
}

// 导出流程路径表
export function exportBpmFlowPathInfo (query) {
  return request({
    url: '/system/bpmFlowPathInfo/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmFlowPathInfo/getInitData/' + dictTypes,
    method: 'get'
  })
}
