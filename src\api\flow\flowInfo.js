import request from '@/utils/request'

export function getFlows(params) {
  return request({
    url: 'api/flowInfo',
    method: 'get',
    params
  })
}

export function getFlowTree(id) {
  return request({
    url: 'api/flowInfo/getflowinfoTree/' + id,
    method: 'get'
  })
}

export function getFlowChart(id) {
  return request({
    url: 'api/flowInfo/getChart?id=' + id,
    method: 'get'
  })
}

export function saveChart(data) {
  return request({
    url: 'api/flowInfo/saveChart',
    method: 'post',
    data
  })
}

export function add(data) {
  return request({
    url: 'api/flowInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/flowInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/flowInfo',
    method: 'put',
    data
  })
}

export default { add, edit, del }
