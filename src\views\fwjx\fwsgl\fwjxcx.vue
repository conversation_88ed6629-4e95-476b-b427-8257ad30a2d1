<template>
  <div>
    <a-card :bordered="false">
      <!-- 条件搜索 -->
      <div class="table-page-search-wrapper">
        <a-form   ref="queryForm">
          <a-tabs v-model:activeKey="myKey"  @change="handleTabChange">
            
            <a-tab-pane key="4" tab="服务绩效统计(周)">
              <a-row :gutter="[24, 24]" class="module-list">
                <a-col :span="24">
                  <div
                    class="module-in module-in01 modlule-show"
                    style="
                      display: flex;
                      height: 6px;
                      align-items: center;
                      font-size: 15px;
                      color: black;
                      font-weight: bold;
                      margin-top: 20px;
                    "
                  >
                   
                    <a-week-picker @change="weeksel"  />
                    <span class="table-page-search-submitButtons">
                      <a-button type="primary" @click="handleQuery(2)" style="margin-left: 12px; margin-right: 10px; margin-top: 14px"><a-icon type="search" />查询</a-button
                      >
                      <a-button   @click="handleExport"  ><a-icon type="search" />导出</a-button
                        >
                      <!-- <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button> -->
                    </span>
                  </div>
                </a-col>
              </a-row> 
            </a-tab-pane>
            <a-tab-pane key="5" tab="服务绩效统计(月)">
              <a-row :gutter="[24, 24]" class="module-list">
                <a-col :span="24">
                  <div
                    class="module-in module-in01 modlule-show"
                    style="
                      display: flex;
                      height: 6px;
                      align-items: center;
                      font-size: 15px;
                      color: black;
                      font-weight: bold;
                      margin-top: 20px;
                    "
                  >
                    起始时间：
                   <a-month-picker   picker="month"  @change="changeMonthSt" v-model="stTime"/>
                    <!-- &nbsp;-&nbsp; -->
                    <!-- <a-month-picker   picker="month"  @change="changeMonthEd" /> -->
                    <span class="table-page-search-submitButtons">
                      <a-button type="primary" @click="handleQuery(1)" style="margin-left: 12px; margin-right:10px;margin-top: 14px"><a-icon type="search" />查询</a-button
                      >
                      <a-button   @click="handleExport"  ><a-icon type="search" />导出</a-button>
                      <!-- <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button> -->
                    </span>
                  </div>
                </a-col>
              </a-row> 
            </a-tab-pane>
          </a-tabs>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false" class="table-card">
      <advance-table
       style=" margin-left: 20px;"
        title="  "
       
        :columns="columns"
        :data-source="itsmsjjeqkEventList"
      >
        
        
      </advance-table>
    </a-card>
  </div>
</template>
<script>
import moment from 'moment'
import AdvanceTable from '@/components/pt/table/AdvanceTable'
import {fwjxhkcx,exportFwjxkhtjQuery} from '@/api/system/itsmEventOrder'
import { downLoadExcel } from '@/utils/zipdownload'
import {mapMutations } from 'vuex'
export default {
  components: {
    AdvanceTable,
  },
  data() {
    return {
      queryType:'1',
      myKey:'4',
      form: this.$form.createForm(this),
      visible: false,

      queryParam: {
        pageNum: 1,
        pageSize: 10,
      },
      columns: [
        {
          title: '合同编号',
          dataIndex: 'contract_no',
        },
        {
          title: '合同名称',
          dataIndex: 'contract_name',
        },
        {
          title: '考核周期',
          dataIndex: 'assess',
        },
        {
          title: '签定日期',
          dataIndex: 'sign_time',
        },
        {
          title: '服务商名称',
          dataIndex: 'provider_name',
        },
        {
          title: '本周加分',
          dataIndex: 'qp',
        },
        {
          title: '本周减分',
          dataIndex: 'qm',
        },
        {
          title: '累积加分',
          dataIndex: 'totalp',
        },
        {
          title: '累积减分',
          dataIndex: 'totalm',
        },
        {
          title: '得分',
          dataIndex: 'total',
        }
      ],
      // data: [
      //   {
      //     key: '1',
      //     name: 'John Brown',
      //     age: 1,
      //     address: '按时处理',
      //     tags: ['nice', 'developer'],
      //   },
      //   {
      //     key: '2',
      //     name: 'Jim Green',
      //     age: 2,
      //     address: '即将处理',
      //     tags: ['loser'],
      //   },
      //   {
      //     key: '3',
      //     name: '已超时',
      //     age: 3,
      //     address: '已超时',
      //     tags: ['cool', 'teacher'],
      //   },
      //   {
      //     key: '3',
      //     name: '已超时',
      //     age: 4,
      //     address: '合计',
      //     tags: ['cool', 'teacher'],
      //   },
      // ],
      // columnsClick: [
      //   {
      //     title: '问题对象',
      //     dataIndex: 'fdnum',
      //     ellipsis: true,
      //     align: 'left',
      //     width: '80px',
      //   },
      //   {
      //     title: '姓名',
      //     dataIndex: 'fdxm',
      //     ellipsis: true,
      //     align: 'left',
      //     width: '80px',
      //   },
      //   {
      //     title: '岗位',
      //     dataIndex: 'fdgw',
      //     ellipsis: true,
      //     align: 'left',
      //     width: '80px',
      //   },
      //   {
      //     title: '身份证号',
      //     dataIndex: 'fdsfzh',
      //     align: 'left',
      //     width: '155px',
      //   },
      //   {
      //     title: '变更情况',
      //     dataIndex: 'fdbgqk',
      //     scopedSlots: { customRender: 'fdbgqk' },
      //     align: 'center',
      //     width: '100px',
      //   },
      //   {
      //     title: '计划变更时间',
      //     dataIndex: 'fdjhbgsj',
      //     align: 'center',
      //     width: '120px',
      //   },
      //   {
      //     title: '用户id',
      //     dataIndex: 'fdyhid',
      //     ellipsis: true,
      //     align: 'left',
      //     width: '100px',
      //   },
      //   {
      //     title: '同步状态',
      //     dataIndex: 'fdtbzt',
      //     scopedSlots: { customRender: 'fdtbzt' },
      //     align: 'center',
      //     width: '100px',
      //   },
      //   {
      //     title: '审批步骤',
      //     dataIndex: 'fdspbz',
      //     scopedSlots: { customRender: 'fdspbz' },
      //     align: 'center',
      //     width: '100px',
      //   },
      //   {
      //     title: '同步时间',
      //     dataIndex: 'fdtbsj',
      //     align: 'center',
      //     width: '100px',
      //   },
      //   {
      //     title: '操作',
      //     dataIndex: 'operation',
      //     align: 'center',
      //     width: '80px',
      //     scopedSlots: { customRender: 'operation' },
      //   },
      // ],
      // dataClick: [
      //   {
      //     key: '1',
      //     name: 'John Brown',
      //     fdxm: '李三',
      //     fdgw: '项目经理',
      //     fdsfzh: '210121199001226513',
      //     fdbgqk: '入场',
      //     fdjhbgsj: '2024-3-19',
      //     fdyhid: '123',
      //     fdtbzt: '',
      //     fdspbz: '',
      //     fdtbsj: '',
      //     fdnum: 1,
      //     address: '按时处理',
      //     tags: ['nice', 'developer'],
      //   },
      // ],
        stTime: null, // 起始时间  
        edTime: null, // 结束时间 
        itsmsjjeqkEventList:[],
    }
  },

  created() {  
    const now = new Date();  
    // const currentYear = now.getFullYear();   
    // const startOfYear = new Date(currentYear, 0, 1);
    // const endOfYear = new Date(currentYear, 11, 1); 
    this.stTime =  moment().startOf('month').format("YYYY-MM-DD")+" 00:00:00"
    this.edTime = moment().endOf('month').format("YYYY-MM-DD")+" 23:59:59"
    this.handleQuery(1)
  },
  methods: {
    weeksel(e){ 
      this.stTime = e.startOf('week').format("YYYY-MM-DD")+" 00:00:00"
      this.edTime = e.endOf('week').format("YYYY-MM-DD")+" 23:59:59" 
    },


    ...mapMutations(['SET_STARTTIME','SET_ENDTTIME','SET_REGISTERUSER','SET_SELFHANDLE']),
    jump2searchEven(row,val){
      this.SET_STARTTIME(this.stTime)  // 从vuex eventStatistics中设置开始时间
      this.SET_ENDTTIME(this.edTime)  // 从vuex eventStatistics中设置开始时间
      this.SET_REGISTERUSER(row['受理人/处理人']!=='合计'?row['受理人/处理人']:undefined)   // 登记人
      this.SET_SELFHANDLE(val) // 自行处理
      this.$multiTab.openCloseOld('eventSearch','Sjcx3','Sjcx3')
    },
    handleTabChange (activeKey) {
      this.myKey = activeKey
      if(this.myKey ==='3') {   //自定义
        this.stTime = moment().startOf('month').format("YYYY-MM-DD")+" 00:00:00"
        this.edTime = moment().endOf('month').format("YYYY-MM-DD")+" 23:59:59"
      }else if(this.myKey==='4') { //周
        this.stTime = moment().startOf('week').format("YYYY-MM-DD")+" 00:00:00"
        this.edTime = moment().endOf('week').format("YYYY-MM-DD")+" 23:59:59"
      }else if(this.myKey==='5') { //月
        this.stTime = moment().startOf('month').format("YYYY-MM-DD")+" 00:00:00"
        this.edTime = moment().endOf('month').format("YYYY-MM-DD")+" 23:59:59"
      }
      this.handleQuery()  // 查询接口
    },
    changeMonthEd(e){
      this.stTime = moment().endOf('month').format("YYYY-MM-DD")+" 23:59:59"
    },
    changeMonthSt(e){
      this.stTime = e.startOf('month').format("YYYY-MM-DD")+" 00:00:00"
      this.edTime = e.endOf('month').format("YYYY-MM-DD") + " 23:59:59"
    },
    changeWeek2(e){      
      this.edTime = e.endOf('week').format("YYYY-MM-DD") + " 23:59:59"
    },
    changeWeek(e){      
      this.stTime = e.startOf('week').format("YYYY-MM-DD") + " 00:00:00"
      this.edTime = e.endOf('week').format("YYYY-MM-DD") + " 23:59:59"
    },
    handleChangeStartTime(date, dateString) {  
    
      this.stTime = dateString || date.format('YYYY-MM-DD'); // 假设你使用的是 moment.js  
      console.log('起始时间已更改：', this.stTime);  
    },  
    formatDate(date) {   
        const year = date.getFullYear();  
        const month = String(date.getMonth() + 1).padStart(2, '0'); 
        const day = String(date.getDate()).padStart(2, '0');  
        return `${year}-${month}-${day}`;  
      },
    handleChangeEndTime(date, dateString) {  

      this.edTime = dateString || date.format('YYYY-MM-DD');  
      console.log('结束时间已更改：', this.edTime);  
    },
    afterVisibleChange(val) {
      console.log('visible', val)
    },
    handleExport(){
      let that = this
      let data = {
          stTime: this.stTime,
          edTime: this.edTime,
        }
      this.$confirm({
          title: '是否确认导出?',
          content: '',
          onOk() {
            return exportFwjxkhtjQuery(that.queryParam)
              .then(response => {
                that.download(response.msg)
                that.$message.success(
                  '导出成功',
                  3
                )
            })
          },
          onCancel() { }
        })
    },
    handleQuery(type){
        // this.queryType = type
        this.getList()
      },
      getList() {
        // let data = {
        //   queryType: this.queryType,
        //   stTime: this.stTime,
        //   edTime: this.edTime,
        // }
        if(typeof(this.stTime)=='object'){
          this.stTime = this.stTime.format("YYYY-MM-DD") +" 00:00:00"
        }
        if(typeof(this.edTime)=='object'){
          this.edTime = this.edTime.format("YYYY-MM-DD")+" 23:59:59"
        }
        let data = {
          stTime: this.stTime,
          edTime: this.edTime,
        }
        fwjxhkcx(data).then(response => {
          console.log(response,'response11111');
          this.itsmsjjeqkEventList = response.data
        })
      },
    showDrawer() {
      this.visible = true
    },
    onClose() {
      this.visible = false
    },
  },
}
</script>
<style>
.ant-drawer-mask {
  background-color: rgba(0, 0, 0, 0.1) !important;
}
</style>
