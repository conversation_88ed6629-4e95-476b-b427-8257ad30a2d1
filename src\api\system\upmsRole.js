import request from '@/utils/request'

// 查询系统角色表列表
export function listUpmsRole (query) {
  return request({
    url: '/system/upmsRole/list',
    method: 'get',
    params: query
  })
}

// 查询系统角色表详细
export function getUpmsRole (id) {
  return request({
    url: '/system/upmsRole/' + id,
    method: 'get'
  })
}

// 新增系统角色表
export function addUpmsRole (data) {
  return request({
    url: '/system/upmsRole',
    method: 'post',
    data: data
  })
}

// 修改系统角色表
export function updateUpmsRole (data) {
  return request({
    url: '/system/upmsRole',
    method: 'put',
    data: data
  })
}

// 删除系统角色表
export function delUpmsRole (id) {
  return request({
    url: '/system/upmsRole/' + id,
    method: 'delete'
  })
}


// 导出系统角色表
export function exportUpmsRole (query) {
  return request({
    url: '/system/upmsRole/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/upmsRole/getInitData/' + dictTypes,
    method: 'get'
  })
}
