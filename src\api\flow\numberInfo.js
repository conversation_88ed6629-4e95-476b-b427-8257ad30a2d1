import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/numberInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/numberInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/numberInfo',
    method: 'put',
    data
  })
}

export function getById(id) {
  return request({
    url: 'api/queryById?id=' + id,
    method: 'get'
  })
}

export function getMaxNumber() {
  return request({
    url: '/api/numberInfo/maxNumberId',
    method: 'get'
  })
}

//查询本年度可用漏号
export function arrQuery() {
  return request({
    url: '/api/numberInfo/arrQuery',
    method: 'get'
  })
}

export default { add, edit, del, getMaxNumber, arrQuery }
