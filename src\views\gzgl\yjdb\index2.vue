<template>
    <div>
      <a-card :bordered="false" style="margin-bottom: 10px;">
        <!-- 条件搜索 -->
        <div class="table-page-search-wrapper">
          <a-form :labelCol="labelCol" :wrapperCol="wrapperCol" ref="queryForm">
            <a-row :gutter="32">
              <a-col :span="6" >
                <a-form-item label="编号">
                  <a-input v-model="queryParam.fdBh" placeholder="请输入编号" allow-clear @keyup.enter.native="handleQuery"/>
                </a-form-item>
              </a-col>
              <!-- <a-col :span="6" >
                <a-form-item label="当前环节名称">
                  <a-input v-model="queryParam.curstepname" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery"/>
                </a-form-item>
              </a-col> -->
              <!-- <a-col :span="6" >
                <a-form-item label="main主键">
                  <a-input v-model="queryParam.mainId" placeholder="请输入main主键" allow-clear @keyup.enter.native="handleQuery"/>
                </a-form-item>
              </a-col> -->
              <!-- <a-col :span="6" v-if="advanced">
                <a-form-item label="超时状态">
                  <a-input v-model="queryParam.fdCszt" placeholder="请输入超时状态:0未超时 1即将超时 2已超时" allow-clear @keyup.enter.native="handleQuery"/>
                </a-form-item>
              </a-col> -->
              <a-col>
                <span class="table-page-search-submitButtons" style="float: right;">
                  <a-button type="primary" @click="handleQuery"><a-icon type="search" />查询</a-button>
                  <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button>
                  <a @click="toggleAdvanced" style="margin-left: 8px">
                    {{ advanced ? '收起' : '展开' }}
                    <a-icon :type="advanced ? 'up' : 'down'"/>
                  </a>
                </span>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </a-card>
      <a-card :bordered="false" class="table-card">
        <!-- 增加 -->
        <itsm-yjqxp-order-add-form
          v-if="showAddModal"
          ref="itsmYjqxpOrderAddForm"
          :pendingStatusOptions="pendingStatusOptions"
          :checkOneStatusOptions="checkOneStatusOptions"
          :checkTwoStatusOptions="checkTwoStatusOptions"
          :checkThreeStatusOptions="checkThreeStatusOptions"
          :statusOptions="statusOptions"
          @ok="getList"
          @close="showAddModal = false"
        />
        <!-- 编辑 -->
        <itsm-yjqxp-order-edit-form
          v-if="showEditModal"
          ref="itsmYjqxpOrderEditForm"
          :pendingStatusOptions="pendingStatusOptions"
          :checkOneStatusOptions="checkOneStatusOptions"
          :checkTwoStatusOptions="checkTwoStatusOptions"
          :checkThreeStatusOptions="checkThreeStatusOptions"
          :statusOptions="statusOptions"
          @ok="getList"
          @close="showEditModal = false"
        />
        <advance-table
          title="应急抢修票查询"
          :pagination="{
            current: queryParam.pageNum,
            pageSize: queryParam.pageSize,
            total: total,
            showSizeChanger: true,
            showLessItems: true,
            showQuickJumper: true,
            showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，总计 ${total} 条`,
            onChange: changeSize,
            onShowSizeChange: onShowSizeChange
          }"
          tableKey="base-itsmYjqxpOrder-index-table"
          @change="handleTableChange"
          rowKey="id"
          size="middle"
          @refresh="getList"
          :columns="columns"
          :data-source="itsmYjqxpOrderList"
          :loading="loading"
          :format-conditions="true"
          :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        >
          <div class="table-operations" slot="button">
            <!-- <a-button type="primary" @click="handleAdd" v-hasPermi="['itsmYjqxpOrder:itsmYjqxpOrder:add']">
              <a-icon type="plus" />新增
            </a-button> -->
            <a-button type="danger" v-if="!multiple" :disabled="multiple" @click="handleDelete" v-hasPermi="['itsmYjqxpOrder:itsmYjqxpOrder:remove']">
              <a-icon type="delete" />删除
            </a-button>
            <a-button type="" @click="handleExport" v-hasPermi="['itsmYjqxpOrder:itsmYjqxpOrder:export']">
              <a-icon type="download" />导出
            </a-button>
          </div>
          <span slot="pendingStatus" slot-scope="{record}">
            {{ pendingStatusFormat(record) }}
          </span>
          <span slot="checkOneStatus" slot-scope="{record}">
            {{ checkOneStatusFormat(record) }}
          </span>
          <span slot="checkTwoStatus" slot-scope="{record}">
            {{ checkTwoStatusFormat(record) }}
          </span>
          <span slot="checkThreeStatus" slot-scope="{record}">
            {{ checkThreeStatusFormat(record) }}
          </span>
          <span slot="status" slot-scope="{record}">
            <a-badge :status="record.status == '0' ? 'processing' : 'error'" :text=" statusFormat(record) " />
          </span>
          <span slot="bpm" slot-scope="{text, record}">
          <a @click="handleBpm(record)" >
            {{text}}
          </a>
        </span>
          <span slot="operation" slot-scope="{text, record}">
            <a @click="handleUpdate(record)" v-hasPermi="['itsmYjqxpOrder:itsmYjqxpOrder:edit']">
              修改
            </a>
            <a-divider type="vertical" v-hasPermi="['itsmYjqxpOrder:itsmYjqxpOrder:remove']"/>
            <a @click="handleDelete(record)" v-hasPermi="['itsmYjqxpOrder:itsmYjqxpOrder:remove']">
              删除
            </a>
          </span>
        </advance-table>
      </a-card>
    </div>
  </template>
  <script>
  import { listItsmYjqxpOrderList, delItsmYjqxpOrder, exportItsmYjqxpOrder, getInitData } from '@/api/system/itsmYjqxpOrder'
  import AdvanceTable from '@/components/pt/table/AdvanceTable'
  import ItsmYjqxpOrderAddForm from '@/views/gzgl/yjdb/itsmyjqxporder/modules/ItsmYjqxpOrderAddForm'
  import ItsmYjqxpOrderEditForm from '@/views/gzgl/yjdb/itsmyjqxporder/modules/ItsmYjqxpOrderEditForm'
  export default {
    name: 'ItsmYjqxpOrder',
    components: {
      AdvanceTable,
      ItsmYjqxpOrderAddForm,
      ItsmYjqxpOrderEditForm
    },
    data () {
      return {
        showAddModal: false,
        showEditModal: false,
        // 遮罩层
        loading: true,
        // 选中数组
        ids: [],
        // 选中的主键集合
        selectedRowKeys: [],
        // 选中的数据集合
        selectedRows: [],
        // 高级搜索 展开/关闭
        advanced: false,
        // 非单个禁用
        single: true,
        // 非多个禁用
        multiple: true,
        // 总条数
        total: 0,
        // label的百分比
        labelCol: { span: 6 },
        // 内容区域的百分比
        wrapperCol: { span: 18 },
        // itsmYjqxpOrder表格数据
        itsmYjqxpOrderList: [],
        // 待办状态字典
        pendingStatusOptions: [],
        // 系统运维商审核状态字典
        checkOneStatusOptions: [],
        // 自动化科业务负责人审核状态字典
        checkTwoStatusOptions: [],
        // 自动化科批准状态字典
        checkThreeStatusOptions: [],
        // 状态字典
        statusOptions: [],
        // 查询参数
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          fdBh: undefined,
          curstepname: undefined,
          addUser: undefined,
  
          mainId: undefined,
  
          fdCszt: undefined
  
        },
        columns: [
          {
            title: '编号',
            dataIndex: 'no',
            ellipsis: true,
            width: '180px',
            sorter: true,
            scopedSlots: { customRender: 'bpm' }
          },
          {
            title: '抢修负责人',
            dataIndex: 'addUser',
            ellipsis: true,
            align: 'center',
            width: '150px',
            scopedSlots: { customRender: 'bpm' }
          },  
          {
            title: '当前环节名称',
            dataIndex: 'taskName',
            align: 'center',
            width: '100px',
          },
          {
            title: '待办人',
            dataIndex: 'taskUser',
            align: 'center',
            width: '100px',
          },
          {
            title: '抢修风险',
            dataIndex: 'risk',
            align: 'center',
            width: '100px',
          },
          {
            title: '抢修内容',
            dataIndex: 'content',
            align: 'center',
            width: '100px',
          },
          {
            title: '风险应对措施',
            dataIndex: 'measures',
            align: 'center',
            width: '100px',
          },
          {
            title: '工作许可人',
            dataIndex: 'permit',
            align: 'center',
            width: '100px',
          },
          // {
          //   title: '操作',
          //   dataIndex: 'operation',
          //   align: 'center',
          //   width: '10%',
          //   scopedSlots: { customRender: 'operation' }
          // }
        ]
      }
    },
    activated(){
      this.getList()
    },
    created () {
      this.getList()
      // getInitData('sys_normal_disable,sys_normal_disable,sys_normal_disable,sys_normal_disable,sys_normal_disable').then(response => {
      //   this.pendingStatusOptions = response.data.sys_normal_disable
      //   this.checkOneStatusOptions = response.data.sys_normal_disable
      //   this.checkTwoStatusOptions = response.data.sys_normal_disable
      //   this.checkThreeStatusOptions = response.data.sys_normal_disable
      //   this.statusOptions = response.data.sys_normal_disable
      // })
    },
    methods: {
      /** 查询itsmYjqxpOrder列表 */
      getList () {
        this.loading = true
        listItsmYjqxpOrderList(this.queryParam).then(response => {
          this.itsmYjqxpOrderList = response.data.list
          this.total = response.data.total
          this.loading = false
        })
      },
      handleBpm(record){
      

      this.loading=true    
      setTimeout(function(){
        this.loading=false
      },3000)
      this.$multiTab.openTempRoute2("gzgl/yjqxdj/index6",'yjgzdjsq',record,'Gzgl')

    },
      // 待办状态字典翻译
      pendingStatusFormat (row) {
        if (row.pendingStatus) {
          return this.selectDictLabel(this.pendingStatusOptions, row.pendingStatus)
        } else {
          return ''
        }
      },
      // 系统运维商审核状态字典翻译
      checkOneStatusFormat (row) {
        if (row.checkOneStatus) {
          return this.selectDictLabel(this.checkOneStatusOptions, row.checkOneStatus)
        } else {
          return ''
        }
      },
      // 自动化科业务负责人审核状态字典翻译
      checkTwoStatusFormat (row) {
        if (row.checkTwoStatus) {
          return this.selectDictLabel(this.checkTwoStatusOptions, row.checkTwoStatus)
        } else {
          return ''
        }
      },
      // 自动化科批准状态字典翻译
      checkThreeStatusFormat (row) {
        if (row.checkThreeStatus) {
          return this.selectDictLabel(this.checkThreeStatusOptions, row.checkThreeStatus)
        } else {
          return ''
        }
      },
      // 状态字典翻译
      statusFormat (row) {
        if (row.status) {
          return this.selectDictLabel(this.statusOptions, row.status)
        } else {
          return ''
        }
      },
      /** 搜索按钮操作 */
      handleQuery () {
        this.queryParam.pageNum = 1
        this.getList()
      },
      /** 重置按钮操作 */
      resetQuery () {
        this.queryParam = {
          pageNum: 1,
          pageSize: 10,
          fdBh: undefined,

          addUser: undefined,
          curstepname: undefined,
          mainId: undefined,
  
          fdCszt: undefined
  
        }
        this.handleQuery()
      },
      /** 翻页操作 */
      onShowSizeChange (current, pageSize) {
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 翻页操作 */
      onSizeChange (current, size) {
        this.queryParam.pageNum = 1
        this.queryParam.pageSize = size
        this.getList()
      },
      /** 翻页操作 */
      changeSize (current, pageSize) {
        this.queryParam.pageNum = current
        this.queryParam.pageSize = pageSize
        this.getList()
      },
      /** 翻页操作 */
      onSelectChange (selectedRowKeys, selectedRows) {
        this.selectedRowKeys = selectedRowKeys
        this.selectedRows = selectedRows
        this.ids = this.selectedRows.map(item => item.id)
        this.single = selectedRowKeys.length !== 1
        this.multiple = !selectedRowKeys.length
      },
      /** 查询折叠和展开操作 */
      toggleAdvanced () {
        this.advanced = !this.advanced
      },
      handleAdd () {
        this.showAddModal = true
        this.$nextTick(() => (
          this.$refs.itsmYjqxpOrderAddForm.handleAdd()
        ))
      },
      handleUpdate (record, ids) {
        this.showEditModal = true
        this.$nextTick(() => (
          this.$refs.itsmYjqxpOrderEditForm.handleUpdate(record, ids)
        ))
      },
      /** 删除按钮操作 */
      handleDelete (row) {
        var that = this
        const itsmYjqxpOrderIds = row.id || this.ids
        this.$confirm({
          title: '确认删除所选中数据?',
          onOk () {
            return delItsmYjqxpOrder(itsmYjqxpOrderIds)
              .then(() => {
                that.onSelectChange([], [])
                that.getList()
                that.$message.success(
                  '删除成功',
                  3
                )
            })
          },
          onCancel () {}
        })
      },
      /** 导出按钮操作 */
      handleExport () {
        var that = this
        this.$confirm({
          title: '是否确认导出?',
          content: '此操作将导出当前条件下所有数据而非选中数据',
          onOk () {
            return exportItsmYjqxpOrder(that.queryParam)
              .then(response => {
                that.download(response.msg)
                that.$message.success(
                  '导出成功',
                  3
                )
            })
          },
          onCancel () {}
        })
      },
      handleTableChange (pagination, filters, sorter) {
        if (sorter.field !== undefined && sorter.field !== null && sorter.field !== '') {
          this.queryParam.orderByColumn = 'a.' + sorter.field
          this.queryParam.isAsc = sorter.order
        }
        this.getList()
      }
    }
  }
  </script>
  