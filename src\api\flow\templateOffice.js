import request from '@/utils/request'

export function getTempOffices(params) {
  return request({
    url: 'api/templateOffice',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/templateOffice',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/templateOffice/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/templateOffice',
    method: 'put',
    data
  })
}

export function getById(id) {
  return request({
    url: 'api/templateOffice/queryById?id=' + id,
    method: 'get'
  })
}

export default { add, edit, del }
