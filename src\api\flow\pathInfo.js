import request from '@/utils/request'

export function getPaths(params) {
  return request({
    url: 'api/pathInfo',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/pathInfo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/pathInfo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/pathInfo',
    method: 'put',
    data
  })
}

export default { add, edit, del }
