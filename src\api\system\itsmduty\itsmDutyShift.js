import request from '@/utils/request'

// 查询值班管理-班次管理列表
export function listItsmDutyShift (query) {
  return request({
    url: '/system/itsmDutyShift/list',
    method: 'get',
    params: query
  })
}

// 查询值班管理-班次管理详细
export function getItsmDutyShift (id) {
  return request({
    url: '/system/itsmDutyShift/' + id,
    method: 'get'
  })
}

// 新增值班管理-班次管理
export function addItsmDutyShift (data) {
  return request({
    url: '/system/itsmDutyShift',
    method: 'post',
    data: data
  })
}

// 修改值班管理-班次管理
export function updateItsmDutyShift (data) {
  return request({
    url: '/system/itsmDutyShift',
    method: 'put',
    data: data
  })
}

// 删除值班管理-班次管理
export function delItsmDutyShift (id) {
  return request({
    url: '/system/itsmDutyShift/' + id,
    method: 'delete'
  })
}


// 导出值班管理-班次管理
export function exportItsmDutyShift (query) {
  return request({
    url: '/system/itsmDutyShift/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyShift/getInitData/' + dictTypes,
    method: 'get'
  })
}
