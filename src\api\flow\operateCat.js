import request from '@/utils/request'

export function getOperateCat(params) {
  return request({
    url: 'api/operateCat',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/operateCat',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/operateCat/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/operateCat',
    method: 'put',
    data
  })
}

export function getById(id) {
  return request({
    url: 'api/queryById?id=' + id,
    method: 'get'
  })
}

export default { add, edit, del }
