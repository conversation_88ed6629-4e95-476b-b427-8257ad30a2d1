import request from '@/utils/request'

// 查询系统用户表列表
export function listUpmsUser (query) {
  return request({
    url: '/system/upmsUser/list',
    method: 'get',
    params: query
  })
}

// 查询系统用户表详细
export function getUpmsUser (id) {
  return request({
    url: '/system/upmsUser/' + id,
    method: 'get'
  })
}

// 新增系统用户表
export function addUpmsUser (data) {
  return request({
    url: '/system/upmsUser',
    method: 'post',
    data: data
  })
}

// 修改系统用户表
export function updateUpmsUser (data) {
  return request({
    url: '/system/upmsUser',
    method: 'put',
    data: data
  })
}

// 删除系统用户表
export function delUpmsUser (id) {
  return request({
    url: '/system/upmsUser/' + id,
    method: 'delete'
  })
}


// 导出系统用户表
export function exportUpmsUser (query) {
  return request({
    url: '/system/upmsUser/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/upmsUser/getInitData/' + dictTypes,
    method: 'get'
  })
}
