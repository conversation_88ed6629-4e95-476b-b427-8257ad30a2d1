import request from '@/utils/request'

// 查询环节操作表列表
export function listBpmStepOperate (query) {
  return request({
    url: '/system/bpmStepOperate/list',
    method: 'get',
    params: query
  })
}

// 查询环节操作表详细
export function getBpmStepOperate (id) {
  return request({
    url: '/system/bpmStepOperate/' + id,
    method: 'get'
  })
}

// 新增环节操作表
export function addBpmStepOperate (data) {
  return request({
    url: '/system/bpmStepOperate',
    method: 'post',
    data: data
  })
}

// 修改环节操作表
export function updateBpmStepOperate (data) {
  return request({
    url: '/system/bpmStepOperate',
    method: 'put',
    data: data
  })
}

// 删除环节操作表
export function delBpmStepOperate (id) {
  return request({
    url: '/system/bpmStepOperate/' + id,
    method: 'delete'
  })
}

// 导出环节操作表
export function exportBpmStepOperate (query) {
  return request({
    url: '/system/bpmStepOperate/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmStepOperate/getInitData/' + dictTypes,
    method: 'get'
  })
}
