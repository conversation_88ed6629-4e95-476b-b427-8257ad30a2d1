import request from '@/utils/request'

// 查询系统组织表列表
export function listUpmsDept (query) {
  return request({
    url: '/system/upmsDept/list',
    method: 'get',
    params: query
  })
}

// 查询系统组织表详细
export function getUpmsDept (id) {
  return request({
    url: '/system/upmsDept/' + id,
    method: 'get'
  })
}

// 新增系统组织表
export function addUpmsDept (data) {
  return request({
    url: '/system/upmsDept',
    method: 'post',
    data: data
  })
}

// 修改系统组织表
export function updateUpmsDept (data) {
  return request({
    url: '/system/upmsDept',
    method: 'put',
    data: data
  })
}

// 删除系统组织表
export function delUpmsDept (id) {
  return request({
    url: '/system/upmsDept/' + id,
    method: 'delete'
  })
}


// 导出系统组织表
export function exportUpmsDept (query) {
  return request({
    url: '/system/upmsDept/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/upmsDept/getInitData/' + dictTypes,
    method: 'get'
  })
}
