import request from '@/utils/request'

// 查询节假日方案列表
export function listItsmDutyHolidayScheme (query) {
  return request({
    url: '/system/itsmDutyHolidayScheme/list',
    method: 'get',
    params: query
  })
}

// 查询节假日方案详细
export function getItsmDutyHolidayScheme (schemeName) {
  return request({
    url: '/system/itsmDutyHolidayScheme/' + schemeName,
    method: 'get'
  })
}

// 新增节假日方案
export function addItsmDutyHolidayScheme (data) {
  return request({
    url: '/system/itsmDutyHolidayScheme',
    method: 'post',
    data: data
  })
}

// 修改节假日方案
export function updateItsmDutyHolidayScheme (data) {
  return request({
    url: '/system/itsmDutyHolidayScheme',
    method: 'put',
    data: data
  })
}

// 删除节假日方案
export function delItsmDutyHolidayScheme (schemeName) {
  return request({
    url: '/system/itsmDutyHolidayScheme/' + schemeName,
    method: 'delete'
  })
}


// 导出节假日方案
export function exportItsmDutyHolidayScheme (query) {
  return request({
    url: '/system/itsmDutyHolidayScheme/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyHolidayScheme/getInitData/' + dictTypes,
    method: 'get'
  })
}
