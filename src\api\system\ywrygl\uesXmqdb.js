import request from '@/utils/request'

// 查询项目清单表列表
export function listUesXmqdb (query) {
  return request({
    url: '/system/uesXmqdb/list',
    method: 'get',
    params: query
  })
}

// 查询项目清单表详细
export function getUesXmqdb (id) {
  return request({
    url: '/system/uesXmqdb/' + id,
    method: 'get'
  })
}

// 新增项目清单表
export function addUesXmqdb (data) {
  return request({
    url: '/system/uesXmqdb',
    method: 'post',
    data: data
  })
}

// 修改项目清单表
export function updateUesXmqdb (data) {
  return request({
    url: '/system/uesXmqdb',
    method: 'put',
    data: data
  })
}

// 删除项目清单表
export function delUesXmqdb (id) {
  return request({
    url: '/system/uesXmqdb/' + id,
    method: 'delete'
  })
}

// 导出项目清单表
export function exportUesXmqdb (query) {
  return request({
    url: '/system/uesXmqdb/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesXmqdb/getInitData/' + dictTypes,
    method: 'get'
  })
}
