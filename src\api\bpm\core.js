import request from '@/utils/request'

export function getAddDocInfo(data) {
  return request({
    url: '/api/bpm/runtime/startflow/starttask',
    method: 'post',
    data
  })
}

export function getDocInfo(data) {
  return request({
    url: '/api/bpm/runtime/startflow/openpending',
    method: 'put',
    data
  })
}

export function getReadDocInfo(data) {
  return request({
    url: '/api/toread/openread',
    method: 'put',
    data
  })
}

export function saveDocInfo(data) {
  return request({
    url: '/api/bpm/runtime/startflow/save/formdata',
    method: 'post',
    data
  })
}

export function delDoc(todoId, taskid) {
  return request({
    url: '/api/bpm/runtime/operation/delete/draft/' + todoId + '/' + taskid,
    method: 'delete'
  })
}

export function apiRfe(todoId, taskid) {
  return request({
    url: '/api/bpm/runtime/operation/rfe/' + todoId + '/' + taskid,
    method: 'get'
  })
}
export function apiHangup(todoId, taskid) {
  return request({
    url: '/api/bpm/runtime/operation/hangup/' + todoId + '/' + taskid,
    method: 'get'
  })
}
export function apiResume(todoId, taskid) {
  return request({
    url: '/api/bpm/runtime/operation/resume/' + todoId + '/' + taskid,
    method: 'get'
  })
}

export function getPathByStep(stepId, taskId) {
  return request({
    url: '/api/bpm/runtime/operation/getnowscriptpath/' + stepId + '/' + taskId,
    method: 'get'
  })
}

export function getUsersByStep(data) {
  return request({
    url: '/api/bpm/runtime/operation/path/target/user',
    method: 'put',
    data
  })
}

export function getBackUser(data) {
  return request({
    url: 'api/bpm/runtime/operation/path/back/user',
    method: 'put',
    data
  })
}

export function saveOpinion(data) { //暂存意见
  return request({
    url: '/api/bpm/runtime/operation/saveOpinion',
    method: 'post',
    data
  })
}

export function getOpinion(data) { //读取暂存意见
  return request({
    url: '/api/bpm/runtime/operation/getSelfOponionByTodoId',
    method: 'put',
    data
  })
}

export function getNextCurInfo(stepId) { //获取用户选择的下一环节的信息
  return request({
    url: '/api/stepInfo/getStepInfoById/' + stepId,
    method: 'get'
  })
}

export function gotoNextSetp(data) { //去下一个环节
  return request({
    url: '/api/bpm/runtime/operation/gotoNextStep',
    method: 'put',
    data
  })
}

export function recycling(data) { //回收办结文件
  return request({
    url: '/api/bpm/runtime/manager/recycling',
    method: 'put',
    data
  })
}

export function dealPending(data) { //是否当前环节最后一个处理人
  return request({
    url: '/api/bpm/runtime/operation/dealPending',
    method: 'put',
    data
  })
}

export function assisting(data) { //送协办
  return request({
    url: '/api/bpm/runtime/operation/assisting',
    method: 'put',
    data
  })
}

export function allUserTreeAssist(data) { //送协办选人
  return request({
    url: '/api/bpm/runtime/selectuser/allUserTree/assist',
    method: 'put',
    data
  })
}

// 单独获取任务流转时信息
export function getTaskInfo(taskId) {
  return request({
    url: '/api/bpm/runtask/get/' + taskId,
    method: 'get'
  })
}

export function getDocLog(id) { //流转日志
  return request({
    url: '/api/bpm/runtime/operation/getbpmlog/' + id,
    method: 'get'
  })
}

export function saveTodotitle(data) { //流转日志
  return request({
    url: '/api/bpm/runtime/startflow/save/todotitle',
    method: 'put',
    data
  })
}

export function getOwnOpinion(userId, fileId) { //流转日志
  return request({
    url: '/api/opinion/getOwnOpinion/' + userId + '/' + fileId,
    method: 'get'
  })
}

export function getAllUserByDept(deptId) { //按部门查询该部门所有人
  return request({
    url: '/api/bpm/runtime/selectuser/allUserTree/' + deptId,
    method: 'get'
  })
}

// 根据任务id获取流转意见
export function getAllOpinion(id) { //流转日志
  return request({
    url: '/api/bpm/runtime/operation/getbpmopinion/' + id,
    method: 'get'
  })
}

// 根据任务id获取流转意见以及会签、办理意见
export function getAllOpinionByGroup(id) { //流转日志
  return request({
    url: '/api/bpm/runtime/operation/getbpmopinion/group/' + id,
    method: 'get'
  })
}

//获取个人留言
export function getBpmMessage(data) { //流转日志
  return request({
    url: '/api/bpm/message/getBpmMessage',
    method: 'put',
    data
  })
}

//获取个人留言未读数
export function getBpmMessageNumber(data) { //流转日志
  return request({
    url: '/api/bpm/message/getBpmMessageNumber',
    method: 'put',
    data
  })
}

//获取该流程所有环节
export function getAllStep(id) {
  return request({
    url: '/api/stepInfo/queryStepByflowId/' + id,
    method: 'get'
  })
}

//获取分发配置信息
export function getByfileid(id) {
  return request({
    url: '/api/sendConfig/getByfileid/' + id,
    method: 'get'
  })
}
//获取会签配置信息
export function getHqConfigByfileid(id) {
  return request({
    url: '/api/countersignConfig/getByfileid/' + id,
    method: 'get'
  })
}

//分发产生待阅
export function sendToRead(data) {
  return request({
    url: '/api/toread/sendToRead',
    method: 'post',
    data
  })
}

//待阅变已阅
export function updateToRead(data) { //流转日志
  return request({
    url: '/api/toread/updateToRead',
    method: 'put',
    data
  })
}

//待阅变已阅
export function getFlowStatus(taskId) { //流转信息
  return request({
    url: '/api/bpm/runtime/manager/getMyRuntimeVo/' + taskId,
    method: 'get'
  })
}

//文件分发，发文转收文
export function sendFile(sendInfo) { //流转信息
  return request({
    url: '/api/bpm/runtime/tosend/start',
    data: sendInfo,
    method: 'post'
  })
}
// 查看分发日志
export function getSendFileLog(taskId) { //流转信息
  return request({
    url: '/api/bpm/runtime/tosend/getsendrecord/' + taskId,
    method: 'get'
  })
}

// 删除已分发
export function delSendFile(sendInfo) {
  return request({
    url: '/api/bpm/runtime/tosend/delete',
    data: sendInfo,
    method: 'put'
  })
}

//文件会签
export function sendHQFile(sendInfo) { //流转信息
  return request({
    url: '/api/bpm/runtime/tosendhq/start',
    data: sendInfo,
    method: 'post'
  })
}
// 查看会签日志
export function getSendHQFileLog(taskId) { //流转信息
  return request({
    url: '/api/bpm/runtime/tosendhq/getsendrecord/' + taskId,
    method: 'get'
  })
}

//检查会签是否完成
export function getHQIscomplete(taskId) {
  return request({
    url: '/api/bpm/runtime/tosendhq/getsendrecord/iscomplete/' + taskId,
    method: 'get'
  })
}

// 删除会签
export function delSendHQFile(sendInfo) {
  return request({
    url: '/api/bpm/runtime/tosendhq/delete',
    data: sendInfo,
    method: 'put'
  })
}

// 会签回收，相当于办结子流程
export function recycSendHQFile(sendInfo) {
  return request({
    url: '/api/bpm/runtime/tosendhq/recycling',
    data: sendInfo,
    method: 'put'
  })
}

// 后台用数科的转换服务转ofd文件
export function createOfd(attachId, parentDocId, token) {
  return request({
    url: '/api/office/officetoofd/' + attachId + '/' + parentDocId + '?token=' + token,
    method: 'get'
  })
}

// 一键审查
export function onePieceReview(data) {
  return request({
    url: '/api/attach/onePieceReview',
    params: data,
    method: 'post'
  })
}
// 审查审查结果
export function queryAnalysisInfo(data) {
  return request({
    url: '/api/attach/queryAnalysisInfo',
    params: data,
    method: 'post'
  })
}
// 获取历史地块
export function queryHisLand(data) {
  return request({
    url: '/api/ruSendjzxm/queryHisLand',
    params: data,
    method: 'get'
  })
}
// 地块叠加分析
export function clipGeoJsonStrByGeoJsonStr(data) {
  return request({
    url: '/api/gis/clipGeoJsonStrByGeoJsonStr',
    data,
    method: 'post'
  })
}
// 计算地块面积
export function selectFileAreaNow(data) {
  return request({
    url: '/api/attach/selectFileAreaNow',
    data,
    method: 'post'
  })
}
// 获取文件面积
export function selectFileArea(data) {
  return request({
    url: '/api/attach/selectFileArea',
    params: data,
    method: 'get'
  })
}
// 批量获取文件面积
export function selectAllFileArea(data) {
  return request({
    url: '/api/attach/selectFileAreas',
    params: data,
    method: 'get'
  })
}

export default {
  getAddDocInfo,
  saveOpinion,
  getOpinion,
  getNextCurInfo,
  gotoNextSetp,
  dealPending,
  getBackUser,
  getTaskInfo,
  assisting,
  saveTodotitle,
  getOwnOpinion,
  getAllUserByDept,
  getAllOpinion,
  getBpmMessage,
  getBpmMessageNumber,
  getAllStep,
  getByfileid,
  getHqConfigByfileid,
  sendToRead,
  updateToRead,
  allUserTreeAssist,
  sendFile,
  getSendFileLog,
  createOfd,
  getHQIscomplete,
  onePieceReview,
  queryAnalysisInfo,
  queryHisLand,
  apiRfe,
  apiHangup,
  apiResume,
  clipGeoJsonStrByGeoJsonStr,
  selectFileAreaNow,
  selectFileArea,
  selectAllFileArea
}
