import request from '@/utils/request'

// 查询通用报障用户表列表
export function listItsmCommonDisableduser (query) {
  return request({
    url: '/system/itsmCommonDisableduser/list',
    method: 'get',
    params: query
  })
}

// 查询通用报障用户表详细
export function getItsmCommonDisableduser (user) {
  return request({
    url: '/system/itsmCommonDisableduser/' + user,
    method: 'get'
  })
}

// 新增通用报障用户表
export function addItsmCommonDisableduser (data) {
  return request({
    url: '/system/itsmCommonDisableduser',
    method: 'post',
    data: data
  })
}

// 修改通用报障用户表
export function updateItsmCommonDisableduser (data) {
  return request({
    url: '/system/itsmCommonDisableduser',
    method: 'put',
    data: data
  })
}

// 删除通用报障用户表
export function delItsmCommonDisableduser (user) {
  return request({
    url: '/system/itsmCommonDisableduser/' + user,
    method: 'delete'
  })
}


// 导出通用报障用户表
export function exportItsmCommonDisableduser (query) {
  return request({
    url: '/system/itsmCommonDisableduser/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmCommonDisableduser/getInitData/' + dictTypes,
    method: 'get'
  })
}

export function searchDeptListByType (name,type) {
  return request({
    url: '/system/dept/searchDeptListByType?deptName='+name+'&deptType='+type,
    method: 'get'
  })
}
