import request from '@/utils/request'

// 查询知识审核表列表
export function listItsmKnowledgeCheck (query) {
  return request({
    url: '/system/itsmKnowledgeCheck/list',
    method: 'get',
    params: query
  })
}

// 查询知识审核表详细
export function getItsmKnowledgeCheck (checkUser) {
  return request({
    url: '/system/itsmKnowledgeCheck/' + checkUser,
    method: 'get'
  })
}

// 新增知识审核表
export function addItsmKnowledgeCheck (data) {
  return request({
    url: '/system/itsmKnowledgeCheck',
    method: 'post',
    data: data
  })
}

// 修改知识审核表
export function updateItsmKnowledgeCheck (data) {
  return request({
    url: '/system/itsmKnowledgeCheck',
    method: 'put',
    data: data
  })
}

// 删除知识审核表
export function delItsmKnowledgeCheck (checkUser) {
  return request({
    url: '/system/itsmKnowledgeCheck/' + checkUser,
    method: 'delete'
  })
}


// 导出知识审核表
export function exportItsmKnowledgeCheck (query) {
  return request({
    url: '/system/itsmKnowledgeCheck/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmKnowledgeCheck/getInitData/' + dictTypes,
    method: 'get'
  })
}
