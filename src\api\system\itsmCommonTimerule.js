import request from '@/utils/request'

// 查询ITSM超时规则列表
export function listItsmCommonTimerule (query) {
  return request({
    url: '/system/itsmCommonTimerule/list',
    method: 'get',
    params: query
  })
}

// 查询ITSM超时规则详细
export function getItsmCommonTimerule (module) {
  return request({
    url: '/system/itsmCommonTimerule/' + module,
    method: 'get'
  })
}

// 新增ITSM超时规则
export function addItsmCommonTimerule (data) {
  return request({
    url: '/system/itsmCommonTimerule',
    method: 'post',
    data: data
  })
}

// 修改ITSM超时规则
export function updateItsmCommonTimerule (data) {
  return request({
    url: '/system/itsmCommonTimerule',
    method: 'put',
    data: data
  })
}

// 删除ITSM超时规则
export function delItsmCommonTimerule (module) {
  return request({
    url: '/system/itsmCommonTimerule/' + module,
    method: 'delete'
  })
}


// 导出ITSM超时规则
export function exportItsmCommonTimerule (query) {
  return request({
    url: '/system/itsmCommonTimerule/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmCommonTimerule/getInitData/' + dictTypes,
    method: 'get'
  })
}
