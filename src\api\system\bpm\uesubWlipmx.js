import request from '@/utils/request'

// 查询网络ip明细表列表
export function listUesubWlipmx (query) {
  return request({
    url: '/system/uesubWlipmx/list',
    method: 'get',
    params: query
  })
}

// 查询网络ip明细表详细
export function getUesubWlipmx (id) {
  return request({
    url: '/system/uesubWlipmx/' + id,
    method: 'get'
  })
}

// 新增网络ip明细表
export function addUesubWlipmx (data) {
  return request({
    url: '/system/uesubWlipmx',
    method: 'post',
    data: data
  })
}

// 修改网络ip明细表
export function updateUesubWlipmx (data) {
  return request({
    url: '/system/uesubWlipmx',
    method: 'put',
    data: data
  })
}

// 删除网络ip明细表
export function delUesubWlipmx (id) {
  return request({
    url: '/system/uesubWlipmx/' + id,
    method: 'delete'
  })
}

// 导出网络ip明细表
export function exportUesubWlipmx (query) {
  return request({
    url: '/system/uesubWlipmx/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesubWlipmx/getInitData/' + dictTypes,
    method: 'get'
  })
}
