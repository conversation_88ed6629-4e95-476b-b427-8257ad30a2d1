import request from '@/utils/request'

// 查询服务合同表列表
export function listItsmQualityContract (query) {
  return request({
    url: '/system/itsmQualityContract/list',
    method: 'get',
    params: query
  })
}

// 查询服务合同表详细
export function getItsmQualityContract (id) {
  return request({
    url: '/system/itsmQualityContract/' + id,
    method: 'get'
  })
}

// 新增服务合同表
export function addItsmQualityContract (data) {
  return request({
    url: '/system/itsmQualityContract',
    method: 'post',
    data: data
  })
}

// 修改服务合同表
export function updateItsmQualityContract (data) {
  return request({
    url: '/system/itsmQualityContract',
    method: 'put',
    data: data
  })
}

// 删除服务合同表
export function delItsmQualityContract (id) {
  return request({
    url: '/system/itsmQualityContract/' + id,
    method: 'delete'
  })
}


// 导出服务合同表
export function exportItsmQualityContract (query) {
  return request({
    url: '/system/itsmQualityContract/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmQualityContract/getInitData/' + dictTypes,
    method: 'get'
  })
}

export function busDict (query) {
  return request({
    url: '/system/itsmqualityprovider/busDict',
    method: 'get',
  })
}

export function getContractInfo (id) {
  return request({
    url: '/system/itsmQualityContract/getContractInfo/' + id,
    method: 'get'
  })
}

export function getContractAll () {
  return request({
    url: '/system/itsmQualityContract/list4select' ,
    method: 'get'
  })
}

export function getScore (id) {
  return request({
    url: '/system/itsmQualityContract/getScore/' + id,
    method: 'get'
  })
}

export function delJX (id) {
  return request({
    url: '/system/itsmqualityassessdatalink/delJX?assessNo=' + id,
    method: 'get'
  })
}


export function exportItsmdatalink (data) {
  return request({
    url: '/system/itsmqualityassessdatalink/export',
    method: 'post',
    data: data
  })
}

export function removeJFK (id) {
  return request({
    url: '/system/itsmqualityscorecard/remove?id=' + id,
    method: 'get'
  })
}


export function getFWJXTJ(year) {
  return request({
    url: '/system/itsmqualityscorecard/getTJ?year='+year,
    method: 'get'
  })
}


export function getRightList(data) {
  return request({
   url: '/system/itsmqualityassessdatalink/listdashboard',
    method: 'post',
    data: data
  })
}

export function getRightList2(data) {
  return request({
   url: '/system/itsmqualityassessdatalink/listdashboard2',
    method: 'post',
    data: data
  })
}

export function getRightList3(data) {
  return request({
   url: '/system/itsmqualityassessdatalink/listdashboard3',
    method: 'post',
    data: data
  })
}