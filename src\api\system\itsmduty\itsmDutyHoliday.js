import request from '@/utils/request'

// 查询值班管理-假期方案列表
export function listItsmDutyHoliday (query) {
  return request({
    url: '/system/itsmDutyHoliday/list',
    method: 'get',
    params: query
  })
}

// 查询值班管理-假期方案详细
export function getItsmDutyHoliday (id) {
  return request({
    url: '/system/itsmDutyHoliday/' + id,
    method: 'get'
  })
}

// 新增值班管理-假期方案
export function addItsmDutyHoliday (data) {
  return request({
    url: '/system/itsmDutyHoliday',
    method: 'post',
    data: data
  })
}

// 修改值班管理-假期方案
export function updateItsmDutyHoliday (data) {
  return request({
    url: '/system/itsmDutyHoliday',
    method: 'put',
    data: data
  })
}

// 删除值班管理-假期方案
export function delItsmDutyHoliday (id) {
  return request({
    url: '/system/itsmDutyHoliday/' + id,
    method: 'delete'
  })
}


// 导出值班管理-假期方案
export function exportItsmDutyHoliday (query) {
  return request({
    url: '/system/itsmDutyHoliday/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyHoliday/getInitData/' + dictTypes,
    method: 'get'
  })
}
