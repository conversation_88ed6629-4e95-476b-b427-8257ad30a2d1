import request from '@/utils/request'

// 查询值班管理-值班人员列表
export function listItsmDutyStaff (query) {
  return request({
    url: '/system/itsmDutyStaff/list',
    method: 'get',
    params: query
  })
}

// 查询值班管理-值班人员详细
export function getItsmDutyStaff (id) {
  return request({
    url: '/system/itsmDutyStaff/' + id,
    method: 'get'
  })
}

// 新增值班管理-值班人员
export function addItsmDutyStaff (data) {
  return request({
    url: '/system/itsmDutyStaff',
    method: 'post',
    data: data
  })
}

// 修改值班管理-值班人员
export function updateItsmDutyStaff (data) {
  return request({
    url: '/system/itsmDutyStaff',
    method: 'put',
    data: data
  })
}

// 删除值班管理-值班人员
export function delItsmDutyStaff (id) {
  return request({
    url: '/system/itsmDutyStaff/' + id,
    method: 'delete'
  })
}


// 导出值班管理-值班人员
export function exportItsmDutyStaff (query) {
  return request({
    url: '/system/itsmDutyStaff/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyStaff/getInitData/' + dictTypes,
    method: 'get'
  })
}
