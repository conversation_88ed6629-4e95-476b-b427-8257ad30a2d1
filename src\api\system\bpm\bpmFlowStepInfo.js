import request from '@/utils/request'

// 查询流程环节表列表
export function listBpmFlowStepInfo (query) {
  return request({
    url: '/system/bpmFlowStepInfo/list',
    method: 'get',
    params: query
  })
}

// 查询流程环节表详细
export function getBpmFlowStepInfo (id) {
  return request({
    url: '/system/bpmFlowStepInfo/' + id,
    method: 'get'
  })
}

// 新增流程环节表
export function addBpmFlowStepInfo (data) {
  return request({
    url: '/system/bpmFlowStepInfo',
    method: 'post',
    data: data
  })
}

// 修改流程环节表
export function updateBpmFlowStepInfo (data) {
  return request({
    url: '/system/bpmFlowStepInfo',
    method: 'put',
    data: data
  })
}

// 删除流程环节表
export function delBpmFlowStepInfo (id) {
  return request({
    url: '/system/bpmFlowStepInfo/' + id,
    method: 'delete'
  })
}

// 导出流程环节表
export function exportBpmFlowStepInfo (query) {
  return request({
    url: '/system/bpmFlowStepInfo/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmFlowStepInfo/getInitData/' + dictTypes,
    method: 'get'
  })
}
