import request from '@/utils/request'

export function getStatistDevice(st, et) {
  return request({
    url: '/system/uesDevice/getStatistDevice?st=' + st + '&et=' + et,
    method: 'get'
  })
}


export function listJrjfbyHj(query) {
  return request({
    url: '/system/ueSbjrjfsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function statbystep4jrjf(query) {
  return request({
    url: '/system/ueSbjrjfsqd/statbystep',
    method: 'get',
    params: query
  })
}

export function statbydate4jrjf(query) {
  return request({
    url: '/system/ueSbjrjfsqd/statbydate',
    method: 'get',
    params: query
  })
}

export function listTcjfbyHj(query) {
  return request({
    url: '/system/ueSbtcjfsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function statbystep4tcjf(query) {
  return request({
    url: '/system/ueSbtcjfsqd/statbystep',
    method: 'get',
    params: query
  })
}

export function statbydate4tcjf(query) {
  return request({
    url: '/system/ueSbtcjfsqd/statbydate',
    method: 'get',
    params: query
  })
}

export function listZysqbyHj(query) {
  return request({
    url: '/system/ueSbzysqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function statbystep4Zysq(query) {
  return request({
    url: '/system/ueSbzysqd/statbystep',
    method: 'get',
    params: query
  })
}

export function statbydate4Zysq(query) {
  return request({
    url: '/system/ueSbzysqd/statbydate',
    method: 'get',
    params: query
  })
}


export function listwlclktbyHj(query) {
  return request({
    url: '/system/ueWlclktsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function statbystep4wlclkt(query) {
  return request({
    url: '/system/ueWlclktsqd/statbystep',
    method: 'get',
    params: query
  })
}

export function statbydate4wlclkt(query) {
  return request({
    url: '/system/ueWlclktsqd/statbydate',
    method: 'get',
    params: query
  })
}

export function listWlipsbyHj(query) {
  return request({
    url: '/system/ueWlipsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function statbystep4Wlips(query) {
  return request({
    url: '/system/ueWlipsqd/statbystep',
    method: 'get',
    params: query
  })
}

export function statbydate4Wlips(query) {
  return request({
    url: '/system/ueWlipsqd/statbydate',
    method: 'get',
    params: query
  })
}

export function listYjptzbyHj(query) {
  return request({
    url: '/system/ueYjptzhqxsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function statbystep4Yjptz(query) {
  return request({
    url: '/system/ueYjptzhqxsqd/statbystep',
    method: 'get',
    params: query
  })
}

export function statbydate4Yjptz(query) {
  return request({
    url: '/system/ueYjptzhqxsqd/statbydate',
    method: 'get',
    params: query
  })
}

export function listYwrybyHj(query) {
  return request({
    url: '/system/ueYwrybgsqd/pendinglist',
    method: 'get',
    params: query
  })
}

export function statbystep4Ywry(query) {
  return request({
    url: '/system/ueYwrybgsqd/statbystep',
    method: 'get',
    params: query
  })
}

export function statbydate4Ywry(query) {
  return request({
    url: '/system/ueYwrybgsqd/statbydate',
    method: 'get',
    params: query
  })
}

export function itsmEventStatbydate(data) {
  return request({
    url: '/system/itsmEvent/statbydate',
    method: 'post',
    data: data
  })
}
// 首页事件统计分析- 事件工单总情况 (新接口)
export function itsmEventOrderConditions(data) {
  return request({
    url: '/system/itsmEvent/analysis/getOrderConditions',
    method: 'post',
    data: data
  })
}
export function itsmEventYxsjcll(data) {
  return request({
    url: '/system/itsmEvent/yxsjcll',
    method: 'post',
    data: data
  })
}
// 实现按周期统计事件分类统计分析
export function itsmEventSjfltjfx(data) {
  return request({
    url: '/system/itsmEvent/sjfltjfx',
    method: 'post',
    data: data
  })
}

// 实现按周期统计事件分类统计分析 -new接口
export function itsmEventTypeConditions(data) {
  return request({
    url: '/system/itsmEvent/analysis/getTypeConditions',
    method: 'post',
    data: data
  })
}
// 分析事件分类总情况占比及趋势分析
export function itsmEventSjflzqkzbqs(data) {
  return request({
    url: '/system/itsmEvent/sjflzqkzbqs',
    method: 'post',
    data: data
  })
}

// 实现按周期统计事件对象统计分析
export function itsmEventSjdxzb(data) {
  return request({
    url: '/system/itsmEvent/sjdxzb',
    method: 'post',
    data: data
  })
}
//实现按周期统计事件对象总情况占比及趋势分析
export function itsmEventSjdxzqkzbqs(data) {
  return request({
    url: '/system/itsmEvent/sjdxzqkzbqs',
    method: 'post',
    data: data
  })
}
// 首页事件统计分析- 事件对象统计分析 （新接口）
export function itsmEventObjectConditions(data) {
  return request({
    url: '/system/itsmEvent/analysis/getObjectConditions',
    method: 'post',
    data: data
  })
}

export function sjhjtltj(data) {
  return request({
    url: '/system/itsmEvent/sjhjtltj',
    method: 'post',
    data: data
  })
}

//首页/事件统计工作台事件登记人TOPN
export function itsmEventSjdjrtj(data) {
  return request({
    url: '/system/itsmEvent/sjdjrtj',
    method: 'post',
    data: data
  })
}

//首页事件统计分析- 事件登记人TopN （新接口）
export function itsmEventRegisterUserTop(data) {
  return request({
    url: '/system/itsmEvent/analysis/getRegisterUserTop',
    method: 'post',
    data: data
  })
}


//首页/事件统计工作台事件登记单位TOPN
export function itsmEventSjdjdw(data) {
  return request({
    url: '/system/itsmEvent/sjdjdw',
    method: 'post',
    data: data
  })
}
// 首页事件统计分析- 事件登记单位TOPN （新接口）
export function itsmEventRegisterUnitTop(data) {
  return request({
    url: '/system/itsmEvent/analysis/getRegisterUnitTop',
    method: 'post',
    data: data
  })
}

// 故障工单情况
export function itsmTroubleStatbydate(data) {
  return request({
    url: '/system/itsmTrouble/statbydate',
    method: 'post',
    data: data
  })
}

// 故障工单情况  （新接口）
export function itsmTroubleOrderConditions(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getOrderConditions',
    method: 'post',
    data: data
  })
}
// 系统故障率 可用率
export function itsmTroubleGzsctj(data) {
  return request({
    url: '/system/itsmTrouble/gzsctj',
    method: 'post',
    data: data
  })
}
// 系统故障率 可用率 (新接口)
export function itsmTroubleReport(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getReport',
    method: 'post',
    data: data
  })
}
// http://localhost:8183/system/itsmTrouble/gzsltj post 故障数量统计
export function itsmTroubleGzsltj(data) {
  return request({
    url: '/system/itsmTrouble/gzsltj',
    method: 'post',
    data: data
  })
}
//实现按周期统计故障责任单位占比 http://localhost:8183/system/itsmTrouble/gzzrdwtj   post
export function itsmTroubleGzzrdwtj(data) {
  return request({
    url: '/system/itsmTrouble/gzzrdwtj',
    method: 'post',
    data: data
  })
}

export function sftjgzbg(data) {
  return request({
    url: '/system/itsmTrouble/sftjgzbg',
    method: 'post',
    data: data
  })
}
// 提交故障报告情况
export function itsmTroubleSubmitReportConditions(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getSubmitReportConditions',
    method: 'post',
    data: data
  })
}

// 实现按周期统计故障责任单位趋势。http://localhost:8183/system/itsmTrouble/gzzrdwqs  post
export function itsmTroubleGzzrdwqs(data) {
  return request({
    url: '/system/itsmTrouble/gzzrdwqs',
    method: 'post',
    data: data
  })
}
//实现按周期统计故障类型统计分析，分析故障类型总情况占比 http://localhost:8183/system/itsmTrouble/gzlxzqkzb  post
export function itsmTroubleGzlxzqkzb(data) {
  return request({
    url: '/system/itsmTrouble/gzlxzqkzb',
    method: 'post',
    data: data
  })
}
// 故障类型统计分析 （新接口）

//实现按周期统计故障类型统计分析  （新接口）
export function itsmTroubleTypeConditions(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getTypeConditions',
    method: 'post',
    data: data
  })
}
//实现按周期统计故障类型统计分析，分析故障类型总情况占比 http://localhost:8183/system/itsmTrouble/gzlxzqkzb  post
export function gzcstjzb(data) {
  return request({
    url: '/system/itsmTrouble/gzcstjzb',
    method: 'post',
    data: data
  })
}
// 问题处理及时率（旧）
export function wtcstjzb(data) {
  return request({
    url: '/system/itsmProblem/wtcstjzb',
    method: 'post',
    data: data
  })
}
//  需求处理及时率(旧)
export function xqcstjzb(data) {
  return request({
    url: '/system/itsmDemand/xqcstjzb',
    method: 'post',
    data: data
  })
}
//实现按周期统计故障类型统计分析，分析故障类型总情况趋势 http://localhost:8183/system/itsmTrouble/gzlxzqs  post
export function itsmTroubleGzlxzqs(data) {
  return request({
    url: '/system/itsmTrouble/gzlxzqs',
    method: 'post',
    data: data
  })
}
// 故障责任单位情况
export function gzzrdw2(data) {
  return request({
    url: '/system/itsmTrouble/gzzrdw2',
    method: 'post',
    data: data
  })
}
// 故障责任单位情况 (新接口)
export function itsmTroubleBlameConditions(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getBlameConditions',
    method: 'post',
    data: data
  })
}
// 故障工单处理及时率  （旧接口）
export function gdzljsl(data) {
  return request({
    url: '/system/itsmTrouble/gdzljsl',
    method: 'post',
    data: data
  })
}
// 故障工单处理及时率 （新接口）
export function itsmTroubleTimeoutConditions(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getTimeoutConditions',
    method: 'post',
    data: data
  })
}

//实现按周期统计故障类型统计分析，分析硬件故障占比情况 http://localhost:8183/system/itsmTrouble/yjgzzb post
export function itsmTroubleYjgzzb(data) {
  return request({
    url: '/system/itsmTrouble/yjgzzb',
    method: 'post',
    data: data
  })
}
//实现按周期统计故障类型统计分析，分析硬件故障趋势分析 http://localhost:8183/system/itsmTrouble/yjgzzb  post
export function itsmTroubleYjgzqs(data) {
  return request({
    url: '/system/itsmTrouble/yjgzqs',
    method: 'post',
    data: data
  })
}
//实现按周期统计故障类型统计分析，分析软件故障占比情况 http://localhost:8183/system/itsmTrouble/rjgzzb post
export function itsmTroublRjgzzb(data) {
  return request({
    url: '/system/itsmTrouble/rjgzzb',
    method: 'post',
    data: data
  })
}
//实现按周期统计故障类型统计分析，分析软件故障趋势分析 http://localhost:8183/system/itsmTrouble/rjgzqs post
export function itsmTroublRjgzqs(data) {
  return request({
    url: '/system/itsmTrouble/rjgzqs',
    method: 'post',
    data: data
  })
}

//实现按周期统计分析故障系统模块占比 http://localhost:8183/system/itsmTrouble/gzxtmkzb post
export function itsmTroublGzxtmkzb(data) {
  return request({
    url: '/system/itsmTrouble/gzxtmkzb',
    method: 'post',
    data: data
  })
}
//实现按周期统计分析故障系统模块情况  （新接口）
export function itsmTroublModelConditions(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getModelConditions',
    method: 'post',
    data: data
  })
}
//实现按周期统计分析故障系统模块趋势分析。http://localhost:8183/system/itsmTrouble/gzxtmkqs  post
export function itsmTroublGzxtmkqs(data) {
  return request({
    url: '/system/itsmTrouble/gzxtmkqs',
    method: 'post',
    data: data
  })
}
//实现按周期统计分析故障登记人、处理人TOPN，N自定义（5、10、15、20） http://localhost:8183/system/itsmTrouble/gzdjrtj post
export function itsmTroublGzdjrtj(data) {
  return request({
    url: '/system/itsmTrouble/gzdjrtj',
    method: 'post',
    data: data
  })
}

// 故障登记人 （新接口）
export function itsmTroubleRegisterUserTop(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getRegisterUserTop',
    method: 'post',
    data: data
  })
}
// 故障处理人
export function gzclrtj(data) {
  return request({
    url: '/system/itsmTrouble/gzclrtj',
    method: 'post',
    data: data
  })
}
// 故障处理人 （新接口）
export function itsmTroubleHandlerTop(data) {
  return request({
    url: '/system/itsmTrouble/analysis/getHandlerTop',
    method: 'post',
    data: data
  })
}
// gzclrtj


// 实现按周期统计分析按已超时、未超时的事件工单的占比情况。
export function itsmEventSjgdcszbqk(data) {
  return request({
    url: '/system/itsmEvent/sjgdcszbqk',
    method: 'post',
    data: data
  })
}

// 首页事件统计分析-事件工单处理及时率 （新接口）
export function itsmEventTimeOutConditions(data) {
  return request({
    url: '/system/itsmEvent/analysis/getTimeOutConditions',
    method: 'post',
    data: data
  })
}


//事件按时处理人TOPN
export function itsmEventSjasclr(data) {
  return request({
    url: '/system/itsmEvent/sjasclr',
    method: 'post',
    data: data
  })
}

//首页事件统计分析-事件按时处理人TOPN （新接口）
export function itsmEventHandlerTop(data) {
  return request({
    url: '/system/itsmEvent/analysis/getHandlerTop',
    method: 'post',
    data: data
  })
}

// 事件超时处理人TOPN
export function itsmEventSjcsclr(data) {
  return request({
    url: '/system/itsmEvent/sjcsclr',
    method: 'post',
    data: data
  })
}

// 首页事件统计分析-事件超时处理人TOPN （新接口）
export function itsmEventTimeoutHandlerTop(data) {
  return request({
    url: '/system/itsmEvent/analysis/getTimeoutHandlerTop',
    method: 'post',
    data: data
  })
}


//事件处理单位TOPN
export function itsmEventSjcldw(data) {
  return request({
    url: '/system/itsmEvent/sjcldw',
    method: 'post',
    data: data
  })
}

//首页事件统计分析-事件处理单位TOPN （新接口）
export function itsmEventHandleUnitTop(data) {
  return request({
    url: '/system/itsmEvent/analysis/getHandleUnitTop',
    method: 'post',
    data: data
  })
}


// 首页/事件统计/点击图表查看详情
export function homepageList(query) {
  return request({
    url: '/system/itsmEvent/homepage/list',
    method: 'get',
    params: query
  })
}

// 首页/事件统计/点击图表查看详情(导出)
export function itsmEventExport(query) {
  return request({
    url: '/system/itsmEvent/export',
    method: 'get',
    params: query
  })
}

// 首页/故障统计/点击图表查看详情
export function TroubleHomepageList(query) {
  return request({
    url: '/system/itsmTrouble/homepage/list',
    method: 'get',
    params: query
  })
}
// 首页/故障统计/点击图表查看详情(导出)
export function ItsmTroubleExport(query) {
  return request({
    url: '/system/itsmTrouble/export',
    method: 'get',
    params: query
  })
}
// 首页/问题统计/点击图表查看详情
export function ItsmProblemHomepageList(query) {
  return request({
    url: '/system/itsmProblem/homepage/list',
    method: 'get',
    params: query
  })
}
// 首页/问题统计/点击图表查看详情(导出)
export function ItsmProblemExport(query) {
  return request({
    url: '/system/itsmProblem/export',
    method: 'get',
    params: query
  })
}
// 首页/需求统计/点击图表查看详情
export function ItsmDemandmHomepageList(query) {
  return request({
    url: '/system/itsmDemand/homepage/list',
    method: 'get',
    params: query
  })
}

// 首页/需求统计/点击图表查看详情(导出)
export function ItsmDemandExport(query) {
  return request({
    url: '/system/itsmDemand/export',
    method: 'get',
    params: query
  })
}

export function getOrderConditionsdemo(query) {
  return request({
    url: '/system/itsmEvent/analysis/getOrderConditions',
    method: 'get',
    params: query
  })
}


export function gefwjxtj(st,ed) {
  return request({
    url: '/system/itsmqualityassessdatalink/getTj?st='+st+'&ed='+ed,
    method: 'get'
  })
}


export function ItsmEventmHomeListExport(query) {
  return request({
    url: '/system/itsmEvent/homepage/list/export',
    method: 'get',
    params: query
  })
}
