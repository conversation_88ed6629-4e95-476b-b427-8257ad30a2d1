import request from '@/utils/request'

// 查询待办工作列表
export function listBpmTodo (query) {
  return request({
    url: '/aidex-system/bpmTodo/list',
    method: 'get',
    params: query
  })
}

// 查询待办工作详细
export function getBpmTodo (id) {
  return request({
    url: '/aidex-system/bpmTodo/' + id,
    method: 'get'
  })
}

// 新增待办工作
export function addBpmTodo (data) {
  return request({
    url: '/aidex-system/bpmTodo',
    method: 'post',
    data: data
  })
}

// 修改待办工作
export function updateBpmTodo (data) {
  return request({
    url: '/aidex-system/bpmTodo',
    method: 'put',
    data: data
  })
}

// 删除待办工作
export function delBpmTodo (id) {
  return request({
    url: '/aidex-system/bpmTodo/' + id,
    method: 'delete'
  })
}

// 导出待办工作
export function exportBpmTodo (query) {
  return request({
    url: '/aidex-system/bpmTodo/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/aidex-system/bpmTodo/getInitData/' + dictTypes,
    method: 'get'
  })
}
