import request from '@/utils/request'

// 查询工作计划工单信息表列表
export function listItsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/list',
    method: 'get',
    params: query
  })
}

// 查询工作计划工单信息表详细
export function getItsmOperationOrder (mainId) {
  return request({
    url: '/system/itsmOperationOrder/' + mainId,
    method: 'get'
  })
}

// 新增工作计划工单信息表
export function addItsmOperationOrder (data) {
  return request({
    url: '/system/itsmOperationOrder',
    method: 'post',
    data: data
  })
}

// 修改工作计划工单信息表
export function updateItsmOperationOrder (data) {
  return request({
    url: '/system/itsmOperationOrder',
    method: 'put',
    data: data
  })
}

// 删除工作计划工单信息表
export function delItsmOperationOrder (mainId) {
  return request({
    url: '/system/itsmOperationOrder/' + mainId,
    method: 'delete'
  })
}


// 导出工作计划工单信息表
export function exportItsmOperationOrder (query) {
  return request({
    url: '/system/itsmOperationOrder/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmOperationOrder/getInitData/' + dictTypes,
    method: 'get'
  })
}
