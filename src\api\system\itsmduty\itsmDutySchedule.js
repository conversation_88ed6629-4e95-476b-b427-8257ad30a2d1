import request from '@/utils/request'

// 查询值班管理-值班安排表列表
export function listItsmDutySchedule (query) {
  return request({
    url: '/system/itsmDutySchedule/list',
    method: 'get',
    params: query
  })
}

// 查询值班管理-值班安排表详细
export function getItsmDutySchedule (id) {
  return request({
    url: '/system/itsmDutySchedule/' + id,
    method: 'get'
  })
}

// 新增值班管理-值班安排表
export function addItsmDutySchedule (data) {
  return request({
    url: '/system/itsmDutySchedule',
    method: 'post',
    data: data
  })
}

// 修改值班管理-值班安排表
export function updateItsmDutySchedule (data) {
  return request({
    url: '/system/itsmDutySchedule',
    method: 'put',
    data: data
  })
}

// 删除值班管理-值班安排表
export function delItsmDutySchedule (id) {
  return request({
    url: '/system/itsmDutySchedule/' + id,
    method: 'delete'
  })
}


// 导出值班管理-值班安排表
export function exportItsmDutySchedule (query) {
  return request({
    url: '/system/itsmDutySchedule/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutySchedule/getInitData/' + dictTypes,
    method: 'get'
  })
}
