<customUI xmlns="http://schemas.microsoft.com/office/2006/01/customui" onLoad="OnWPSWorkTabLoad">
    <ribbon startFromScratch="false">
        <tabs>
            <tab id="WPSWorkExtTab" label="OA辅助" getVisible="OnGetVisible">                <!-- OA菜单开启显隐处理-->
                <!-- <group id="grpWPSClound" label="WPS云文档相关功能" getVisible="OnGetVisible">
                    <box id="boxBoxCloud1" boxStyle="vertical" visible="true">
                        <button id="btnOpenLocalWPSYUN" label="导入文件" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>
                        <button id="btnImportTemplate" label="导入模板" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>
                        <button id="btnInsertBookmark" label="导入书签" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>
                    </box>
                    <separator id="sepWPSClound"/>
                </group> -->
                <group id="grpWPSWork" label="OA助手文档操作功能组" getVisible="OnGetVisible">
                    <button id="btnSaveToServer" label="保存到OA" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>
                    <button id="btnInsertRedHeader" label="套红头" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>
                    <button id="btnChangeToOFD" label="转OFD保存" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>

                    <box id="boxRevsion3" boxStyle="vertical" visible="true">
                        <button id="btnShowRevision" label="显示痕迹" getLabel="OnGetLabel" getEnabled="OnGetEnabled" getVisible="OnGetVisible" onAction="OnAction" getImage="GetImage" size="large" />
                        <button id="btnHideRevision" label="隐藏痕迹" getLabel="OnGetLabel" getEnabled="OnGetEnabled" getVisible="OnGetVisible" onAction="OnAction" getImage="GetImage" size="large" />
                    </box>

                    <separator id="sepWPSWork" getVisible="OnGetVisible" />
                </group>
                <!--<group id="grpOAExtend" label="扩展功能组" getVisible="OnGetVisible">
                    <button id="btnInsertRedHeader" label="套红头" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>
                    <button id="btnClearRevDoc" label="清稿" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large" />
                    <button id="btnInsertSeal" label="印章" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large" />
                    <separator id="sepOAExtend" getVisible="OnGetVisible" />
                </group>
                <group id="grpRevision" label="OA修订功能按钮组" getVisible="OnGetVisible">
                    <box id="boxRevsion1" boxStyle="horizontal" visible="true">
                        <button id="btnOpenRevision" label="打开修订" onAction="OnAction" getLabel="OnGetLabel" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large" />
                        <button id="btnCloseRevision" label="关闭修订" onAction="OnAction" getLabel="OnGetLabel" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large" />
                    </box>
                    <box id="boxRevsion2" boxStyle="horizontal" visible="true">
                        <button id="btnAcceptAllRevisions" label="接收修订" getLabel="OnGetLabel" getEnabled="OnGetEnabled" getVisible="OnGetVisible" onAction="OnAction" getImage="GetImage" size="large" />
                        <button id="btnRejectAllRevisions" label="拒绝修订" getLabel="OnGetLabel" getEnabled="OnGetEnabled" getVisible="OnGetVisible" onAction="OnAction" getImage="GetImage" size="large" />
                    </box>
                    <box id="boxRevsion3" boxStyle="horizontal" visible="true">
                        <button id="btnShowRevision" label="显示痕迹" getLabel="OnGetLabel" getEnabled="OnGetEnabled" getVisible="OnGetVisible" onAction="OnAction" getImage="GetImage" size="large" />
                        <button id="btnHideRevision" label="隐藏痕迹" getLabel="OnGetLabel" getEnabled="OnGetEnabled" getVisible="OnGetVisible" onAction="OnAction" getImage="GetImage" size="large" />
                    </box>
                    <separator id="sepWPSRevision" getVisible="OnGetVisible" />
                </group>
                <group id="grpFreqUsed" label="常用功能组" getVisible="OnGetVisible">
                    <box id="boxFreqUsed1" boxStyle="horizontal" visible="true">
                        <button id="btnInsertDate" label="插入日期" getLabel="OnGetLabel" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>
                        <button id="btnQRCode" label="二维码" onAction="OnAction" getEnabled="OnGetEnabled" getVisible="OnGetVisible" getImage="GetImage" size="large"/>
                    </box>
                    <separator id="sepWPSFreqUsed" getVisible="OnGetVisible" />
                </group> -->
                 <group id="grpDocSource" label="文件来源" getVisible="OnGetVisible">
                    <box id="boxUserName" boxStyle="horizontal" visible="true">
                        <labelControl id="lblUserName" label="用户" getLabel="OnGetLabel" />
                        <button id="btnUserName" label="用户" getLabel="OnGetLabel" />
                    </box>
                </group>
            </tab>
        </tabs>
    </ribbon>
    <commands>
        <!-- idMso支持getEnabled和onAction，不支持visible属性和事件-->
        <!-- 文档复制和剪切控制-->
        <command idMso="Copy" getEnabled="OnGetEnabled" />
        <command idMso="Cut" getEnabled="OnGetEnabled" />
        <!-- 文档保存和另存控制-->
        <command idMso="FileSave" getEnabled="OnGetEnabled" onAction="OnAction"/>
        <command idMso="SaveAll" getEnabled="OnGetEnabled" />
        <command idMso="FileSaveAsMenu" getEnabled="OnGetEnabled" onAction="OnAction"/>
        <command idMso="FileSaveAs" getEnabled="OnGetEnabled" onAction="OnAction"/>
        <command idMso="FileSaveAsPicture" getEnabled="OnGetEnabled" />
        <command idMso="SaveAsPicture" getEnabled="OnGetEnabled" />
        <command idMso="FileMenuSendMail" getEnabled="OnGetEnabled" />
        <!-- 输出PDF控制-->
        <command idMso="SaveAsPDF" getEnabled="OnGetEnabled"/>
        <command idMso="FileSaveAsPDF" getEnabled="OnGetEnabled"/>
        <command idMso="ExportToPDF" getEnabled="OnGetEnabled"/>
        <command idMso="FileSaveAsPdfOrXps" getEnabled="OnGetEnabled"/>
        <!-- 输出OFD控制-->
        <command idMso="SaveAsOfd" getEnabled="OnGetEnabled"/>
        <command idMso="FileSaveAsOfd" getEnabled="OnGetEnabled"/>
        <!--文档打印控制-->
        <command idMso="FilePrint" getEnabled="OnGetEnabled"/>
        <command idMso="FilePrintMenu" getEnabled="OnGetEnabled"/>
        <command idMso="FilePrintPreview" getEnabled="OnGetEnabled"/>
        <!--文档修订控制-->
        <command idMso="ReviewTrackChangesMenu" getEnabled="OnGetEnabled"/>
        <command idMso="ReviewRejectChangeMenu" getEnabled="OnGetEnabled"/>
        <command idMso="ReviewAcceptChangeMenu" getEnabled="OnGetEnabled"/>
        <!--文档新建控制-->
        <command idMso="FileNewMenu" getEnabled="OnGetEnabled"/>
        <command idMso="FileNew" getEnabled="OnGetEnabled"/>
        <command idMso="WindowNew" getEnabled="OnGetEnabled"/>
        <command idMso="FileNewBlankDocument" getEnabled="OnGetEnabled"/>
    </commands>
    <contextMenus>
        <contextMenu idMso="ContextMenuText">
            <menu id="Menu_ContextMenuText" label="测试右键" visible="true">
                <button id="ShowAlert_ContextMenuText" label="弹出一个警告框" onAction="OnAction" getImage="GetImage" visible="true"/>
            </menu>
        </contextMenu>
    </contextMenus>
</customUI>