import request from '@/utils/request'

// 查询待办表列表
export function listBpmTodo (query) {
  return request({
    url: '/system/bpmTodo/list',
    method: 'get',
    params: query
  })
}

// 查询待办表详细
export function getBpmTodo (id) {
  return request({
    url: '/system/bpmTodo/' + id,
    method: 'get'
  })
}

// 新增待办表
export function addBpmTodo (data) {
  return request({
    url: '/system/bpmTodo',
    method: 'post',
    data: data
  })
}

// 修改待办表
export function updateBpmTodo (data) {
  return request({
    url: '/system/bpmTodo',
    method: 'put',
    data: data
  })
}

// 删除待办表
export function delBpmTodo (id) {
  return request({
    url: '/system/bpmTodo/' + id,
    method: 'delete'
  })
}

// 导出待办表
export function exportBpmTodo (query) {
  return request({
    url: '/system/bpmTodo/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmTodo/getInitData/' + dictTypes,
    method: 'get'
  })
}
