<!DOCTYPE html>
<html lang="en">

<head>
    <title>导入模板</title>
    <meta charset="UTF-8">
    <script type="text/javascript" src='js/main.js'></script>
    <script type="text/javascript" src="otherslib/lib/vue.min.js"></script>
    <style type="text/css">
        * {
            box-sizing: border-box;
        }

        /*清除浮动*/
        .clear:after {
            content: "";
            display: block;
            clear: both;
        }

        html,
        body,
        #template {
            margin: 0;
            padding: 0;
            width: 100%;
            height: 100%;
        }

        .row {
            width: 100%;
            border-top: 2px solid #e7e7e7;
        }

        .row>div {
            height: 100%;
        }

        #file_select {
            width: 100%;
            padding-left: 5%;
        }

        .def_control {
            height: 55%;
            width: 100%;
            font-size: 18px;
        }

        .btn_box {
            width: 16%;
            float: right;
            line-height: 4.5em;
            margin-right: 3%;
        }
    </style>
</head>

<body>

    <div id="template">
        <div class="row" style="height:50%;padding-top: 3%;">
            <div id="file_select">
                <span>文件名：</span>
                <select class="def_control" style="width: 80%;" v-model="templateItem">
                    <option value="-1">请选择模板</option>
                    <option v-for="(item,key) in templates" :value="item.tempId" :key="key">{{item.tempName}}</option>
                </select>
            </div>
        </div>
        <div class="row" style="height: 50%;">
            <div class="btn_box">
                <button class="def_control" type="button" @click="cancel()">取消</button>
            </div>
            <div class="btn_box">
                <button class="def_control" type="button" @click="OnImportTemplate()">导入</button>
            </div>
        </div>
    </div>

</body>

</html>
<script>

    /**
     * 导入公文模板，并替换当前文档全部内容
     * @param templateURL  模板路径
     */
    function importTemlateFile(templateURL) {
        var wpsApp = wps.WpsApplication();
        var activeDoc = wpsApp.ActiveDocument;
        if (!activeDoc) {
            alert("文档不存在");
            return;
        }
        var selection = wpsApp.ActiveWindow.Selection;
        selection.WholeStory(); //选取全文
        selection.Delete();     // 删除选中内容
        selection.InsertFile(templateURL);
        if (activeDoc.Revisions.Count > 0) {  // 文档或区域中的修订
            activeDoc.AcceptAllRevisions();  // 接受对指定文档的所有修订
        }
    }

    // 获取选中项，拼接模板Url进行导入模板
    function OnImportTemplate() {
        var templateId = vm.templateItem;
        console.log(vm);
        if (templateId == -1) {
            alert('请选中模板!!');
            return;
        }
        var p_Doc = wps.WpsApplication().ActiveDocument;
        var templatePath = GetDocParamsValue(p_Doc, "templatePath");
        if (templatePath == "") { templatePath = OA_DOOR.templateBaseURL; }
        var templateURL = templatePath + templateId;

        importTemlateFile(templateURL);
        window.opener = null;
        window.open('', '_self', '');
        window.close();
        wps.OAAssist.ShellExecute("ksowebstartupwps://");
    }


    function cancel() { // 取消按钮
        window.close();
        wps.OAAssist.ShellExecute("ksowebstartupwps://");   // 将WPS程序置前
    }

    var vm = new Vue({
        el: "#template",
        data: {
            templateItem: -1,
            templates: {}
        },
        methods: {
            getAllTemplate: function () {
                var _this = this
                var p_Doc = wps.WpsApplication().ActiveDocument;
                var templateDataUrl = GetDocParamsValue(p_Doc, "templateDataUrl");
                if (templateDataUrl == "") { templateDataUrl = OA_DOOR.templateDataUrl; }
                $.ajax({
                    url: templateDataUrl,
                    async: false,
                    method: "post",
                    dataType: 'json',
                    success: function (res) {
                        _this.templates = res;
                        console.log("模板列表数据：" + JSON.stringify(res));
                    },
                    error: function (res) {
                        alert("获取响应失败");
                        _this.templates = {}
                    }
                });
            }
        },
        mounted: function () {
            this.getAllTemplate();
        }
    });


</script>