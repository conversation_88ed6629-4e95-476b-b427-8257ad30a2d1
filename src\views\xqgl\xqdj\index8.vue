<template>
  <div>
    
    <a-row class="arowstyle">
         <a-col :span="8"><span style="font-size: 28px;font-weight: bold;">{{dLabel}} </span></a-col>
         <a-col :span="16">
            <a-button style="margin: 20px;float:right;" @click="closeThisPage" >关闭</a-button>        
            <a-button type="danger"  v-if="delBtn" style="margin: 20px;float:right;" @click="deleteDoc" >删除</a-button>
            <a-button type="primary" v-if="currentStep!=20"  style="margin: 20px;float:right;" @click="newSaveForm" >保存</a-button>
            <a-button type="primary" v-if="nextBtn" style="margin: 20px;float:right;" @click="showNext">{{ nextstr }}</a-button>
            <a-button type="primary" v-if="kfsclBtn" style="margin: 20px;float:right;" @click="showNext2" >流转至系统开发商处理 </a-button>
            <a-button type="primary" v-if="rewroteBtn" style="margin: 20px;float:right;" @click="rollBack" >退回</a-button>
            <a-button type="primary" v-if="redoBtn" style="margin: 20px;float:right;" @click="rollBack2" >重新处理</a-button>
            <a-button type="danger" v-if="currentStep==10 || currentStep==11" style="margin: 20px;float:right;" >需求取消</a-button>
            <a-modal v-model="nextForm5" title="请选择下一步操作的用户" @ok="subimtNext6" >
              <a-row style="width:100%">
                <a-col :span="8">请选择自动化科业务人员确认：</a-col>
                <a-col :spna="16">
                  <a-checkbox-group v-model="nextUserId" :options="nextUserArr">
                      
                    </a-checkbox-group>
                </a-col>
              </a-row>
              <a-row style="width:100%">
                <a-col :span="8">请选择需求登记人员确认：</a-col>
                <a-col :spna="16">
                  <a-checkbox-group v-model="nextUserId2" :options="nextUserArr2">
                      
                    </a-checkbox-group>
                </a-col>
              </a-row>   
            </a-modal>
            <a-modal v-model="nextForm" title="请选择下一步操作的用户" @ok="subimtNext" >
            <a-row style="width:100%">
                <a-col >请选择下一步操作的用户：</a-col>
              </a-row>
              <a-row style="width:100%">
                <a-col >
                  <a-checkbox-group v-model="nextUserId" :options="nextUserArr">
                    </a-checkbox-group>
                </a-col>
              </a-row>
                        
            </a-modal>

            <a-modal v-model="nextForm2" title="请选择下一步操作的用户" @ok="subimtNext" >
              <a-row style="width:100%">
                <a-col :span="8">请选择自动化科操作的用户：</a-col>
                <a-col :spna="16">
                  <a-checkbox-group v-model="nextUserId" :options="nextUserArr">
                      
                    </a-checkbox-group>
                </a-col>
              </a-row>
              <a-row style="width:100%">
                <a-col :span="8">请选择自动化科业务操作的用户：</a-col>
                <a-col :spna="16">
                  <a-checkbox-group v-model="nextUserId2" :options="nextUserArr2">
                      
                    </a-checkbox-group>
                </a-col>
              </a-row>    
            </a-modal>
            <a-modal v-model="nextForm3" title="请选择下一步操作的用户" @ok="subimtNext" >
              <a-row v-if="rc1" style="width:100%">
                <a-col :span="8">请选择市场化科的领导：</a-col>
                <a-col :spna="16">
                  <a-checkbox-group v-model="nextUserId" :options="nextUserArr">
                      
                    </a-checkbox-group>
                </a-col>
              </a-row>
              <a-row v-if="rc2" style="width:100%">
                <a-col :span="8">请选择自动化科的领导：</a-col>
                <a-col :spna="16">
                  <a-checkbox-group v-model="nextUserId2" :options="nextUserArr2">
                      
                    </a-checkbox-group>
                </a-col>
              </a-row>    
              <a-row v-if="rc3" style="width:100%">
                <a-col :span="8">请选择中心的领导：</a-col>
                <a-col :spna="16">
                  <a-checkbox-group v-model="nextUserId3" :options="nextUserArr3">
                      
                    </a-checkbox-group>
                </a-col>
              </a-row>  
    
            </a-modal>
            <a-modal v-model="nextForm4" title="请选择下一步操作的用户" @ok="subimtNext4" >
              <a-row style="width:100%">
                <a-col :span="8">请选择下一步操作的用户：</a-col>
                <a-col :span="16">
                  <a-checkbox-group v-model="nextUserId" :options="nextUserArr4">
                      
                    </a-checkbox-group>
                </a-col>
              </a-row>
                        
            </a-modal>
         </a-col>
    </a-row>
    <a-row class="arowstyle">
      <a-col :span="24">
        <a-tabs v-model:activeKey="mykey" style="margin-top:-20px;">
          <a-tab-pane key="1" tab="需求工单">
            <a-row style="width:100%;" >
              <a-col :span="24">
                <StepsShow ref="StepShow" :stepArr="Steps" :number="current" :stepInfoIds="stepInfoId"  v-if="refreshint0" />
              </a-col>
            </a-row>
        
            <a-row style="width:100%;" >
              <a-col :span="24">
                <a-spin :spinning="loading" tip="加载中。。。" size="large" style="width:100%;">
                <FormShow ref="StepForm" v-if="reopenkkk" :doc2="form2" :doc3="form3" :doc4="form4" :doc5="form5" :doc6="form6" :doc7="form7"
                 :doc8="form8"  :doc9="form9" :doc10="form10" :doc11="form11" @rb2ndBtnCtl="rb2ndBtnCtl"  :doc="doc" @refreshParent="readStep"
                 @rb1stBtnCtl="rb1stBtnCtl" @rnchange2="rnchange2"  :showHandleResult="showHandleResult" :currentStep="steptemp2"
                @onChange1="onChange1" @onChange2="onChange2" @onChange3="onChange3"
                    bussType="sjdj"   
                  :disabled="false" />
                </a-spin>
              </a-col>
            </a-row>
            <a-row style="width:100%;" >
              <a-col :span="24">
                <a-collapse style="width:100%;margin:0%;" expandIconPosition="left"  v-model="mykk">
                  <template slot="expandIcon" slot-scope="props"  >
                  <div style="display:flex;" >
                    <a-icon v-if="props.isActive" type="up-circle" theme="twoTone" />
                    <a-icon v-if="!props.isActive" type="down-circle" theme="twoTone" />
                  </div>
                </template>
                  <a-collapse-panel :key="index"  v-for="(item,index) in Steps"  v-if="index==0"    :header="item.stepName"  :style="collapsestyle"> 
                    <FormShow :key="index" :keyindex="index"  :steps="Steps" 
                    :doc="doc"  v-if="kkk"  :currentStep="item.stepName"  
                      :doc2arr="form2arr" :doc3arr="form3arr" :doc4arr="form4arr" :doc5arr="form5arr" 
                      :doc6arr="form6arr" :doc7arr="form7arr" :doc8arr="form8arr" :doc9arr="form9arr"
                      :doc10arr="form10arr" :doc11arr="form11arr"
                      :disabled="true" />
                  </a-collapse-panel>

                </a-collapse>
                <a-collapse style="width:100%;margin:0%;"  >
                  <template slot="expandIcon" slot-scope="props"  expandIconPosition="left">
                  <div style="display:flex;" >
                    <a-icon v-if="props.isActive" type="up-circle" theme="twoTone" />
                    <a-icon v-if="!props.isActive" type="down-circle" theme="twoTone" />
                  </div>
                </template>
                  <a-collapse-panel :key="index"  v-for="(item,index) in Steps"  v-if="item.hideFormState!=1&&index!=0" :header="item.stepName"  :style="collapsestyle"> 
                    <FormShow :key="index" :keyindex="index"  :steps="Steps" 
                     :doc="doc"  v-if="kkk"  :currentStep="item.stepName"  
                      :doc2arr="form2arr" :doc3arr="form3arr" :doc4arr="form4arr" :doc5arr="form5arr" 
                      :doc6arr="form6arr" :doc7arr="form7arr" :doc8arr="form8arr" :doc9arr="form9arr"
                      :doc10arr="form10arr" :doc11arr="form11arr"
                      :disabled="true" />
                  </a-collapse-panel>
                  
                </a-collapse>
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane key="2" tab="需求流程">
            <a-row>
              <a-col :span="24">
                流程图
              </a-col>
            </a-row>
            <a-row style="height:489px;background:#fff;">
              <a-col :span="24">
                <!-- <flow-view v-if="flowshow" :flowId="flowId"
          :taskId="taskId"
                   ></flow-view> -->
                   <!-- <demand-flow-chart v-if="flowshow"></demand-flow-chart> -->
                   <div class="flowImg" v-if="flowshow"></div>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-divider type="vertical" />
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                流转日志
              </a-col>
            </a-row>
            <a-row style="margin-top:50px;">
              <a-col :span="24">
                <a-timeline>
                  <a-timeline-item v-for="item in flowLogs">{{ item.dept }} {{ item.user }} {{ item.opTime }} {{item.stepName}}  {{item.acton}} </a-timeline-item>
                </a-timeline>
              </a-col>
            </a-row>
          </a-tab-pane>
          <a-tab-pane key="3" tab="关联 工单">
            <a-tabs v-model="commonkey" type="line" @change="mykeychange">
              <a-tab-pane key="1" tab="事件单号 ">
                <a-input placeholder="请输入事件单号" v-model="commonId" style="width: 50%;margin-left: 10px;" />
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="searchEvent" type="primary"
                    style="left: 10px;background-color: #1890ff;border-color: #1890ff;">
                    本页查询
                  </a-button>
                </a-config-provider>
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="resetsearch" style="left: 20px;">
                    重置
                  </a-button>
                </a-config-provider>
                
                <a-table :columns="columns" :data-source="commonrelationList" style="margin-top: 10px;">
                  
                  <template slot="bpm" slot-scope="text,record,index" >
                    <a @click="handleBpm(record)"  >
                      {{text}}
                    </a>
                  </template>
                  <template slot="bpm2" slot-scope="text,record,index" >
                    <a @click="handleBpm2(record)"  >
                      {{text}}
                    </a>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="2" tab="故障单号 ">
                <a-input placeholder="请输入事件单号" v-model="commonId"  style="width: 50%;margin-left: 10px;" />
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="searcht" type="primary"
                    style="left: 10px;background-color: #1890ff;border-color: #1890ff;">
                    本页查询
                  </a-button>
                </a-config-provider>
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="resetsearch" style="left: 20px;">
                    重置
                  </a-button>
                </a-config-provider>
                
                <a-table :columns="columns" :data-source="commonrelationList" style="margin-top: 10px;">
                  
                  <template slot="bpm" slot-scope="text,record,index" >
                    <a @click="handleBpm(record)"  >
                      {{text}}
                    </a>
                  </template>
                  <template slot="bpm2" slot-scope="text,record,index" >
                    <a @click="handleBpm2(record)"  >
                      {{text}}
                    </a>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="3" tab="问题单号 ">
                <a-input placeholder="请输入事件单号" v-model="commonId"  style="width: 50%;margin-left: 10px;" />
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="searchP" type="primary"
                    style="left: 10px;background-color: #1890ff;border-color: #1890ff;">
                    本页查询
                  </a-button>
                </a-config-provider>
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="resetsearch" style="left: 20px;">
                    重置
                  </a-button>
                </a-config-provider>
            
                <a-table :columns="columns" :data-source="commonrelationList" style="margin-top: 10px;">
                
                  <template slot="bpm" slot-scope="text,record,index" >
                    <a @click="handleBpm(record)"  >
                      {{text}}
                    </a>
                  </template>
                  <template slot="bpm2" slot-scope="text,record,index" >
                    <a @click="handleBpm2(record)"  >
                      {{text}}
                    </a>
                  </template>
                </a-table>
              </a-tab-pane>
              <a-tab-pane key="6" tab="发布计划单号 ">
                <a-input placeholder=" " v-model="commonId"  style="width: 50%;margin-left: 10px;" />
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="searchlPan" type="primary"
                    style="left: 10px;background-color: #1890ff;border-color: #1890ff;">
                    本页查询
                  </a-button>
                </a-config-provider>
                <a-config-provider :auto-insert-space-in-button="false">
                  <a-button @click="resetsearch" style="left: 20px;">
                    重置
                  </a-button>
                </a-config-provider>
                
                <a-table :columns="columns" :data-source="commonrelationList" style="margin-top: 10px;">
                  
                  <template slot="bpm" slot-scope="text,record,index" >
                    <a @click="handleBpm(record)"  >
                      {{text}}
                    </a>
                  </template>
                  <template slot="bpm2" slot-scope="text,record,index" >
                    <a @click="handleBpm2(record)"  >
                      {{text}}
                    </a>
                  </template>
                </a-table>
              </a-tab-pane>
              

            </a-tabs>
          </a-tab-pane>
        </a-tabs>
      </a-col>
    </a-row>
  
  </div>
</template>
<script>

import StepsShow from "@/components/bpm/StepsShow.vue";
import FormShow from "@/components/bpm/Formxqdj2.vue";
import SubmitShow from "@/components/bpm/SubmitShow.vue";
import Options from "@/components/bpm/Options.vue";
 import flowView from '@/components/flowchart/flowView.vue'
import AdvanceTable from '@/components/pt/table/AdvanceTable';
import Glgd from '@/components/bpm/glgd.vue'
import DemandFlowChart from '@/components/bpm/flowChart/DemandFlowChart.vue';
import {
  startflowPost,
  saveformdataPost,
  uploadPost,
  getDocInfo,
  operationGotoNextStep,
  getStepChartGet,
  removeFLowByIds,
  getbpmlog,getLastFlowInfo,saveDemandTrack3,
  queryCommonrelationList,saveyys,queryyys,demandNextStep,returnTodo,
  savehandle,queryhandle,savefinish,queryfinish,saveTroubleCheck,queryTroubleCheck,
  saveTroubleHandle,queryTroubleHandle,saveDemandCheck2,queryDemandCheck,
  saveTroubleConfirm,queryTroubleConfirm,saveDemandTrack2,queryDemandTrack
} from '@/api/system/ywrygl/operation'
import notification from 'ant-design-vue/es/notification'
import { listUesDevice, listUesDeviceSelect, delUesDevice, exportUesDevice, getInitData, importTemplate } from '@/api/system/bpm/uesDevice'
import { listUesWxryqkb,getRoleUser } from '@/api/system/ywrygl/uesWxryqkb'
import { login, loginBySms, getInfo, logout } from '@/api/login'
import moment from 'moment';
const data = []
export default {
  name: 'sjdb',
  components: {
    Glgd,
    StepsShow,
    FormShow,
    SubmitShow,
    Options,
    AdvanceTable,
    flowView,
    DemandFlowChart
  },
  data() {
    return {
      loading: false,
      delBtn:false,
      mykk:[0],
      steptemp2:'需求登记',
      collapsestyle: 'background-color: ghostwhite;width:100%;margin:0%;',
      collapseKey:'2',
      dLabel:'需求登记',
      redoBtn:false,
      rewroteBtn:false,
      nextBtn:true,
      kfsclBtn:false,
      nextForm5:false,
      nextUserId5:null,
      nextUserArr5:[],
      nextUserMap5:new Map(),
      nextForm3:false,
      nextUserId3:null,
      nextUserArr3:[],
      nextUserMap3:new Map(),
      nextForm4:false,
      nextUserId4:null,
      nextUserArr4:[],
      nextUserMap4:new Map(),
      nextForm2:false,
      nextUserId2:null,
      nextUserArr2:[],
      nextUserMap2:new Map(),
      nextForm:false,
      nextUserId:null,
      nextUserArr:[],
      nextUserMap:new Map(),
      form3:null,
      form2:null,
      form4:null,
      form5:null,
      form6:null,
      form7:null,
      form8:null,
      form9:null,
      form10:null,
      form11:null,
      form3arr:[],
      form2arr:[],
      form4arr:[],
      form5arr:[],
      form6arr:[],
      form7arr:[],
      form8arr:[],
      form9arr:[],
      form10arr:[],
      form11arr:[],
      form12:null,
      form13:null,
      form14:null,
      form15:null,
      form16:null,
      reopenkkk:false,
      refreshint99:false,
      haveSaved:false,
      flowId:"f853265058405285888",
      flowshow:false,
      taskId:'',
      nextstr:'流转',
      userroles:[],
      step2obj:null,
      step3obj:null,
      step4obj:null,
      refId:null,
      currentStep:'1',
      nextPerformUser:[],
      opUserId:null,
      opUserName:null,
      opDeptId:null,
      opDeptName:null,
      yysjlshuser:[],
      sjkshuser:[],
      zdhkshuser:[],
      sjcluser:[],
      sjkUserList:[],
      zdhkUserList:[],
      yysjlUserList:[],
      sjclUserList:[],
      selAcc:true,
      yysshShow:false,
      zdhkshShow:false,
      sjkshShow:false,
      selNextForm:false,
      kkk:true,
      nextForm5:false,
      columns: [
      {
          show: true,
          title: '编号',
          dataIndex: 'fdBh',
          align: 'center',
          scopedSlots: { customRender: 'bpm2' }
        },
        {
          show: true,
          title: '标题',
          dataIndex: 'title',
          align: 'center',
          scopedSlots: { customRender: 'bpm2' }
        },
        {
          show: true,
          title: '类型',
          dataIndex: 'type',
          align: 'center',
          scopedSlots: { customRender: 'bpm' }
        },
        {
          show: true,
          title: '当前环节',
          dataIndex: 'stepName',
          align: 'center',
          scopedSlots: { customRender: 'bpm' }
        },
       
      ],
      showHandleResult:false,
      flowLogs: [],
      mykey: '1',
      dataSources: [],
      refreshint0: false,
      current: 0,
      data,
      form: this.$form.createForm(this),
      commonkey:'1',
      activeKey: ['1'],
      disable: false,
      stepInfoId: '',
      stepId: '',
      // title: '',
      Steps: [],
      todoDtoId: '',
      taskInfo: {},
      doc: {},
      subIndex: 0,
      visible: false,
      refreshint1:false,
      deviceName: '',
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        event_no: undefined,
        add_time: undefined,
        occur_time: undefined,
        application_user: undefined,
        application_unit: undefined,
        application_dept: undefined,
        event_source: undefined,
        application_user_phone: undefined,
        mobile_phone: undefined,
        event_type: undefined,
        event_object: undefined,
        revisit_way: undefined,
        event_effect: undefined,
        event_emergent: undefined,
        event_prior: undefined,
        opinion: undefined,
        register_user: undefined,
        register_unit: undefined,
        register_dept: undefined,

      },
      dataSource: [],
      selectedRowKeys: [],
      selectedRows: [],
      selectedRowKeys2: [],
      selectedRows2: [],
      commonrelationList:[],
      commonType:'event',
      commonId:null,
      rc1:false,
      rc2:false,
      rc3:false,
      fid:null,
      eventgdgl:false,
      troublegdgl:false,
      problemgdgl:false,
      demandgdgl:false,
    }
  },
  watch: {
    activeKey(key) {
    },
    
  },
  computed: {
    rowSelection() {
      return {
        selectedRowKeys: this.selectedRowKeys,
        onChange: this.onSelectChange,
      }
    },
    rowSelection2() {
      return {
        selectedRowKeys: this.selectedRowKeys2,
        onChange: this.onSelectChange2,
      }
    },
  },
  activated() {
    this.refreshint99=false
    if(sessionStorage.getItem(this.$route.path)==2){
      this.commonrelationList=[]
      this.mykey = '1'
      this.kfsclBtn = false
      this.refreshint99=false
      this.nextPerformUser=[]
      this.nextUserId=[]
      this.nextUserArr=[]
      this.nextUserId2=[]
      this.nextUserArr2=[]
      this.nextUserId3=[]
      this.nextUserArr3=[]
      this.nextUserId5=[]
      this.nextUserArr5=[]
      this.kkk=false
      this.form={}
      this.doc=null
      this.flowLogs = new Array()
      this.disable = false
      this.refreshint0 = false
      if (null != this.$route.query.xid && "" != this.$route.query.xid
        && null != this.$route.query.xpid && "" != this.$route.query.xpid
        && null != this.$route.query.fid && "" != this.$route.query.fid) {
        this.reopenkkk=false
        this.stepInfoId = this.$route.query.xpid
        this.refId = this.$route.query.xpid
        this.todoDtoId = this.$route.query.xid
        this.fid = this.$route.query.fid
        this.refreshint1=false
        this.readStep()
      } else {
        this.refreshint1=true
        // this.readyStep()
      }
      this.kkk=true
      
      sessionStorage.setItem(this.$route.path,1)
    
    }
  },
  created() {
    this.refreshint99=false
    this.haveSaved=false
    this.doc=null
    this.form={}
    this.kkk=false
    this.flowLogs = new Array()
    this.disable = false
    this.refreshint0 = false
    this.currentStep = '1'
    
   
      getInfo().then((res)=>{
       
       this.opUserId=res.user.id
       this.opUserName=res.user.name
       this.opDeptId=res.user.deptId
       this.opDeptName=res.user.deptName
       this.userroles = res.roles
       if (null != this.$route.query.xid && "" != this.$route.query.xid
        && null != this.$route.query.xpid && "" != this.$route.query.xpid
        && null != this.$route.query.fid && "" != this.$route.query.fid) {
        this.stepInfoId = this.$route.query.xpid
        this.todoDtoId = this.$route.query.xid
        this.fid = this.$route.query.fid
        this.refreshint1=false
        this.readStep()
      } else {
        this.refreshint1=true
   //     this.readyStep()
      }
     })
    //  this.kkk=true
    
    
    sessionStorage.setItem(this.$route.path,1)
    
    
  },
  methods: {
    deleteDoc(){
      removeFLowByIds(this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].id).then((res) => {
        this.$multiTab.closeCurrentPage()
      })
    },
    closeThisPage(){
      this.$multiTab.closeCurrentPage()
    },
    eventgdglSet(){
      this.eventgdgl=true
      this.$nextTick(() => (
        this.$refs.eventglgd.handleUpdate()
      ))
    },
    troublegdglSet(){
      this.troublegdgl=true
      this.$nextTick(() => (
        this.$refs.troublegdgl.handleUpdate()
      ))
    },
    problemgdglSet(){
      this.problemgdgl=true
      this.$nextTick(() => (
        this.$refs.problemgdgl.handleUpdate()
      ))
    },
    demandgdglSet(){
      this.demandgdgl=true
      this.$nextTick(() => (
        this.$refs.demandgdgl.handleUpdate()
      ))
    },
    gowhere(){
      if(this.rc1==true||this.rc2==true||this.rc3==true){
        this.kfsclBtn =false
        this.nextBtn=true
      }
      if(this.rc1==false&&this.rc2==false&&this.rc3==false){
        this.kfsclBtn =true
        this.nextBtn=false
      }

    },
    onChange1(e){
      if(e){
        this.rc1=true
      }else{
        this.rc1= false
      }
      this.gowhere()
    },
    onChange2(e){
      if(e){
        this.rc2 = true
      }else{
        
        this.rc2 = false
      }
      this.gowhere()
    },
    onChange3(e){
      if(e){
        this.rc3 = true
      }else{
        this.rc3 = false
      }
      this.gowhere()
    },
    rb2ndBtnCtl(e){      
      if(e==1){
        this.redoBtn=false
        this.nextBtn=true
      }else{
        this.redoBtn=true
        this.nextBtn=false
      }
    },
    rnchange2(e){
      if(e==1){
        this.rewroteBtn=false
        this.nextBtn=true
      }else{
        this.rewroteBtn=true
        this.nextBtn=false
      }
    },
    rb1stBtnCtl(e){
      if(e==1){
        this.rewroteBtn=false
        this.kfsclBtn=true
      }else{
        this.rewroteBtn=true
        this.kfsclBtn=false
      }
    },
    submitStep6(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      for(var i=0;i<that.nextUserId.length;i++){
        that.nextUserArr.forEach(item=>{         
          if(item.value == that.nextUserId[i]){
            let eu = {
              userId:item.value,
              userName:item.label,
              agentsDeptId:'s881175867114389504',
              agentsDeptName:'自动化科业务人员确认',
            }
            that.nextPerformUser.push(eu)
          }
        })
        
      }
      for(var i=0;i<that.nextUserId2.length;i++){
        that.nextUserArr2.forEach(item=>{         
          if(item.value == that.nextUserId2[i]){
            let eu = {
              userId:item.value,
              userName:item.label,
              agentsDeptId:'s853269617466933248',
              agentsDeptName:'需求登记人员确认',
            }
            that.nextPerformUser.push(eu)
          }
        })
        
      }
        nextParam.nextPerformUser = that.nextPerformUser
        nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType='common'
      
        // nextParam.sendUserId='456'
        // nextParam.sendUserDeptId="Dsaaas"
        nextParam.sendUserName=_this.opUserName
        nextParam.sendUserId=_this.opUserId
        nextParam.sendUserDeptId=_this.opDeptId
        
        nextParam.opinion='_this.myoption'
        nextParam.stepId=this.doc.STEP_INFO.id
        console.error(nextParam)
        demandNextStep(nextParam).then(()=>{
          _this.lingdangjianshao()
          _this.$message.success( '提交成功')
          _this.selNextForm=false
          _this.$multiTab.closeCurrentPage()
        })
    },
    submitStep10(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      // for(var i=0;i<that.nextUserId.length;i++){
      //     that.nextUserArr4.forEach(item=>{         
      //       if(item.value == that.nextUserId[i]){
      //         let eu = {
      //           userId:item.value,
      //           userName:item.label,
      //           agentsDeptId:sid,
      //           agentsDeptName:name,
      //         }
      //         that.nextPerformUser.push(eu)
      //       }
      //     })
          
      //   }
      //   nextParam.nextPerformUser = that.nextPerformUser
        nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType='common'
      
        // nextParam.sendUserId='456'
        // nextParam.sendUserDeptId="Dsaaas"
        nextParam.sendUserName=_this.opUserName
        nextParam.sendUserId=_this.opUserId
        nextParam.sendUserDeptId=_this.opDeptId
        
        nextParam.opinion='_this.myoption'
        nextParam.stepId=this.doc.STEP_INFO.id
        console.error(nextParam)
        demandNextStep(nextParam).then(()=>{
          _this.lingdangjianshao()
          _this.$message.success( '提交成功')
          _this.selNextForm=false
          _this.$multiTab.closeCurrentPage()
        })
    },
    submitStep4(sid,name,path){
    
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      for(var i=0;i<that.nextUserId.length;i++){
          that.nextUserArr4.forEach(item=>{         
            if(item.value == that.nextUserId[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:sid,
                agentsDeptName:name,
              }
              that.nextPerformUser.push(eu)
            }
          })
          
        }
        nextParam.nextPerformUser = that.nextPerformUser
        nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType='common'
      
        // nextParam.sendUserId='456'
        // nextParam.sendUserDeptId="Dsaaas"
        nextParam.sendUserName=_this.opUserName
        nextParam.sendUserId=_this.opUserId
        nextParam.sendUserDeptId=_this.opDeptId
        
        nextParam.opinion='_this.myoption'
        nextParam.stepId=this.doc.STEP_INFO.id
        console.error(nextParam)
        demandNextStep(nextParam).then(()=>{
          _this.lingdangjianshao()
          _this.$message.success( '提交成功')
          _this.selNextForm=false
          _this.$multiTab.closeCurrentPage()
        })
      
    },
    submitStep5(sid,name,path){
      let _this = this
      let that = this
      let nextParam = _this.doc.TASK_INFO
      that.nextPerformUser=[]
      that.nextUserArr.forEach(item=>{
        let eu = {
        userId:item.value,
        userName:item.label,
        agentsDeptId:'s853266885314084864',
        agentsDeptName:'系统开发商审核',
      }
      that.nextPerformUser.push(eu)
      })
      
      nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType='common'
      
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      
      nextParam.opinion='_this.myoption'
      nextParam.stepId=this.doc.STEP_INFO.id
      console.error(nextParam)
      demandNextStep(nextParam).then(()=>{
        _this.lingdangjianshao()
        _this.$message.success( '提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    submitStep1(sid,name,path){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      let _this = this
      let nextParam = _this.doc.TASK_INFO
      // nextParam.nextPerformUser = _this.nextPerformUser
      that.nextPerformUser=[]
     
      if(this.currentStep=='1'){
        for(var i=0;i<that.nextUserId.length;i++){
          that.nextUserArr.forEach(item=>{         
            if(item.value == that.nextUserId[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:'s853266417233952768',
                agentsDeptName:'业务科室领导审核',
              }
              that.nextPerformUser.push(eu)
            }
          })
          
        }
        nextParam.nextPerformUser = that.nextPerformUser
      } else if(this.currentStep=='2'){
        for(var i=0;i<that.nextUserId.length;i++){
          that.nextUserArr.forEach(item=>{         
            if(item.value == that.nextUserId[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:'s853266885314084864',
                agentsDeptName:'系统开发商审核',
              }
              that.nextPerformUser.push(eu)
            }
          })
        }
        
        nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='3'){
        for(var i=0;i<that.nextUserId.length;i++){
          that.nextUserArr.forEach(item=>{         
            if(item.value == that.nextUserId[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:'s853268090425704448',
                agentsDeptName:'自动化科审核',
              }
              that.nextPerformUser.push(eu)
            }
          })
        }

        for(var i=0;i<that.nextUserId2.length;i++){
          that.nextUserArr2.forEach(item=>{         
            if(item.value == that.nextUserId2[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:'s881175442516606976',
                agentsDeptName:'自动化科业务人员审核',
              }
              that.nextPerformUser.push(eu)
            }
          })
        }
        nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='4'){
        // for(var i=0;i<that.nextUserId.length;i++){
        //   that.nextUserArr.forEach(item=>{         
        //     if(item.value == that.nextUserId[i]){
        //       let eu = {
        //         userId:item.value,
        //         userName:item.label,
        //         agentsDeptId:'s853266885314084864',
        //         agentsDeptName:'系统开发商审核',
        //       }
        //       that.nextPerformUser.push(eu)
        //     }
        //   })
        // }
        
        // nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='5'){
       if(null!=that.nextUserId && that.nextUserId.length>0){
        for(var i=0;i<that.nextUserId.length;i++){
          that.nextUserArr.forEach(item=>{         
            if(item.value == that.nextUserId[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:'s881176290105753600',
                agentsDeptName:'市场部领导审核',
              }
              that.nextPerformUser.push(eu)
            }
          })
        }
       }
       if(null!=that.nextUserId2 && that.nextUserId2.length>0){
        for(var i=0;i<that.nextUserId2.length;i++){
          that.nextUserArr2.forEach(item=>{         
            if(item.value == that.nextUserId2[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:'s881176525578174464',
                agentsDeptName:'科室领导审核',
              }
              that.nextPerformUser.push(eu)
            }
          })
        }
       }

       if(null!=that.nextUserId3 && that.nextUserId3.length>0){
        for(var i=0;i<that.nextUserId3.length;i++){
          that.nextUserArr3.forEach(item=>{         
            if(item.value == that.nextUserId3[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:'s881176670910808064',
                agentsDeptName:'中心领导审核',
              }
              that.nextPerformUser.push(eu)
            }
          })
        }
       } 

        
        // for(var i=0;i<that.nextUserId4.length;i++){
        //   that.nextUserArr4.forEach(item=>{         
        //     if(item.value == that.nextUserId4[i]){
        //       let eu = {
        //         userId:item.value,
        //         userName:item.label,
        //         agentsDeptId:'s881176670910808064',
        //         agentsDeptName:'中心领导审核',
        //       }
        //       that.nextPerformUser.push(eu)
        //     }
        //   })
        // }
        nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='6'){
        // for(var i=0;i<that.nextUserId.length;i++){
        //   that.nextUserArr.forEach(item=>{         
        //     if(item.value == that.nextUserId[i]){
        //       let eu = {
        //         userId:item.value,
        //         userName:item.label,
        //         agentsDeptId:'s853266885314084864',
        //         agentsDeptName:'系统开发商审核',
        //       }
        //       that.nextPerformUser.push(eu)
        //     }
        //   })
        // }
        
        // nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='7'){
        // for(var i=0;i<that.nextUserId.length;i++){
        //   that.nextUserArr.forEach(item=>{         
        //     if(item.value == that.nextUserId[i]){
        //       let eu = {
        //         userId:item.value,
        //         userName:item.label,
        //         agentsDeptId:'s853266885314084864',
        //         agentsDeptName:'系统开发商审核',
        //       }
        //       that.nextPerformUser.push(eu)
        //     }
        //   })
        // }
        
        // nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='8'){
        // for(var i=0;i<that.nextUserId.length;i++){
        //   that.nextUserArr.forEach(item=>{         
        //     if(item.value == that.nextUserId[i]){
        //       let eu = {
        //         userId:item.value,
        //         userName:item.label,
        //         agentsDeptId:'s853266885314084864',
        //         agentsDeptName:'系统开发商审核',
        //       }
        //       that.nextPerformUser.push(eu)
        //     }
        //   })
        // }
        
        // nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='9'){
        for(var i=0;i<that.nextUserId.length;i++){
          that.nextUserArr.forEach(item=>{         
            if(item.value == that.nextUserId[i]){
              let eu = {
                userId:item.value,
                userName:item.label,
                agentsDeptId:'s881175867114389504',
                agentsDeptName:'自动化科业务人员确认',
              }
              that.nextPerformUser.push(eu)
            }
          })
        }
        
        nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='10'){
        // for(var i=0;i<that.nextUserId.length;i++){
        //   that.nextUserArr.forEach(item=>{         
        //     if(item.value == that.nextUserId[i]){
        //       let eu = {
        //         userId:item.value,
        //         userName:item.label,
        //         agentsDeptId:'s853266885314084864',
        //         agentsDeptName:'系统开发商审核',
        //       }
        //       that.nextPerformUser.push(eu)
        //     }
        //   })
        // }
        
        // nextParam.nextPerformUser = that.nextPerformUser
      }else if(this.currentStep=='11'){
        // for(var i=0;i<that.nextUserId.length;i++){
        //   that.nextUserArr.forEach(item=>{         
        //     if(item.value == that.nextUserId[i]){
        //       let eu = {
        //         userId:item.value,
        //         userName:item.label,
        //         agentsDeptId:'s853266885314084864',
        //         agentsDeptName:'系统开发商审核',
        //       }
        //       that.nextPerformUser.push(eu)
        //     }
        //   })
        // }
        
        // nextParam.nextPerformUser = that.nextPerformUser
      }
      
        nextParam.nextStepid = sid
        nextParam.nextStepName = name
        nextParam.pathType='common'
      
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      
      nextParam.opinion='_this.myoption'
      nextParam.stepId=this.doc.STEP_INFO.id
      console.error(nextParam)
      demandNextStep(nextParam).then(()=>{
        _this.lingdangjianshao()
        _this.$message.success( '提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    async subimtNext6(){
      if(null==this.nextUserId||this.nextUserId.length==0){
        this.nextForm5 = false
        this.$message.error( '请选择自动化科业务人员')
        return 
      }
      if(null==this.nextUserId2||this.nextUserId2.length==0){
        this.nextForm5 = false
        this.$message.error( '请选择需求登记人员')
        return 
      }
      await this.saveTrackForm('2',false)
      this.submitStep6('s881175867114389504','s853269617466933248','common')
      this.nextForm5 = false
    },
    async subimtNext4(){
      this.submitStep4('s853269210690748416','系统开发商处理','common')
      this.nextForm4 = false
    },
    async subimtNext(){
      this.nextForm=false
      this.nextForm2=false
      this.nextForm3=false
      if(this.currentStep=='1'){
        this.submitStep1('s853266417233952768','业务科室领导审核','common')
      }else if(this.currentStep=='2'){
        this.submitStep1('s853266885314084864','系统开发商审核','common')
      }else if(this.currentStep=='3'){
        if(null==this.nextUserId||this.nextUserId.length==0){
          this.$notification.error({
             message: '请选择自动化科处理人',
              description: ``
          })
          return false;
        }
        if(null==this.nextUserId2||this.nextUserId2.length==0){
          this.$notification.error({
             message: '请选择自动化科业务操作人',
              description: ``
          })
          return false;
        }
        let str = ''
        for(var i=0;i<this.nextUserId.length;i++){
          if(str==''){
            str = this.nextUserId[i]
          }else{
            str = str + ',' + this.nextUserId[i]
          }
          
        }
        let str2 = ''
        for(var i=0;i<this.nextUserId2.length;i++){
          if(str2==''){
            str2 = this.nextUserId2[i]
          }else{
            str2 = str2 + ',' + this.nextUserId2[i]
          }
          
        }
        let resxult =  await this.saveFormInSubmit(str,str2,null,null,null)
        if(resxult){
          this.submitStep1('s853268090425704448','自动化科审核','gatewaystart')
        }
      }else if(this.currentStep=='4'){
        
          this.submitStep1('s853269210690748416','系统开发商处理','gatewaystart')
        
      }else if(this.currentStep=='5'){
        let str = ''
        if(null!=this.nextUserId && this.nextUserId.length>0){
          for(var i=0;i<this.nextUserId.length;i++){
            if(str==''){
              str = this.nextUserId[i]
            }else{
              str = str + ',' + this.nextUserId[i]
            }
            
          }
        }
        
        let str2 = ''
        if(null!=this.nextUserId2 && this.nextUserId2.length>0){
          for(var i=0;i<this.nextUserId2.length;i++){
            if(str2==''){
              str2 = this.nextUserId2[i]
            }else{
              str2 = str2 + ',' + this.nextUserId2[i]
            }
            
          }
        }
        
        let str3 = ''
        if(null!=this.nextUserId3 && this.nextUserId3.length>0){
          for(var i=0;i<this.nextUserId3.length;i++){
            if(str3==''){
              str3 = this.nextUserId3[i]
            }else{
              str3 = str3 + ',' + this.nextUserId3[i]
            }
            
          }
        }
        
        let resxult =  await this.saveFormInSubmit(null,null,str,str2,str3)
        if(resxult){
          this.submitStep1('s853269210690748416','系统开发商处理*','common')
        }
      }else if(this.currentStep=='6'){
        this.submitStep1('s853269210690748416','系统开发商处理','common')
      }else if(this.currentStep=='7'){
        this.submitStep1('s851120271619260416','系统开发商处理','common')
      }else if(this.currentStep=='8'){
        this.submitStep1('s853269210690748416','系统开发商处理','common')
      }else if(this.currentStep=='9'){
        this.submitStep1('s881175867114389504','自动化科业务人员确认','common')
      }else if(this.currentStep=='10'){
        this.submitStep1('s853269617466933248','需求登记人员确认','common')
      }else if(this.currentStep=='11'){
        this.submitStep1('s851120271619260416','已关闭','end')
      }else if(this.currentStep=='12'){
        this.submitStep1('s851120271619260416','已关闭','end')
      }
    },
    async showNext2(){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('5','2',false)
          if(resxult2){
            that.nextForm4=true
            // this.submitStep1('s851119837257138176','自动化科业务人员确认','common')
          }
        }
    },
    async showNext(){
      
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      if(this.currentStep=='1'){
         let resxult = await this.saveStepForm()
         if(resxult){
          // this.submitStep1('s853266417233952768','业务科室领导审核','common')
          that.nextForm=true
         }
      }else if(this.currentStep=='2'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('2','2',false)
          if(resxult2){
            that.nextForm=true
            // this.submitStep1('s853266885314084864','系统开发商审核','common')
          }
        }
      }else if(this.currentStep=='3'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult2 = await this.saveCheckForm('3','2',false)  
        let vform3 = this.$refs.StepForm.getFrom3()
        if(resxult2){
          that.nextForm2=true  
          
          
        }
      }else if(this.currentStep=='4'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('4','2',false)
          if(resxult2){
         
            this.submitStep1('s853269210690748416','系统开发商处理','common')
          }
        }
      }else if(this.currentStep=='5'){
       
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('5','2',false)
          if(resxult2){
            that.nextForm3=true
            // this.submitStep1('s851119837257138176','自动化科业务人员确认','common')
          }
        }
      }else if(this.currentStep=='6'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('6','2',false)
          if(resxult2){
            // that.nextForm5=true
            this.submitStep5('s853269210690748416','系统开发商处理','common')
          }
        }
      }else if(this.currentStep=='7'){
        setTimeout(function(){
          that.refreshint99=true
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('7','2',false)
          if(resxult2){
            // that.nextForm=true
            this.submitStep5('s853269210690748416','系统开发商处理','common')
          }
        }
      }else if(this.currentStep=='8'){
        setTimeout(function(){
          that.refreshint99=true
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('8','2',false)
          if(resxult2){
            // that.nextForm=true
            this.submitStep5('s853269210690748416','系统开发商处理','common')
          }
        }
      }else if(this.currentStep=='9'){
        setTimeout(function(){
          that.refreshint99=true
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
    
            that.nextForm5=true
            // this.submitStep1('s851120271619260416','已关闭','common')
          
        }
      }else if(this.currentStep=='10'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('10','2',false)
          if(resxult2){
            // that.nextForm=false
            this.submitStep10('s853269700384129024','已关闭','end')
          }
        }
      }else if(this.currentStep=='11'){
        setTimeout(function(){
          that.refreshint99=false
        },3000)
        let resxult =  await this.saveFormInSubmit(null,null,null,null,null)
        if(resxult){
          let resxult2 = await this.saveCheckForm('11','2',false)
          if(resxult2){
            // that.nextForm=false
            this.submitStep10('s853269700384129024','已关闭','end')
          }
        }
      }
      
    },
    save4back(){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      if(this.currentStep=='1'){
        return this.saveStepForm()
      }else if(this.currentStep=='2'){       
        return this.saveCheckForm('2','2',false)
      }else if(this.currentStep=='3'){       
        let x = this.saveCheckForm('3','2',false)
        if(x==true){
          let vform3 = this.$refs.StepForm.getFrom3()  
          x = this.saveFormInSubmit(null,null,null,null,null)
        }
        return x
      }else if(this.currentStep=='4'){  
        let y = this.saveCheckForm('4','2',false)
        if(y==true){
          let vf4= this.$refs.StepForm.getFrom4()
          y = this.saveFormInSubmit(null,null,null,null,null)
        }
        return y
      }else if(this.currentStep=='5'){  
        return this.saveCheckForm('5','2',false)
        
      }else if(this.currentStep=='6'){  
        return this.saveCheckForm('6','2',false)
      }else if(this.currentStep=='7'){  
        return this.saveCheckForm('7','2',false)
      }else if(this.currentStep=='8'){  
        return this.saveCheckForm('8','2',false)
      }else if(this.currentStep=='9'){  
        return this.saveTrackForm('2',false)
      }else if(this.currentStep=='10'){  
        return this.saveCheckForm('10','2',false)
      }else if(this.currentStep=='11'){  
        return this.saveCheckForm('11','2',false)
      }
      
      return false

  
      
    },
    newSaveForm(){
      let that = this
      this.refreshint99=true
      setTimeout(function(){
        that.refreshint99=false
      },3000)
      if(this.currentStep=='1'){
        return this.saveStepForm()
      }else if(this.currentStep=='2'){       
        return this.saveCheckForm('2','1',true)
      }else if(this.currentStep=='3'){       
        this.saveCheckForm('3','1',true)
        let vform3 = this.$refs.StepForm.getFrom3()  
        return this.saveFormInSubmit(null,null,null,null,null)
      }else if(this.currentStep=='4'){  
        this.saveCheckForm('4','1',true)
        let vf4= this.$refs.StepForm.getFrom4()
        return this.saveFormInSubmit(null,null,null,null,null)
      }else if(this.currentStep=='5'){  
        return this.saveCheckForm('5','1',true)
      }else if(this.currentStep=='6'){  
        return this.saveCheckForm('6','1',true)
      }else if(this.currentStep=='7'){  
        return this.saveCheckForm('7','1',true)
      }else if(this.currentStep=='8'){  
        return this.saveCheckForm('8','1',true)
      }else if(this.currentStep=='9'){  
        return this.saveTrackForm('1',true)
      }else if(this.currentStep=='10'){  
        return this.saveCheckForm('10','1',true)
      }else if(this.currentStep=='11'){  
        return this.saveCheckForm('11','1',true)
      }
      
     

  
      
    },
    async saveTrackForm(status,flag){
      let that = this
      let vform =this.$refs.StepForm.getFrom9()
      if (undefined != vform && null != vform && "" != vform) {
        vform.demandId = that.doc.TASK_INFO.id
        vform.isRelease = status
        return await saveDemandTrack2(vform).then(res=>{
          if(flag==true){
            that.$message.success( '保存成功')
          }
          
        // callback(false)
          that.refreshint99=false 
          return true
        })
      }else{
        that.$notification.error({
          message: '保存失败',
          description: ``
        })
        // callback(false)
        that.refreshint99=false 
        return false
      }
    },
    async saveConfirmForm(type){
      let that = this
      let vform = null
      if(type=='5'){
        vform = this.$refs.StepForm.getFrom5()
      }else if(type=='6'){
        vform = this.$refs.StepForm.getFrom6()
      }else if(type=='7'){
        vform = this.$refs.StepForm.getFrom7()
      }
      
      if (undefined != vform && null != vform && "" != vform) {
        vform.mainId = that.doc.TASK_INFO.id
        vform.flowNodeInstanceIds=type
        return await saveTroubleConfirm(vform).then(res=>{
          that.$message.success( '保存成功')
        // callback(false)
          that.refreshint99=false 
          return true
        })
      }else{
        that.$notification.error({
          message: '保存失败',
          description: ``
        })
        // callback(false)
        that.refreshint99=false 
        return false
      }
    },
    async saveHandleForm(){
      let that = this
      let vform = null
      vform = this.$refs.StepForm.getFrom4()
      if (undefined != vform && null != vform && "" != vform) {
        vform.mainId = that.doc.TASK_INFO.id
        vform.flowNodeInstanceIds=that.doc.STEP_INFO.id
        return await saveTroubleHandle(vform).then(res=>{
          that.$message.success( '保存成功')
        // callback(false)
          that.refreshint99=false 
          return true
        })
      }else{
        that.$notification.error({
          message: '保存失败',
          description: ``
        })
        // callback(false)
        that.refreshint99=false 
        return false
      }
    },
   async saveCheckForm(type,status,flag){
    let that = this
    let vform = null
    if(type=='2'){
      vform = this.$refs.StepForm.getFrom2()
    }else if(type=='3'){
      vform = this.$refs.StepForm.getFrom3()
    }else if(type=='4'){
      vform = this.$refs.StepForm.getFrom4()
    }else if(type=='5'){
      vform = this.$refs.StepForm.getFrom5()
    }else if(type=='6'){
      vform = this.$refs.StepForm.getFrom6()
    }else if(type=='7'){
      vform = this.$refs.StepForm.getFrom7()
    }else if(type=='8'){
      vform = this.$refs.StepForm.getFrom8()
    }else if(type=='10'){
      vform = this.$refs.StepForm.getFrom10()
    }else if(type=='11'){
      vform = this.$refs.StepForm.getFrom11()
    }
    if (undefined != vform && null != vform && "" != vform) {
      vform.registerId = that.doc.TASK_INFO.id
      vform.releases=type
      vform.taskId=that.doc.STEP_INFO.id
      vform.taskName=that.doc.STEP_INFO.stepName
      vform.project = status
      return await saveDemandCheck2(vform).then(res=>{
        if(flag==true){
          that.$message.success( '保存成功')
        }
        
      // callback(false)
        that.refreshint99=false 
        return true
        })
    }else{
      that.$notification.error({
        message: '保存失败',
        description: ``
      })
      // callback(false)
      that.refreshint99=false 
      return false
    }

   }, 
   async saveFormInSubmit(zdhkshuser,zdhkywryshuser,ksldshuser,scbldshuser,zxldshuser){
    let param = {
      MAIN_TABLE: {

      },
      SUB_TABLE: {
      },
      PENDING_TABLE: {}
    }
    param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)] = this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)]
    param.PENDING_TABLE={
      todoDto:{
        id: this.doc.TASK_INFO.todoDto.id,
        isDelete:-1
      }
    }
    if(null!=zdhkshuser&&""!=zdhkshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].zdhkshuser = zdhkshuser
    }
    if(null!=zdhkshuser&&""!=zdhkywryshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].zdhkywryshuser = zdhkywryshuser
    }
    if(null!=ksldshuser&&""!=ksldshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].ksldshuser = ksldshuser
    }
    if(null!=scbldshuser&&""!=scbldshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].scbldshuser = scbldshuser
    }
    if(null!=zxldshuser&&""!=zxldshuser){
      param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].zxldshuser = zxldshuser
    }
    delete param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].timeout_time
    delete param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].gmt_create

   
    return await saveformdataPost(param).then((res) => {
      this.$message.success( '保存成功')
    
      this.refreshint99=false 
      return true
    })
    
   },
   async saveStepForm() {
      if(this.currentStep=='1'){
        let vform = this.$refs.StepForm.getFrom()
        if (undefined != vform && null != vform && "" != vform) {
          let param = {
            MAIN_TABLE: {

            },
            SUB_TABLE: {
            },
            PENDING_TABLE: {}
          }
          let parr = new Array()
          this.makeDocFormDataMainTable(vform)
          param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)] = this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)]
          param.PENDING_TABLE={
            todoDto:{
              id: this.doc.TASK_INFO.todoDto.id,
              isDelete:-1
            }
          }

         return await saveformdataPost(param).then((res) => {
            this.$message.success( '保存成功')
          
            this.refreshint99=false 
            return true
          })
          
          
        }else{
          this.$notification.error({
              message: '保存失败',
              description: ``
            })
            // callback(false)
            this.refreshint99=false 
          return false
        }
      }
      
      
      
    },
    async rollBack2(){
      let _this = this
      let x = await _this.save4back()
      if(x==false){
        return 
      }
      let nextParam = _this.doc.TASK_INFO
      // nextParam.nextPerformUser = _this.nextPerformUser
      
        nextParam.nextStepid = 's853269210690748416'
        nextParam.nextStepName = '系统开发商处理'
         nextParam.pathType='back'
      
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
      nextParam.sendUserName=_this.opUserName
      nextParam.sendUserId=_this.opUserId
      nextParam.sendUserDeptId=_this.opDeptId
      
      nextParam.opinion='_this.myoption'
      nextParam.stepId=this.doc.STEP_INFO.id
      returnTodo(nextParam).then(()=>{
        _this.lingdangjianshao()
        _this.$message.success( '提交成功')
        _this.selNextForm=false
        _this.$multiTab.closeCurrentPage()
      })
    },
    async rollBack(){
      let _this = this
      let x = await  _this.save4back()
      if(x==true){
        let nextParam = _this.doc.TASK_INFO
      // nextParam.nextPerformUser = _this.nextPerformUser
      
        nextParam.nextStepid = 's853265198016888832'
        nextParam.nextStepName = '需求登记'
         nextParam.pathType='back'
      
      // nextParam.sendUserId='456'
      // nextParam.sendUserDeptId="Dsaaas"
        nextParam.sendUserName=_this.opUserName
        nextParam.sendUserId=_this.opUserId
        nextParam.sendUserDeptId=_this.opDeptId
        
        nextParam.opinion='_this.myoption'
        nextParam.stepId=this.doc.STEP_INFO.id
        returnTodo(nextParam).then(()=>{
          _this.lingdangjianshao()
          _this.$message.success( '提交成功')
          _this.selNextForm=false
          _this.$multiTab.closeCurrentPage()
        })
      }
      
    },
   
    handleBpm(record){
      if(record.orderIdSuf!=this.doc.TASK_INFO.id){
        if(record.orderTypeSuf=='problem'){
          this.$router.push({ name: 'Wtdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypeSuf=='trouble'){
          this.$router.push({ name: 'Gzdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypeSuf=='event'){
          this.$router.push({ name: 'Sjdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypeSuf=='demand'){
          this.$router.push({ name: 'Xqdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }
      }
      
    },
    handleBpm2(record){
      if(record.orderIdPre!=this.doc.TASK_INFO.id){
        if(record.orderTypePre=='problem'){
          this.$router.push({ name: 'Wtdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypePre=='trouble'){
          this.$router.push({ name: 'Gzdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypePre=='event'){
          this.$router.push({ name: 'Sjdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }else if(record.orderTypePre=='demand'){
          this.$router.push({ name: 'Xqdj',params:{xid:record.orderIdPre,xpid:record.orderIdSuf} })
        }
      }
    },
    resetsearch(){
      this.commonkey='1'
      this.commonType='event'
      this.commonId=null
      this.commonrelationList=[]
    },
    mykeychange(e){
      if(e==1){
        this.commonType='event'
      }else if(e==2){
        this.commonType='trouble'
      }else if(e==3){
        this.commonType='problem'
      }else if(e==4){
        this.commonType='demand'
      }
      this.search()
    },
    searchlPan(){
      this.commonrelationList=[]
      let queryP={
        orderIdPre:this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh,
        orderTypeSuf:'release',
      }
      queryCommonrelationList(queryP).then((res2)=>{
        this.commonrelationList=res2.rows
        
      })
      let queryP2={
        orderTypePre:'release',
        orderIdSuf:this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh
      }
      queryCommonrelationList(queryP2).then((res2)=>{
        res2.rows.forEach(item=>{
          this.commonrelationList.push(item)
        })
        
      })
    },
    searchP(){
      this.commonrelationList=[]
      let queryP={
        orderIdPre:this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh,
        orderTypeSuf:'problem',
      }
      queryCommonrelationList(queryP).then((res2)=>{
        this.commonrelationList=res2.rows
        
      })
      let queryP2={
        orderTypePre:'problem',
        orderIdSuf:this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh
      }
      queryCommonrelationList(queryP2).then((res2)=>{
        res2.rows.forEach(item=>{
          this.commonrelationList.push(item)
        })
        
      })
    },
    searcht(){
      this.commonrelationList=[]
      let queryP={
        orderIdPre:this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh,
        orderTypeSuf:'trouble',
      }
      queryCommonrelationList(queryP).then((res2)=>{
        this.commonrelationList=res2.rows
        
      })
      let queryP2={
        orderTypePre:'trouble',
        orderIdSuf:this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh
      }
      queryCommonrelationList(queryP2).then((res2)=>{
        res2.rows.forEach(item=>{
          this.commonrelationList.push(item)
        })
        
      })
    },
    searchEvent(){
      this.commonrelationList=[]
      let queryP={
        orderIdPre:this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh,
        orderTypeSuf:'event',
      }
      queryCommonrelationList(queryP).then((res2)=>{
        this.commonrelationList=res2.rows
        
      })
      let queryP2={
        orderTypePre:'event',
        orderIdSuf:this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh
      }
      queryCommonrelationList(queryP2).then((res2)=>{
        res2.rows.forEach(item=>{
          this.commonrelationList.push(item)
        })
        
      })
    },
    search(){
      this.commonrelationList=[]
      // let queryP={
      //   orderTypePre:this.commonType,
      //   orderIdPre:this.commonId,
      //   orderTypeSuf:'event',
      //   orderIdSuf:this.doc.TASK_INFO.id
      // }
      // queryCommonrelationList(queryP).then((res2)=>{
      //   this.commonrelationList=res2.rows
        
      // })
      // let queryP2={
      //   orderTypePre:'event',
      //   orderIdPre:this.doc.TASK_INFO.id,
      //   orderTypeSuf:this.commonType,
      //   orderIdSuf:this.commonId
      // }
      // commonrelationList(queryP2).then((res2)=>{
      //   res2.rows.forEach(item=>{
      //     this.commonrelationList.push(item)
      //   })
        
      // })
    },
    setInsertOrder() {
      this.$refs.SubmitShow.setInsertOrder()
    },
    setExmaine() {
      this.$refs.SubmitShow.setExmaine()
    },
    setdisposal() {
      this.$refs.SubmitShow.setdisposal()
      
    },
    getBpmLogFun() {
      getbpmlog(this.taskInfo.todoDto.parentDocId).then((res) => {
        this.flowLogs = []
        res.data.forEach(element => {
          this.flowLogs.push(element)
        });        
      })
    },
    delSel() {
      var arr = new Array()

      for (var j = 0; j < this.dataSource.length; j++) {
        if (this.selectedRowKeys2.indexOf(j) == -1) {
          arr.push(this.dataSource[j])
        }
      }
      this.dataSource = arr
    },
    handleInputChange(row) {
      // 当下拉框的值改变时，这个方法会被调用
      // row 参数是当前行的数据
      // 在这里，您可以根据需要更新其他属性或发送请求到后端
    },
    handleDelete(record) {

      if (record) {
        const index = this.dataSource.findIndex((item) => item.id == record.id)
        if (index !== -1) {
          this.dataSource.splice(index, 1)
        } else {
          console.error('未找到要删除的数据项')
        }
      } else {
        console.error('尝试删除的行数据未定义')
      }
    },
    onSelectChange(selectedRowKeys) {
      this.selectedRowKeys = selectedRowKeys
      console.log('selectedRowKeys changed: ', selectedRowKeys)
    },
    onSelectChange2(selectedRowKeys) {
      this.selectedRowKeys2 = selectedRowKeys
      console.log('selectedRowKeys changed: ', selectedRowKeys)
    },


    showModal() {
      this.visible = true
      this.listUesWxryqkbFun()
    },
    searchPerson() {
      this.listUesWxryqkbFun()
    },
    listUesWxryqkbFun() {
      const params = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        fdxm: this.name,
      }
      listUesWxryqkb(params).then((response) => {
        this.dataSources = response.data.list
        this.pageNum = response.data.pages
        this.pageSize = response.data.pageSize
        this.total = response.data.total
      })
    },
    hideModal() {
      this.dataSource = this.selectedRowKeys.map((index) => this.dataSources[index])
      this.visible = false
    },

    lingdangjianshao(){
      let x =this.$root.$children[0].$children[0].$children[0].$children[0].$children[0].$children[0].$children[0].$children[0].$children[0]
      x = x.$children[1].$children[0].$children[0].$children[1].$children[0].$children[0].$children[1]
      console.error(x.DataList)
      x.stepInfoGetStepInfoByIdFun()
    },

    makeDocFormDataMainTable(vform){
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bh=vform.fd_bh
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bt=vform.fd_bt
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].title=vform.fd_bt
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].handle_content=vform.handle_content
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register_time=vform.register_time
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].add_time=null
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register_occur_time=vform.register_occur_time
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].remind_time=null
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register_expect_time=vform.register_expect_time
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].timeout_time=null
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].complain_user=vform.complain_user
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].source=vform.source
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register_user_phone=vform.register_user_phone
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].type=vform.type
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register_scope=vform.register_scope
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register_project=vform.register_project
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].importance=vform.importance
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].development_lead=vform.development_lead
        // this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].event_prior=vform.event_prior
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register_user=vform.register_user
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].egisterUnit=vform.egisterUnit
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].register_dept=vform.register_dept
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].main_id = this.doc.TASK_INFO.id
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].file_ids=vform.file_ids
        
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].add_time  
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].remind_time  
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].timeout_time  
        delete this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].gmt_create  
   
        let xd = new Date()
        this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_archive_datetime = moment().format('YYYY-MM-DD')
    },
    showerro(){
      this.$message.error('保存错误')
    },

    
    handleDel(key) {
      console.error(key)
      let arr = new Array()
      for (var i = 0; i < this.dataSource.length; i++) {
        if (this.dataSource[i].xid != key) {
          arr.push(this.dataSource[i])
        }
      }
      this.dataSource = arr
    },
    async readStep() {
      this.loading = true
      console.log(this.stepInfoId, "this.stepInfoId");
      console.log(this.todoDtoId, "this.todoDtoId");
      this.kkk=false
      this.reopenkkk=false
      // this.stepInfoId=this.$route.params.xpid
      // this.todoDtoId = this.$route.params.xid
      let odata = {
        title:"根据TASK_INFO.id、TASK_INFO.todoDto.id获取表单权限",
        id: this.fid,
        parentDocId: this.stepInfoId,
        isOpened: "1"
      }
     
      await this.getDocStep(odata)
      
       getStepChartGet({
          parentDocId: this.stepInfoId,
          maintable:'itsm_demand_order'
        }).then(async (response) => {
          this.Steps = response
          let xi = -1
          let xii = 0
          for (var i = 0; i < this.Steps.length; i++) {
            if (this.Steps[i].state >= 2) {
              xi = i
              xii ++
            }
          }
          this.current = xii
          this.currentStep=String(this.current+1)
    
          if(xii>=this.Steps.length){
            this.current=xii+1
          }
          if (xi > 0) {
            this.disable = true;
          }
      
          // if(this.currentStep==6){
          //   this.currentStep="7"
          //   this.current=6
          // }
          if(this.refreshint1==false){
            // this.$refs.StepForm.setExamShow(true)
          }
          
          this.refreshint1 = true
          let yys = {
            mainId:this.doc.TASK_INFO.id
          }
          this.refreshint0 = true

          this.dLabel = this.doc.STEP_INFO.stepName
          this.steptemp2 = this.doc.STEP_INFO.stepName 
          if(this.doc.STEP_INFO.stepName=='业务科室领导审核'){
              this.currentStep="2"
              this.current=1
          }else

          if(this.doc.STEP_INFO.stepName=='系统开发商审核'){
              this.currentStep="3"
              this.current=2
          }else

          if(this.doc.STEP_INFO.stepName=='自动化科业务人员审核'){
              this.currentStep="4"
              this.current=3
          }else

          if(this.doc.STEP_INFO.stepName=='自动化科审核'){
              this.currentStep="5"
              this.current=4
          }else

          if(this.doc.STEP_INFO.stepName=='市场部领导审核'){
              this.currentStep="6"
              this.current=5
          }else

          if(this.doc.STEP_INFO.stepName=='科室领导审核'){
              this.currentStep="7"
              this.current=6
          }else

          if(this.doc.STEP_INFO.stepName=='中心领导审核'){
              this.currentStep="8"
              this.current=7
          }else

          if(this.doc.STEP_INFO.stepName=='系统开发商处理'){
              this.currentStep="9"
              this.current=8
          }else

          if(this.doc.STEP_INFO.stepName=='自动化科业务人员确认'){
              this.currentStep="10"
              this.current=9
          }else

          if(this.doc.STEP_INFO.stepName=='需求登记人员确认'){
              this.currentStep="11"
              this.current=10
          }else{
            this.currentStep="1"
              this.current=0
              this.delBtn=true 
          }
          if(this.currentStep=='10'){
            this.nextstr="办结"
          }
          if(this.currentStep=='11'){
            this.nextstr="办结"
          }



          this.nextUserMap=new Map()
          this.nextUserArr=[]
          this.nextUserMap2=new Map()
          this.nextUserArr2=[]
          this.nextUserMap3=new Map()
          this.nextUserArr3=[]
          this.nextUserMap4=new Map()
          this.nextUserArr4=[]


          let yys23 = {
            registerId:this.doc.TASK_INFO.id,
            project:'1'
          }
          await queryDemandCheck(yys23).then(res=>{
            let arr = res.rows
            if(null!=arr&&arr.length>0){
              arr.forEach(item=>{
                if(item.releases=='2'){
                  this.form2 = item
                }
                if(item.releases=='3'){
                  this.form3 = item
                }
                if(item.releases=='4'){
                  this.form4 = item
                }
                if(item.releases=='5'){
                  this.form5 = item
                }
                if(item.releases=='6'){
                  this.form6 = item
                }
                if(item.releases=='7'){
                  this.form7 = item
                }
                if(item.releases=='8'){
                  this.form8 = item
                }
                if(item.releases=='10'){
                  this.form10 = item
                }
                if(item.releases=='11'){
                  this.form11 = item
                }
              })
            }       
              
            
          })

          let yys23ed = {
            registerId:this.doc.TASK_INFO.id,
            project:'2'
          }
          await queryDemandCheck(yys23ed).then(res=>{
            let arr = res.rows
            if(null!=arr&&arr.length>0){
              arr.forEach(item=>{
                if(item.releases=='2'){
                  this.form2arr.push(item)
                }
                if(item.releases=='3'){
                  this.form3arr.push(item)
                }
                if(item.releases=='4'){
                  this.form4arr.push(item)
                }
                if(item.releases=='5'){
                  this.form5arr.push(item)
                }
                if(item.releases=='6'){
                  this.form6arr.push(item)
                }
                if(item.releases=='7'){
                  this.form7arr.push(item)
                }
                if(item.releases=='8'){
                  this.form8arr.push(item)
                }
                if(item.releases=='10'){
                  this.form10arr.push(item)
                }
                if(item.releases=='11'){
                  this.form11arr.push(item)
                }
              })
            }       
              console.error(this.form10arr)
            
          })

          

          let dds = {
            demandId:this.doc.TASK_INFO.id,
            isRelease:'1'
          }


          await queryDemandTrack(dds).then(res2=>{
            let arr2 = res2.rows
            if(null!=arr2&&arr2.length>0){
              arr2.forEach(item=>{
                if(item.developSchedule==null||item.developSchedule==""||item.developSchedule==undefined){
                  this.form9 = item
                }
              })
            }
            
          })

          let ddsed = {
            demandId:this.doc.TASK_INFO.id,
            isRelease:'2'
          }


          await queryDemandTrack(ddsed).then(res2=>{
            this.form9arr = res2.rows
           
            
          })
          
          

          if(this.currentStep=='1'){
            console.error(this.form2arr)
            this.flowshow = true       
            this.reopenkkk = true 
            this.kkk=true
            getRoleUser('eighteen').then(res=>{
              res.eighteen.forEach(item=>{
                let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                if(!this.nextUserMap.has(item.id)){
                   this.nextUserArr.push(x)
                  this.nextUserMap.set(item.id,'0')
                }                
              })              
            })
          }else{
            if(this.currentStep=='2'){
              getRoleUser('fourteen').then(res6=>{
                  res6.fourteen.forEach(item=>{
                    let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                    if(!this.nextUserMap.has(item.id)){
                       this.nextUserArr.push(x)
                      this.nextUserMap.set(item.id,'0')
                    }                                  
                  })              
                })            
            }else if(this.currentStep=='3'){
              this.nextUserMap=new Map()
                this.nextUserArr=[]
                this.nextUserMap2=new Map()
                this.nextUserArr2=[]
              getRoleUser('autoCheck,eleven').then(res5=>{
                
                  res5.autoCheck.forEach(item=>{
                    let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                    if(!this.nextUserMap.has(item.id)){
                      this.nextUserArr.push(x)
                      // this.nextUserArr2.push(x)
                      this.nextUserMap.set(item.id,'0')
                    }                
                  })  
                  res5.eleven.forEach(item=>{
                    let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                    if(!this.nextUserMap2.has(item.id)){
                       this.nextUserArr2.push(x)
                      this.nextUserMap2.set(item.id,'0')
                    }                                  
                  })              
              })            
            }else if(this.currentStep=='4'){
              
            }else if(this.currentStep=='5'){
              getRoleUser('fifteen,eighteen,nineteen,fourteen').then(res4=>{
                  res4.eighteen.forEach(item=>{
                  let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                  if(!this.nextUserMap.has(item.id)){
                     this.nextUserArr.push(x)
                    this.nextUserMap.set(item.id,'0')
                  }                
                })       
                res4.fifteen.forEach(item=>{
                  let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                  if(!this.nextUserMap2.has(item.id)){
                     this.nextUserArr2.push(x)
                    this.nextUserMap2.set(item.id,'0')
                  }                
                })       
                res4.nineteen.forEach(item=>{
                  let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                  if(!this.nextUserMap3.has(item.id)){
                     this.nextUserArr3.push(x)
                    this.nextUserMap3.set(item.id,'0')
                  }                
                })   
                res4.fourteen.forEach(item=>{
                  let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                  if(!this.nextUserMap4.has(item.id)){
                     this.nextUserArr4.push(x)
                    this.nextUserMap4.set(item.id,'0')
                  }                
                }) 
              })            
            }else if(this.currentStep=='6'){
              getRoleUser('fourteen').then(res4=>{
                  res4.fourteen.forEach(item=>{
                  let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                  if(!this.nextUserMap.has(item.id)){
                     this.nextUserArr.push(x)
                    this.nextUserMap.set(item.id,'0')
                  }                
                })              
              })  
              let param ={
                stepId:'s853266885314084864',
                refId: this.doc.TASK_INFO.id
              }
              getLastFlowInfo(param).then(res=>{
                let x = { label: res.data.sendUserCn, value: res.data.sendUser ,userId:res.data.sendUser,userName:res.data.sendUserCn}
                this.nextUserArr=[]
                 this.nextUserArr.push(x)
                        
              })          
            }else if(this.currentStep=='7'){
              getRoleUser('fourteen').then(res4=>{
                  res4.fourteen.forEach(item=>{
                  let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                  if(!this.nextUserMap.has(item.id)){
                     this.nextUserArr.push(x)
                    this.nextUserMap.set(item.id,'0')
                  }                
                })              
              })       
              let param ={
                stepId:'s853266885314084864',
                refId: this.doc.TASK_INFO.id
              }
              getLastFlowInfo(param).then(res=>{
                let x = { label: res.data.sendUserCn, value: res.data.sendUser ,userId:res.data.sendUser,userName:res.data.sendUserCn}
                this.nextUserArr=[]
                 this.nextUserArr.push(x)
                        
              })       
            }else if(this.currentStep=='8'){
              getRoleUser('fourteen').then(res4=>{
                  res4.fourteen.forEach(item=>{
                  let x = { label: item.name, value: item.id ,deptId:item.deptId,userId:item.id,userName:item.name,deptName:item.deptName}
                  if(!this.nextUserMap.has(item.id)){
                     this.nextUserArr.push(x)
                    this.nextUserMap.set(item.id,'0')
                  }                
                })              
              })         
              let param ={
                stepId:'s853266885314084864',
                refId: this.doc.TASK_INFO.id
              }
              getLastFlowInfo(param).then(res=>{
                let x = { label: res.data.sendUserCn, value: res.data.sendUser ,userId:res.data.sendUser,userName:res.data.sendUserCn}
                this.nextUserArr=[]
                 this.nextUserArr.push(x)
                        
              })     
            }else if(this.currentStep=='9'){
              console.error(this.doc)
              let param ={
                stepId:'s881175442516606976',
                refId: this.doc.TASK_INFO.id
              }
              getLastFlowInfo(param).then(res=>{
                let x = { label: res.data.sendUserCn, value: res.data.sendUser ,userId:res.data.sendUser,userName:res.data.sendUserCn}
                this.nextUserArr=[]
                 this.nextUserArr.push(x)
                        
              })
              let param2 ={
                stepId:'s853265198016888832',
                refId: this.doc.TASK_INFO.id
              }
              getLastFlowInfo(param2).then(res=>{
                let x = { label: res.data.sendUserCn, value: res.data.sendUser ,userId:res.data.sendUser,userName:res.data.sendUserCn}
                this.nextUserArr2=[]
                 this.nextUserArr2.push(x)
                        
              })
          
            }

            if(this.currentStep=='2'||this.currentStep=='3'||this.currentStep=='5'||this.currentStep=='4'
                ||this.currentStep=='6'||this.currentStep=='7'||this.currentStep=='8'||this.currentStep=='10'||this.currentStep=='11'
              ){
               
                this.refreshint99=false
                this.flowshow = true       
                this.reopenkkk = true 
                this.kkk=true
                
              }      
              if(this.currentStep=='5'){
                this.nextstr='流转至市场部，科室及中心领导审核'
                this.kfsclBtn = true
                this.nextBtn = false
              }
              if(this.currentStep=='2'){
                this.kfsclBtn = false
                this.nextBtn = true
                this.redoBtn = false
              }
              if(this.currentStep=='3'){
                this.kfsclBtn = false
                this.nextBtn = true
                this.redoBtn = false
              }
              if(this.currentStep=='4'){
                this.kfsclBtn = false
                this.nextBtn = true
                this.redoBtn = false
              }

              if(this.currentStep=='8'){
                this.kfsclBtn = false
                this.nextBtn = true
                this.redoBtn = false
              }

              if(this.currentStep=='6'){
                this.kfsclBtn = false
                this.nextBtn = true
                this.redoBtn = false
              }
              
              if(this.currentStep=='9'){
                this.flowshow = true       
                this.reopenkkk = true 
                this.kkk=true
              }   

              if(this.currentStep=='1'){
                console.error('90000-9768')
                this.flowshow = true       
                this.reopenkkk = true 
                this.kkk=true
              }   
            
            
            
            

            
          }
          

          

          this.getBpmLogFun()
          this.loading = false
        })
      
    },
    async getDocStep(odata){
      await getDocInfo(odata).then((res) => {
        this.taskInfo = res.TASK_INFO
        this.stepId = res.TASK_INFO.stepId
        this.doc = null;
        // this.doc = res
        console.log(res);
        this.taskId=res.TASK_INFO.id
        let Formm = res.FORM_DATA.MAIN_TABLE[Object.keys(res.FORM_DATA.MAIN_TABLE)]
       
        res.FORM_DATA.MAIN_TABLE[Object.keys(res.FORM_DATA.MAIN_TABLE)]=Formm
      

      
      
       // delete this.doc.FORM_DATA.MAIN_TABLE.itsm_event_order.fd_complete_duration
         
        this.stepInfoId = res.TASK_INFO.id
        // this.$refs.StepForm.setForm(this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)])
        // if(this.refreshint1==true){
        //   this.$refs.StepForm.getCurrUserInfoFun()
        // }
        this.doc = res

        this.$multiTab.rename(this.$route.path,'需求-'+this.doc.FORM_DATA.MAIN_TABLE[Object.keys(res.FORM_DATA.MAIN_TABLE)].fd_bh)

      })
    },

    readyStep() {
      new Promise((resolve,reject)=>{
        startflowPost({
          "fileName": "问题管理发起申请",
          "id": "t842446523487748098",
          "fileFormId": "842445647436054530", // 表单id
          "flowId": "f851116608305758208",
          "haveNumber": "0",
          "fileCat": "问题管理发起申请",
          "fileCatId": "c180013"
        }).then( async (res) => {

          this.stepInfoId = res.TASK_INFO.id
          this.todoDtoId = res.TASK_INFO.todoDto.id

           await this.readStep()
           resolve()
        })
      }).then(rees=>{
        this.saveStepForm()
      })
    
      
    }

  },
}
</script>
<style lang="less" scoped="scoped">
//::v-deep .ant-steps-item-wait .ant-steps-item-icon{
//  background: #1677ff;
// .ant-steps-icon{
//   color: #fff;
// }
//}
::v-deep .anticon-loading {
  color: #30a000;
  font-size: 32px;
}

::v-deep .ant-steps-item-custom .ant-steps-item-tail::after {
  background-color: #50c51c;
}

.ant-advanced-search-form {
  // margin-top: 20px;
  padding: 24px;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.ant-coll-all {
  margin: 0% 4%;
  // padding: 0 4%;
  background: #fff;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
}

.ant-collapse {
  // margin-left: 10px;
  margin: 0% 4%;
  // padding: 1% 1%;
  background: #fafafa;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  // border-radius: 6px;
  // margin-left: 10px;
}

.ant-collapse-header {
  background-color: #fafafa;
  margin: 0% 0%;
  height: 50px;
  border-radius: 6px;
}

.sqlc-font {
  font-size: 14px;
  display: flex;
  align-items: center;
}

.ant-coll-font {
  display: flex;
  height: 50px;
  padding: 2%;
  font-size: 15px;
  align-items: center;
}

.ant-collapse-foot {
  padding: 0 4%;
  background: #fff;
  border: 0px solid #d9d9d9;
}

.table-page-search-wrapper {
  // background: #fff;
  // border: 1px solid #d9d9d9;
  margin-top: 20px;
  padding: 3%;
}

.ant-advanced-search-form .ant-form-item {
  display: flex;
}

.ant-advanced-search-form .ant-form-item-control-wrapper {
  flex: 1;
}

#components-form-demo-advanced-search .ant-form {
  max-width: none;
}

#components-form-demo-advanced-search .search-result-list {
  margin-top: 16px;
  border: 1px dashed #e9e9e9;
  border-radius: 6px;
  background-color: #fafafa;
  min-height: 200px;
  text-align: center;
  padding-top: 80px;
}

.arowstyle{
  margin-top: 5px;
  margin-left: 10px;
  margin-bottom: 5px;
  margin-right:10px;
  width: 100%;
}
.flowImg {
    background: url('~@/assets/images/flow/demandFlow.png') no-repeat;
    background-size: contain;
    width: auto;
    height: 441px;
    margin:24px;
}
</style>
