import request from '@/utils/request'

// 查询服务合同表列表
export function listItsmQualityContract (query) {
  return request({
    url: '/system/itsmQualityContract/list',
    method: 'get',
    params: query
  })
}

// 查询服务合同表详细
export function getItsmQualityContract (id) {
  return request({
    url: '/system/itsmQualityContract/' + id,
    method: 'get'
  })
}

// 新增服务合同表
export function addItsmQualityContract (data) {
  return request({
    url: '/system/itsmQualityContract',
    method: 'post',
    data: data
  })
}

// 修改服务合同表
export function updateItsmQualityContract (data) {
  return request({
    url: '/system/itsmQualityContract',
    method: 'put',
    data: data
  })
}

// 删除服务合同表
export function delItsmQualityContract (id) {
  return request({
    url: '/system/itsmQualityContract/' + id,
    method: 'delete'
  })
}


// 导出服务合同表
export function exportItsmQualityContract (query) {
  return request({
    url: '/system/itsmQualityContract/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmQualityContract/getInitData/' + dictTypes,
    method: 'get'
  })
}
