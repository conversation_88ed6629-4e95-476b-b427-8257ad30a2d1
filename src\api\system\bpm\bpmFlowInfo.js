import request from '@/utils/request'

// 查询流程信息表列表
export function listBpmFlowInfo (query) {
  return request({
    url: '/system/bpmFlowInfo/list',
    method: 'get',
    params: query
  })
}

// 查询流程信息表详细
export function getBpmFlowInfo (id) {
  return request({
    url: '/system/bpmFlowInfo/' + id,
    method: 'get'
  })
}

// 新增流程信息表
export function addBpmFlowInfo (data) {
  return request({
    url: '/system/bpmFlowInfo',
    method: 'post',
    data: data
  })
}

// 修改流程信息表
export function updateBpmFlowInfo (data) {
  return request({
    url: '/system/bpmFlowInfo',
    method: 'put',
    data: data
  })
}

// 删除流程信息表
export function delBpmFlowInfo (id) {
  return request({
    url: '/system/bpmFlowInfo/' + id,
    method: 'delete'
  })
}

// 导出流程信息表
export function exportBpmFlowInfo (query) {
  return request({
    url: '/system/bpmFlowInfo/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmFlowInfo/getInitData/' + dictTypes,
    method: 'get'
  })
}
