import request from '@/utils/request'

export function getTempDrafts(params) {
  return request({
    url: 'api/templateDraft',
    method: 'get',
    params
  })
}

export function add(data) {
  return request({
    url: 'api/templateDraft',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/templateDraft/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/templateDraft',
    method: 'put',
    data
  })
}

export default { add, edit, del }
