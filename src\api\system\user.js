import request from '@/utils/request'
import { praseStrEmpty } from '@/utils/aidex'

// 查询用户列表
export function listUser(query) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params: query,
  })
}

// 查询报障用户列表
export function listUser2(query) {
  return request({
    url: '/system/itsmCommonDisableduser/querylist',
    method: 'get',
    params: query,
  })
}

export function listUser3(query) {
  return request({
    url: '/system/itsmCommonDisableduser/querylist2',
    method: 'get',
    params: query,
  })
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: '/system/user/' + praseStrEmpty(userId),
    method: 'get',
  })
}

// // 查询用户详细2
// export function getUserByUserDeptId(userId, deptId) {
//   return request({
//     url: '/system/user/getUserInfoByUserDeptId/' + praseStrEmpty(userId) + '/' + praseStrEmpty(deptId),
//     method: 'get',
//   })
// }
// 查询用户详细2
export function getUserByUserDeptId(userId, deptId) {
  return request({
    url: '/system/user/getUserInfoByUserDeptId?userId=' + praseStrEmpty(userId) + '&deptId=' + praseStrEmpty(deptId) + '&tmp',
    method: 'get',
  })
}

// 新增用户
export function addUser(data) {
  return request({
    url: '/system/user',
    method: 'post',
    data: data,
  })
}

// 修改用户
export function updateUser(data) {
  return request({
    url: '/system/user',
    method: 'put',
    data: data,
  })
}

// 删除用户
export function delUser(userId) {
  return request({
    url: '/system/user/' + userId,
    method: 'delete',
  })
}

// 导出用户
export function exportUser(query) {
  return request({
    url: '/system/user/export',
    method: 'get',
    params: query,
  })
}

// 用户密码重置
export function resetUserPwd(id, password) {
  const data = {
    id,
    password,
  }
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data: data,
  })
}

// 用户状态修改
export function changeUserStatus(id, status) {
  const data = {
    id,
    status,
  }
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data: data,
  })
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: '/system/user/profile',
    method: 'get',
  })
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: '/system/user/profile',
    method: 'put',
    data: data,
  })
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword,
  }
  return request({
    url: '/system/user/profile/updatePwd',
    method: 'put',
    params: data,
  })
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: '/system/user/profile/avatar',
    method: 'post',
    data: data,
  })
}

// 下载用户导入模板
export function importTemplate() {
  return request({
    url: '/system/user/importTemplate',
    method: 'get',
  })
}

// 校验用户名称唯一性
export function checkUserNameUnique(data) {
  return request({
    url: '/system/user/checkUserNameUnique',
    method: 'get',
    params: data,
  })
}
export function checkEmailUnique(data) {
  return request({
    url: '/system/user/checkEmailUnique',
    method: 'get',
    params: data,
  })
}
export function checkPhoneUnique(data) {
  return request({
    url: '/system/user/checkPhoneUnique',
    method: 'get',
    params: data,
  })
}
// 查询用户详细
export function getUserInfoByIds(userIds) {
  return request({
    url: '/system/user/getUserInfoByIds',
    method: 'post',
    data: userIds,
  })
}
// 查询角色下用户列表
export function getRoleUserList(query) {
  return request({
    url: '/system/user/roleUserList',
    method: 'get',
    params: query,
  })
}
// 插入角色用户
export function saveRoleUser(data) {
  return request({
    url: '/system/user/addRoleUser',
    method: 'post',
    data: data,
  })
}


export function searchlist(name) {
  return request({
    url: '/system/user/searchlist?name='+name,
    method: 'get'
  })
}

export function getStatisticsScope() {
  return request({
    url: '/system/ItsmStatistics/getStatisticsScope',
    method: 'get'
  })
}

export function statistics4ITService(data) {
  return request({
    url: '/system/ItsmStatistics/statistics4ITService',
    method: 'get',
    params: data,
  })
}

export function export4ITService(data) {
  return request({
    url: '/system/ItsmStatistics/export4ITService',
    method: 'get',
    params: data,
  })
}