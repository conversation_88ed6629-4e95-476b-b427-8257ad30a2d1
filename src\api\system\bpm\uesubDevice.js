import request from '@/utils/request'

// 查询设备记录表列表
export function listUesubDevice (query) {
  return request({
    url: '/system/uesubDevice/list',
    method: 'get',
    params: query
  })
}

// 查询设备记录表详细
export function getUesubDevice (id) {
  return request({
    url: '/system/uesubDevice/' + id,
    method: 'get'
  })
}

// 新增设备记录表
export function addUesubDevice (data) {
  return request({
    url: '/system/uesubDevice',
    method: 'post',
    data: data
  })
}

// 修改设备记录表
export function updateUesubDevice (data) {
  return request({
    url: '/system/uesubDevice',
    method: 'put',
    data: data
  })
}

// 删除设备记录表
export function delUesubDevice (id) {
  return request({
    url: '/system/uesubDevice/' + id,
    method: 'delete'
  })
}

// 导出设备记录表
export function exportUesubDevice (query) {
  return request({
    url: '/system/uesubDevice/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesubDevice/getInitData/' + dictTypes,
    method: 'get'
  })
}
