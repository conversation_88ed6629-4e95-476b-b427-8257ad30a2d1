import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/bpmTemplatePdf',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/bpmTemplatePdf/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/bpmTemplatePdf',
    method: 'put',
    data
  })
}

export function getById(id) {
  return request({
    url: 'api/queryById?id=' + id,
    method: 'get'
  })
}

export default { add, edit, del }
