import request from '@/utils/request'

// 查询设备资源分配明细表列表
export function listUesubSbzyfpmx (query) {
  return request({
    url: '/system/bpm/uesubSbzyfpmx/list',
    method: 'get',
    params: query
  })
}

// 查询设备资源分配明细表详细
export function getUesubSbzyfpmx (id) {
  return request({
    url: '/system/bpm/uesubSbzyfpmx/' + id,
    method: 'get'
  })
}

// 新增设备资源分配明细表
export function addUesubSbzyfpmx (data) {
  return request({
    url: '/system/bpm/uesubSbzyfpmx',
    method: 'post',
    data: data
  })
}

// 修改设备资源分配明细表
export function updateUesubSbzyfpmx (data) {
  return request({
    url: '/system/bpm/uesubSbzyfpmx',
    method: 'put',
    data: data
  })
}

// 删除设备资源分配明细表
export function delUesubSbzyfpmx (id) {
  return request({
    url: '/system/bpm/uesubSbzyfpmx/' + id,
    method: 'delete'
  })
}

// 导出设备资源分配明细表
export function exportUesubSbzyfpmx (query) {
  return request({
    url: '/system/bpm/uesubSbzyfpmx/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpm/uesubSbzyfpmx/getInitData/' + dictTypes,
    method: 'get'
  })
}
