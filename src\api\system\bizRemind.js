import request from '@/utils/request'

// 查询超时事项表列表
export function listBizRemind (query) {
  return request({
    url: '/system/bizremind/list',
    method: 'get',
    params: query
  })
}

// 查询超时事项表详细
export function getBizRemind (id) {
  return request({
    url: '/system/bizremind/' + id,
    method: 'get'
  })
}

// 新增超时事项表
export function addBizRemind (data) {
  return request({
    url: '/system/bizremind',
    method: 'post',
    data: data
  })
}

// 修改超时事项表
export function updateBizRemind (data) {
  return request({
    url: '/system/bizremind',
    method: 'put',
    data: data
  })
}

// 删除超时事项表
export function delBizRemind (id) {
  return request({
    url: '/system/bizremind/' + id,
    method: 'delete'
  })
}


// 导出超时事项表
export function exportBizRemind (query) {
  return request({
    url: '/system/bizremind/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bizremind/getInitData/' + dictTypes,
    method: 'get'
  })
}
