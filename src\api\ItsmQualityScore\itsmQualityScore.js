import request from '@/utils/request'

// 查询服务质量考核/评分细则列表
export function listItsmQualityScore (query) {
  return request({
    url: '/system/itsmqualityscore/list',
    method: 'get',
    params: query
  })
}

// 查询服务质量考核/评分细则详细
export function getItsmQualityScore (id) {
  return request({
    url: '/system/itsmqualityscore/' + id,
    method: 'get'
  })
}

// 新增服务质量考核/评分细则
export function addItsmQualityScore (data) {
  return request({
    url: '/system/itsmqualityscore',
    method: 'post',
    data: data
  })
}

// 修改服务质量考核/评分细则
export function updateItsmQualityScore (data) {
  return request({
    url: '/system/itsmqualityscore',
    method: 'put',
    data: data
  })
}

// 删除服务质量考核/评分细则
export function delItsmQualityScore (id) {
  return request({
    url: '/system/itsmqualityscore/' + id,
    method: 'delete'
  })
}


// 导出服务质量考核/评分细则
export function exportItsmQualityScore (query) {
  return request({
    url: '/system/itsmqualityscore/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmqualityscore/getInitData/' + dictTypes,
    method: 'get'
  })
}
