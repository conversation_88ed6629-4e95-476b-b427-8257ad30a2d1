import request from '@/utils/request'

// 查询知识操作记录表列表
export function listItsmKnowledgeUpdate (query) {
  return request({
    url: '/system/itsmKnowledgeUpdate/list',
    method: 'get',
    params: query
  })
}

// 查询知识操作记录表详细
export function getItsmKnowledgeUpdate (mainId) {
  return request({
    url: '/system/itsmKnowledgeUpdate/' + mainId,
    method: 'get'
  })
}

// 新增知识操作记录表
export function addItsmKnowledgeUpdate (data) {
  return request({
    url: '/system/itsmKnowledgeUpdate',
    method: 'post',
    data: data
  })
}

// 修改知识操作记录表
export function updateItsmKnowledgeUpdate (data) {
  return request({
    url: '/system/itsmKnowledgeUpdate',
    method: 'put',
    data: data
  })
}

// 删除知识操作记录表
export function delItsmKnowledgeUpdate (mainId) {
  return request({
    url: '/system/itsmKnowledgeUpdate/' + mainId,
    method: 'delete'
  })
}


// 导出知识操作记录表
export function exportItsmKnowledgeUpdate (query) {
  return request({
    url: '/system/itsmKnowledgeUpdate/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmKnowledgeUpdate/getInitData/' + dictTypes,
    method: 'get'
  })
}
