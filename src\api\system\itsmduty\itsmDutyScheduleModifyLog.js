import request from '@/utils/request'

// 查询排班设置-操作日志列表
export function listItsmDutyScheduleModifyLog (query) {
  return request({
    url: '/system/itsmDutyScheduleModifyLog/list',
    method: 'get',
    params: query
  })
}

// 查询排班设置-操作日志详细
export function getItsmDutyScheduleModifyLog (id) {
  return request({
    url: '/system/itsmDutyScheduleModifyLog/' + id,
    method: 'get'
  })
}

// 新增排班设置-操作日志
export function addItsmDutyScheduleModifyLog (data) {
  return request({
    url: '/system/itsmDutyScheduleModifyLog',
    method: 'post',
    data: data
  })
}

// 修改排班设置-操作日志
export function updateItsmDutyScheduleModifyLog (data) {
  return request({
    url: '/system/itsmDutyScheduleModifyLog',
    method: 'put',
    data: data
  })
}

// 删除排班设置-操作日志
export function delItsmDutyScheduleModifyLog (id) {
  return request({
    url: '/system/itsmDutyScheduleModifyLog/' + id,
    method: 'delete'
  })
}


// 导出排班设置-操作日志
export function exportItsmDutyScheduleModifyLog (query) {
  return request({
    url: '/system/itsmDutyScheduleModifyLog/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmDutyScheduleModifyLog/getInitData/' + dictTypes,
    method: 'get'
  })
}
