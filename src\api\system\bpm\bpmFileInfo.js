import request from '@/utils/request'

// 查询流程文件表列表
export function listBpmFileInfo (query) {
  return request({
    url: '/system/bpmFileInfo/list',
    method: 'get',
    params: query
  })
}

// 查询流程文件表详细
export function getBpmFileInfo (id) {
  return request({
    url: '/system/bpmFileInfo/' + id,
    method: 'get'
  })
}

// 新增流程文件表
export function addBpmFileInfo (data) {
  return request({
    url: '/system/bpmFileInfo',
    method: 'post',
    data: data
  })
}

// 修改流程文件表
export function updateBpmFileInfo (data) {
  return request({
    url: '/system/bpmFileInfo',
    method: 'put',
    data: data
  })
}

// 删除流程文件表
export function delBpmFileInfo (id) {
  return request({
    url: '/system/bpmFileInfo/' + id,
    method: 'delete'
  })
}

// 导出流程文件表
export function exportBpmFileInfo (query) {
  return request({
    url: '/system/bpmFileInfo/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmFileInfo/getInitData/' + dictTypes,
    method: 'get'
  })
}
