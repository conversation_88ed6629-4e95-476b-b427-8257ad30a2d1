import request from '@/utils/request'

// 查询设备资源申请子表列表
export function listUesubSbzysq (query) {
  return request({
    url: '/system/uesubSbzysq/list',
    method: 'get',
    params: query
  })
}

// 查询设备资源申请子表详细
export function getUesubSbzysq (id) {
  return request({
    url: '/system/uesubSbzysq/' + id,
    method: 'get'
  })
}

// 新增设备资源申请子表
export function addUesubSbzysq (data) {
  return request({
    url: '/system/uesubSbzysq',
    method: 'post',
    data: data
  })
}

// 修改设备资源申请子表
export function updateUesubSbzysq (data) {
  return request({
    url: '/system/uesubSbzysq',
    method: 'put',
    data: data
  })
}

// 删除设备资源申请子表
export function delUesubSbzysq (id) {
  return request({
    url: '/system/uesubSbzysq/' + id,
    method: 'delete'
  })
}


// 导出设备资源申请子表
export function exportUesubSbzysq (query) {
  return request({
    url: '/system/uesubSbzysq/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/uesubSbzysq/getInitData/' + dictTypes,
    method: 'get'
  })
}
