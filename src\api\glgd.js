export default {
    data () {
        return {
            eventgdgl:false,
            troublegdgl:false,
            problemgdgl:false,
            demandgdgl:false,
        }
    },
    methods: {
        eventgdglSet(){
        this.eventgdgl=true
        this.$nextTick(() => (
            this.$refs.eventglgd.handleUpdate()
        ))
        },
        troublegdglSet(){
        this.troublegdgl=true
        this.$nextTick(() => (
            this.$refs.troublegdgl.handleUpdate()
        ))
        },
        problemgdglSet(){
        this.problemgdgl=true
        this.$nextTick(() => (
            this.$refs.problemgdgl.handleUpdate()
        ))
        },
        demandgdglSet(){
        this.demandgdgl=true
        this.$nextTick(() => (
            this.$refs.demandgdgl.handleUpdate()
        ))
        },
    }
}
