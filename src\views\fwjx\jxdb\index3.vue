<template>
  <div>
    <a-card :bordered="false" style="margin-bottom: 10px;">
      <!-- 条件搜索 -->
      <div class="table-page-search-wrapper">
        <a-form :labelCol="labelCol" :wrapperCol="wrapperCol" ref="queryForm">
          <a-row :gutter="32">
            <a-col :span="8" >
              <a-form-item label=" 编号">
                <a-input v-model="queryParam.assessNo" placeholder="请输入" allow-clear @keyup.enter.native="handleQuery"/>
              </a-form-item>
            </a-col>
            <a-col :span="8" >
              <a-form-item label="当前处理环节">
                <a-select   allow-clear v-model="queryParam.currentTaskName" >
                  <a-select-option value="服务绩效考核登记" >服务绩效考核登记</a-select-option>
                  <a-select-option value="业务负责人审核" >业务负责人审核</a-select-option>
                  <a-select-option value="自动化科审核" >自动化科审核</a-select-option>
                  <a-select-option value="服务商确认" >服务商确认</a-select-option>
                  <a-select-option value="自动化科申诉复核" >自动化科申诉复核</a-select-option>
                  <a-select-option value="服务绩效考核确认" >服务绩效考核确认</a-select-option>
                  <a-select-option value="已关闭" >已关闭</a-select-option>
                </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" >
              <a-form-item label="服务商">
                <a-select v-model="queryParam.providerId" allowClear option-label-prop="label">
                <a-select-option v-for="(dict, index) in providerArr" :key="index" :value="dict.id" :label="dict.providerName"><span>{{ dict.providerName }}</span><br/><span style="font-size:12px;">{{dict.providerNo}}&nbsp;&nbsp;&nbsp;{{dict.director}}</span></a-select-option>
              </a-select>
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="advanced">
              <a-form-item label="考核类型">
                <a-select  placeholder="请选择考核类型" allow-clear v-model="queryParam.assessType">
                  <a-select-option v-for="(dict, index) in khlx" :key="index" :value="dict.dictValue" >{{ dict.dictLabel }}</a-select-option>
                </a-select> 
              </a-form-item>
            </a-col>
            <a-col :span="8" v-if="advanced">
            <a-form-item label="考核对象">
              <a-select  placeholder="请选择考核对象" allow-clear v-model="queryParam.assessObject">
              <a-select-option v-for="(dict, index) in khdx" :key="index" :value="dict.dictValue" >{{ dict.dictLabel }}</a-select-option>
            </a-select> 
            </a-form-item>
          </a-col>
          <a-col :span="8" v-if="advanced">
            <a-form-item label="发生日期">
              <a-date-picker v-model="queryParam.stTime" valueFormat="YYYY-MM-DD" style="width:47%;" placeholder="开始日期"/> ~ <a-date-picker v-model="queryParam.edTime" valueFormat="YYYY-MM-DD" style="width:47%;" placeholder="结束日期"/> 
            </a-form-item>
          </a-col>
            <a-col>
              <span class="table-page-search-submitButtons" style="float: right;">
                <a-button type="primary" @click="handleQuery"><a-icon type="search" />查询</a-button>
                <a-button style="margin-left: 8px" @click="resetQuery"><a-icon type="redo" />重置</a-button>
                <a @click="toggleAdvanced" style="margin-left: 8px">
                  {{ advanced ? '收起' : '展开' }}
                  <a-icon :type="advanced ? 'up' : 'down'"/>
                </a>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>
    </a-card>
    <a-card :bordered="false" class="table-card">
      <!-- 增加 -->
      <itsm-demand-order-add-form
        v-if="showAddModal"
        ref="itsmDemandOrderAddForm"
        :checkThreeStatusOptions="checkThreeStatusOptions"
        :checkFourStatusOptions="checkFourStatusOptions"
        :checkFiveStatusOptions="checkFiveStatusOptions"
        :checkSixStatusOptions="checkSixStatusOptions"
        :checkSevenStatusOptions="checkSevenStatusOptions"
        @ok="getList"
        @close="showAddModal = false"
      />
      <!-- 编辑 -->
      <itsm-demand-order-edit-form
        v-if="showEditModal"
        ref="itsmDemandOrderEditForm"
        :checkThreeStatusOptions="checkThreeStatusOptions"
        :checkFourStatusOptions="checkFourStatusOptions"
        :checkFiveStatusOptions="checkFiveStatusOptions"
        :checkSixStatusOptions="checkSixStatusOptions"
        :checkSevenStatusOptions="checkSevenStatusOptions"
        @ok="getList"
        @close="showEditModal = false"
      />
      <advance-table
        title="服务绩效考核查询"
        :pagination="{
          current: queryParam.pageNum,
          pageSize: queryParam.pageSize,
          total: total,
          showSizeChanger: true,
          showLessItems: true,
          showQuickJumper: true,
          showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，总计 ${total} 条`,
          onChange: changeSize,
          onShowSizeChange: onShowSizeChange
        }"
        tableKey="base-itsmDemandOrder-index-table"
        @change="handleTableChange"
        rowKey="id"
        size="middle"
        @refresh="getList"
        :columns="columns"
        :data-source="itsmDemandOrderList"
        :loading="loading"
        :format-conditions="true"
        :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      >
        <div class="table-operations" slot="button">
  
          <a-button type="" @click="handleExport"  >
            <a-icon type="download" />导出
          </a-button>
        </div>
        <span slot="checkThreeStatus" slot-scope="{record}">
          {{ checkThreeStatusFormat(record) }}
        </span>
        <span slot="checkFourStatus" slot-scope="{record}">
          {{ checkFourStatusFormat(record) }}
        </span>
        <span slot="checkFiveStatus" slot-scope="{record}">
          {{ checkFiveStatusFormat(record) }}
        </span>
        <span slot="checkSixStatus" slot-scope="{record}">
          {{ checkSixStatusFormat(record) }}
        </span>
        <span slot="checkSevenStatus" slot-scope="{record}">
          {{ checkSevenStatusFormat(record) }}
        </span>
        <span slot="bpm" slot-scope="{record,text}">
          <a @click="handlerBpm(record)" >
            {{text}}
          </a>
        </span>
        <span slot="khlxtra" slot-scope="{record,text}">
         {{ khlxfy(record) }}
        </span>
        <span slot="trankhdxt" slot-scope="{text, record}">
        {{ trankhdx(record) }}
      </span>
        <span slot="operation" slot-scope="{text, record}">
          
          <a-divider type="vertical" />
          <a @click="handleDelete(record)" v-hasPermi="['system:itsmDatalink:remove']">
            删除
          </a>
        </span>
      </advance-table>
    </a-card>
  </div>
</template>
<script>
import { list } from '@/api/system/itsmQualityAssess'
import { listItsmEventOrder, delItsmEventOrder, exportItsmEventOrder } from '@/api/system/itsmEventOrder'
import { busDict,delJX,exportItsmdatalink } from '@/api/ItsmQualityProvider/itsmQualityContract.js'
import { getInitData } from '@/api/system/bpm/uesDevice'
import AdvanceTable from '@/components/pt/table/AdvanceTable'
import ItsmDemandOrderAddForm from '@/views/xqgl/xqdb/itsmdemandorder/modules/ItsmDemandOrderAddForm'
import ItsmDemandOrderEditForm from '@/views/xqgl/xqdb/itsmdemandorder/modules/ItsmDemandOrderEditForm'
export default {
  name: 'ItsmDemandOrder',
  components: {
    AdvanceTable,
    ItsmDemandOrderAddForm,
    ItsmDemandOrderEditForm

  },
  data () {
    return {
      showAddModal: false,
      showEditModal: false,
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 选中的主键集合
      selectedRowKeys: [],
      // 选中的数据集合
      selectedRows: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // label的百分比
      labelCol: { span: 6 },
      // 内容区域的百分比
      wrapperCol: { span: 18 },
      //  登记流程表格数据
      itsmDemandOrderList: [],
      // 自动化科批准状态字典
      checkThreeStatusOptions: [],
      // 自动化科批准状态字典
      checkFourStatusOptions: [],
      // 自动化科批准状态字典
      checkFiveStatusOptions: [],
      // 自动化科批准状态字典
      checkSixStatusOptions: [],
      // 自动化科批准状态字典
      checkSevenStatusOptions: [],
      // 查询参数
      queryParam: {
        pageNum: 1,
        pageSize: 10,
        assessNo:null,
        providerId:null,
        currentTaskName:null,
        assessType:null,
        assessObject:null,
        edTime:null,
        stTime:null,


      },
      columns: [
        {
          title: '服务绩效编号',
          dataIndex: 'assessNo',
          ellipsis: true,
          align: 'center',
          width: '180px',
          scopedSlots: { customRender: 'bpm' },
        },
        {
          title: '当前处理环节',
          dataIndex: 'currentTaskName',
          align: 'center',
          width: '180px'
        },
        {
          title: '当前处理人',
          dataIndex: 'taskUser',
          align: 'center',
          width: '120px'
        },
        {
          title: '服务商',
          dataIndex: 'providerName',
          align: 'center',
          width: '100px'
        },
        {
          title: '责任人',
          dataIndex: 'directorName',
          align: 'center',
          width: '100px'
        },
        {
          title: '考核内容说明',
          dataIndex: 'assessContent',
          align: 'center',
          width: '180px'
        },
         {
          title: '考核得分',
          dataIndex: 'assessValue',
          align: 'center',
          width: '100px'
        },
        {
          title: '考核类型',
          dataIndex: 'assessType',
          align: 'center',
          width: '150px',
           scopedSlots: { customRender: 'khlxtra' },
        },
        {
          title: '考核对象',
          dataIndex: 'assessObject',
          align: 'center',
          width: '120px',
          scopedSlots: { customRender: 'trankhdxt' },
        },
        {
          title: '一级指标',
          dataIndex: 'target1Name',
          align: 'center',
          width: '120px'
        },
        {
          title: '二级指标名称',
          dataIndex: 'target2Name',
          align: 'center',
          width: '120px'
        },
        {
          title: '详细条款',
          dataIndex: 'clauseName',
          align: 'center',
          width: '120px'
        },  
       
        {
          title: '发生时间',
          dataIndex: 'assessTime',
          align: 'center',
          width: '100px'
        },
        {
          title: '操作',
          dataIndex: 'id',
          align: 'center',
          width: '120px',
          scopedSlots: { customRender: 'operation' },
        }
      ],
      jxfws:[],
      requestUserList:[],
      khlx:[],
      khdx:[],
      fwjxejzb:[],
      ejzbsel:[],
      fwjxyjzb:[],
      fwjxkhzt:[],
      providerArr:[],
    }
  },
  activated(){
    this.getList()
  },
  created () {
    this.getList()
    busDict().then(resd=>{
      this.providerArr = resd
    })
    getInitData('jxfws,khlx,khdx,fwjxejzb,fwjxyjzb,fwjxkhzt').then(res=>{
      this.jxfws = res.data.jxfws
      this.khlx = res.data.khlx
      this.khdx = res.data.khdx
      this.fwjxejzb = res.data.fwjxejzb
      this.ejzbsel = this.fwjxejzb
      this.fwjxyjzb = res.data.fwjxyjzb
      this.fwjxkhzt = res.data.fwjxkhzt
    })
  },
  methods: {
    trankhdx(row){
      let tr=""
      this.khdx.forEach(item=>{
        if(item.dictValue==row.assessObject){
          tr=item.dictLabel
        }
      })
      return tr
    },
    tranKHLIST(row){
      let tr=""
      this.khlx.forEach(item=>{
        if(item.dictValue==row.assessType){
          tr=item.dictLabel
        }
      })
      return tr
    },
    khlxfy(record){
      for(var i=0;i<this.khlx.length;i++){
        if(this.khlx[i].dictValue == record.assessType){
          return this.khlx[i].dictLabel
        }
      }
    },
    /** 查询 登记流程列表 */
    getList () {
      this.loading = true
      list(this.queryParam).then(response => {
        this.itsmDemandOrderList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 自动化科批准状态字典翻译
    checkThreeStatusFormat (row) {
      if (row.checkThreeStatus) {
        return this.selectDictLabel(this.checkThreeStatusOptions, row.checkThreeStatus)
      } else {
        return ''
      }
    },
    // 自动化科批准状态字典翻译
    checkFourStatusFormat (row) {
      if (row.checkFourStatus) {
        return this.selectDictLabel(this.checkFourStatusOptions, row.checkFourStatus)
      } else {
        return ''
      }
    },
    // 自动化科批准状态字典翻译
    checkFiveStatusFormat (row) {
      if (row.checkFiveStatus) {
        return this.selectDictLabel(this.checkFiveStatusOptions, row.checkFiveStatus)
      } else {
        return ''
      }
    },
    // 自动化科批准状态字典翻译
    checkSixStatusFormat (row) {
      if (row.checkSixStatus) {
        return this.selectDictLabel(this.checkSixStatusOptions, row.checkSixStatus)
      } else {
        return ''
      }
    },
    // 自动化科批准状态字典翻译
    checkSevenStatusFormat (row) {
      if (row.checkSevenStatus) {
        return this.selectDictLabel(this.checkSevenStatusOptions, row.checkSevenStatus)
      } else {
        return ''
      }
    },
    /** 搜索按钮操作 */
    handleQuery () {
      this.queryParam.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery () {
      this.queryParam = {
        pageNum: 1,
        pageSize: 10,
        title: undefined,

        curstepname: undefined,

        fdBt: undefined,

        fdBh: undefined,
        demandType:undefined,
        registerUser:undefined,
        gmtCreate:undefined,

        proposer :undefined

      }
      this.handleQuery()
    },
    /** 翻页操作 */
    onShowSizeChange (current, pageSize) {
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    /** 翻页操作 */
    onSizeChange (current, size) {
      this.queryParam.pageNum = 1
      this.queryParam.pageSize = size
      this.getList()
    },
    /** 翻页操作 */
    changeSize (current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      this.getList()
    },
    /** 翻页操作 */
    onSelectChange (selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
      this.ids = this.selectedRows.map(item => item.id)
      this.single = selectedRowKeys.length !== 1
      this.multiple = !selectedRowKeys.length
    },
    /** 查询折叠和展开操作 */
    toggleAdvanced () {
      this.advanced = !this.advanced
    },
    handleAdd () {
      this.showAddModal = true
      this.$nextTick(() => (
        this.$refs.itsmDemandOrderAddForm.handleAdd()
      ))
    },
    handleUpdate (record, ids) {
      this.showEditModal = true
      this.$nextTick(() => (
        this.$refs.itsmDemandOrderEditForm.handleUpdate(record, ids)
      ))
    },
    /** 删除按钮操作 */
    handleDelete (row) {
      var that = this
      const assessNo = row.assessNo 
      this.$confirm({
        title: '确认删除所选中数据?',
        onOk () {
          return delJX(assessNo)
            .then(() => {
              that.onSelectChange([], [])
              that.getList()
              that.$message.success(
                '删除成功',
                3
              )
          })
        },
        onCancel () {}
      })
    },
    /** 导出按钮操作 */
    handleExport () {
      var that = this
      this.$confirm({
        title: '是否确认导出?',
        content: '此操作将导出当前条件下所有数据而非选中数据',
        onOk () {
          return exportItsmdatalink(that.queryParam)
            .then(response => {
            //  console.error(response.msg)
             that.download(response.msg)
              that.$message.success(
                '导出成功',
                3
              )
          })
        },
        onCancel () {}
      })
    },
    handlerBpm(record){
    //   console.error(record)

    // this.$router.push({ name: 'Xqdj',params:{xid:record.id,xpid:record.refId}})
    this.loading=true    
      this.$multiTab.openTempRoute2("fwjx/jxdj/index6",'fwjxkhdjsq6',record,'Fwjxkh')
  
    },
    handleTableChange (pagination, filters, sorter) {
      if (sorter.field !== undefined && sorter.field !== null && sorter.field !== '') {
        this.queryParam.orderByColumn = 'a.' + sorter.field
        this.queryParam.isAsc = sorter.order
      }
      this.getList()
    }
  }
}
</script>
