import request from '@/utils/request'

// 查询工作计划审核表列表
export function listItsmOperationCheck (query) {
  return request({
    url: '/system/itsmOperationCheck/list',
    method: 'get',
    params: query
  })
}

// 查询工作计划审核表详细
export function getItsmOperationCheck (checkUser) {
  return request({
    url: '/system/itsmOperationCheck/' + checkUser,
    method: 'get'
  })
}

// 新增工作计划审核表
export function addItsmOperationCheck (data) {
  return request({
    url: '/system/itsmOperationCheck',
    method: 'post',
    data: data
  })
}

// 修改工作计划审核表
export function updateItsmOperationCheck (data) {
  return request({
    url: '/system/itsmOperationCheck',
    method: 'put',
    data: data
  })
}

// 删除工作计划审核表
export function delItsmOperationCheck (checkUser) {
  return request({
    url: '/system/itsmOperationCheck/' + checkUser,
    method: 'delete'
  })
}


// 导出工作计划审核表
export function exportItsmOperationCheck (query) {
  return request({
    url: '/system/itsmOperationCheck/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmOperationCheck/getInitData/' + dictTypes,
    method: 'get'
  })
}
