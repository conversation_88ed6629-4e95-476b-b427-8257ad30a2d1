import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/todo',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/todo/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/todo',
    method: 'put',
    data
  })
}

export function getById(id) {
  return request({
    url: 'api/queryById?id=' + id,
    method: 'get'
  })
}

export function getList(pers) {
  return request({
    url: 'api/todo',
    method: 'get',
    params: pers
  })
}

export function getMyAllTodo(data) {
  return request({
    url: 'api/todo/getMyAllTodo',
    method: 'put',
    data
  })
}

export default { add, edit, del, getList }
