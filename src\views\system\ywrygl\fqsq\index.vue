<template>
    <div>
  
      <a-tabs v-model:activeKey="mykey" style="margin-top:-20px;">
 
        <a-tab-pane key="1" tab="基础信息">

        <a-spin :spinning="!refreshint0" tip="加载中。。。"  size="large" style="width:100%;">
        <StepsShow ref="StepShow"  :stepArr="Steps" :number="current" :stepInfoIds="stepInfoId" v-if="refreshint0" />
        <FormShow ref="StepForm"  :doc="doc" v-if="refreshint0" bussType="ywrygl" :disabled="disable" /> 
              <!-- 编辑 -->
          <UesWxryqkbEditForm
            v-if="showEditModal"
            ref="uesWxryqkbEditForm"
            @ok="getList"
            @close="showEditModal = false"
          />
        <a-card :bordered="false" style="margin-bottom: 10px;padding:24px;" v-if="refreshint0">
          <div style="display: flex;margin-top:10px;margin-bottom: 10px;" v-if="!disable">
          <a-button type="primary"  style="margin-left: 0px;" @click="showModal" v-if="!disable"> 获取人员</a-button>
          <a-button type="primary"  style="margin-left: 10px;" @click="handleAdd" v-if="!disable"> 新增</a-button>
          </div>
          <a-table bordered :data-source="dataSource" :columns="columns" :pagination="false">
             <!-- 驻场类型 -->
            <template slot="fdtype" slot-scope="text,record">
              <a-select v-model="record.fdtype" style="width: 100%" @change="handleInputChange(record)" :disabled="disable">
                <a-select-option v-for="option in sfdrTypeArr" :key="option.value" :value="option.value" >
                  {{option.label}}
                </a-select-option>
              </a-select>
            </template>

            <template slot="fdbgqk" slot-scope="text,record">
              <!-- 这里是变更情况的自定义渲染逻辑 -->
              <a-select v-model="record.fdbgqk" style="width: 100%" @change="handleInputChange(record)" :disabled="disable">
                <a-select-option v-for="option in changeTypeOptions" :key="option.value" :value="option.value" >
                  {{ option.label }}
                </a-select-option>
              </a-select>
            </template>
            <template slot="fdjhbgsj" slot-scope="text,record">
              <a-date-picker
               :disabled="disable"
                valueFormat="YYYY-MM-DD"
                v-model="record.fdjhbgsj"
                format="YYYY-MM-DD"
                placeholder="选择日期"
              />
            </template>

            <template slot="operation" slot-scope="text,record">
              <a-button type="danger" :disabled="disable" @click="handleDelete(record) ">删除</a-button>
            </template>
          </a-table>
          <a-modal v-model="visible" ok-text="确认" cancel-text="取消" @ok="hideModal" width="50%">
            <div class="search-container" style="display: flex; align-items: center; padding: 20px">
              <div style="display: flex; align-items: center; margin-right: 10px">
                <div style="margin-right: 10px">姓名</div>
                <a-input v-model="name" style="width: 200px; padding: 0"> </a-input>
              </div>
              <a-button type="primary" @click="searchPerson">搜索</a-button>
            </div>
            <a-table :rowSelection="rowSelection" :columns="columnss" :dataSource="dataSources" bordered 
            :pagination="{
              current: queryParam.pageNum,
              pageSize: queryParam.pageSize,
              pageSizeOptions:[10,20,,30,50,100],
              total: total,
              showSizeChanger: true,
              showLessItems: true,
              showQuickJumper: true,
              showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，总计 ${total} 条`,
              onChange: changeSize,
              onShowSizeChange: onShowSizeChange,
            }"
            > 
              <span slot="fdsfzh" slot-scope="text, record">
                {{handleIdCard(text)}}
              </span>
            </a-table>
          </a-modal>
        </a-card>
      <!-- <Options :parentDocId="stepInfoId" v-if="refreshint0"/> -->
        <SubmitShow ref="SubmitShow" @saveStepForm="saveStepForm"  :disabled="disable" 
        :stepId="stepId" :todoDtoId="todoDtoId" v-if="refreshint0"  @starttask="readyStep"
        :stepInfoIds="stepInfoId" :taskInfo="taskInfo" :doc="doc" bussType="ywrygl" />
      
      </a-spin>
        </a-tab-pane>
       
       
      </a-tabs>
    </div>
  </template>
  <script>
  import StepsShow from "@/components/bpm/StepsShow.vue";
  import FormShow from "@/components/bpm/FormShow.vue";
  import SubmitShow from "@/components/bpm/SubmitShow3.vue";
  import Options from "@/components/bpm/Options.vue";
  import AdvanceTable from '@/components/pt/table/AdvanceTable';
  import UesWxryqkbEditForm from '@/views/system/ywrygl/fqsq/modules/UesWxryqkbEditForm';
  import {
    startflowPost,
    saveformdataPost,
    uploadPost,
    getDocInfo,
    operationGotoNextStep,
    getStepChartGet,
    targetUser,
    operationGetnowscriptpath,
    stepInfoGetStepInfoById,  
    getbpmlog,
  } from '@/api/system/ywrygl/operation'
  import notification from 'ant-design-vue/es/notification'
  import { listUesDevice,listUesDeviceSelect, delUesDevice, exportUesDevice, getInitData, importTemplate } from '@/api/system/bpm/uesDevice'
  import { listUesWxryqkb } from '@/api/system/ywrygl/uesWxryqkb'
  import moment from 'moment';
  import { maskIdCard } from '@/utils/util'
  const columns = [
    {
      title: '序号',
      dataIndex: 'fdnum',
      key:'fdnum',
     
    },
    {
      title: '姓名',
      dataIndex: 'fdxm',
      key:'fdxm',
      
    },
    {
      title: '工牌编号',
      dataIndex: 'workId',
      key:'workId',
      
    },
    {
      title: '身份证号',
      dataIndex: 'fdsfzh',
      key:'fdsfzh',
   
    },
    {
      title: '驻场类型',
      dataIndex: 'fdtype',
      key:'fdtype',
      scopedSlots: { customRender: 'fdtype' },
    },
    {
          title: '变更情况',
          dataIndex: 'fdbgqk',
          key: 'fdbgqk',
          scopedSlots: { customRender: 'fdbgqk' },
          width: '160px',
        },
        {
          title: '计划变更时间',
          dataIndex: 'fdjhbgsj',
          key: 'fdjhbgsj',
          scopedSlots: { customRender: 'fdjhbgsj' },
          width: '160px',
        },
        {
          title: '操作',
          dataIndex: 'operation',
          scopedSlots: {
            customRender: 'operation',
          },
          width: '130px',
        },
  ]
  const data = []
  export default {
    name:'ywgl',
    components: {
      UesWxryqkbEditForm,
      StepsShow,
      FormShow,
      SubmitShow,
      Options,
      AdvanceTable,
      
    },
    data() {
      return {
        showEditModal:false,  // 新增表单
        saved:false,
        flowLogs:[],
        mykey:'1',
        changeTypeOptions: [
        // 变更情况选项
        { value: '入场', label: '入场' },
        { value: '出场', label: '出场' },
      ],
        sfdrTypeArr: [{value:'常驻', label: '常驻'},{value:'短期', label: '短期'}],   // 驻场类型
        dataSources: [],
        columnss: [
        {
          title: '序号',
          dataIndex: 'fdnum',
          key: 'fdnum',
        },
        {
          title: '姓名',
          dataIndex: 'fdxm',
          key: 'fdxm',
          width: '100px',
        },
        {
          title: '工牌编号',
          dataIndex: 'workId',
          key: 'workId',
          width: '100px',
        },
        {
          title: '身份证号',
          dataIndex: 'fdsfzh',
          key: 'fdsfzh',
          scopedSlots: { customRender: 'fdsfzh' },
        },
        {
          title: '单位',
          dataIndex: 'fdwxdw',
          key: 'fdwxdw',
        },
        {
          title: '归属项目',
          dataIndex: 'fdgsxm',
          key: 'fdgsxm',
        },
      ],
        refreshint0: true,
        current: 0,
        data,
        columns,
        form: this.$form.createForm(this),
        text: '运维公司变更，请审批！',
        activeKey: ['1'],
        customStyle:
          'background: #fafafa;border-radius: 6px;border: 1;margin-bottom: 0px;padding-right:10px;overflow: hidden;',
        expandIconPosition: 'right',
        disable: false,
        stepInfoId: '',
        stepId:'',
        Steps:[],
        todoDtoId:'',
        taskInfo:{},
        doc:{},
        subIndex:0,
        visible: false,
        deviceName:'',
        queryParam: {
          pageNum: 1,
          pageSize: 10,
          fid: undefined,
  
          fdnum: undefined,
  
          fdbh: undefined,
  
          fdsbfl: undefined,
  
          fdsbmc: undefined,
  
          fdyxzt: undefined,
  
          fdssks: undefined,
  
          fdsbzr: undefined,
  
          fdszjf: undefined,
  
          fdszqy: undefined,
  
          fdszdw: undefined,
  
          fdxxwzms: undefined,
  
          fdjlzcbh: undefined,
  
          fdssywxt: undefined,
  
          fdssaqfq: undefined,
  
          fdjjgtjb: undefined,
  
          fdnum: undefined,
  
          fdsbcs: undefined,
  
          fdgys: undefined,
  
          fdhtmc: undefined,
  
          fdglks: undefined,
  
          fdccxlh: undefined,
  
          fdedgl: undefined,
  
          fdsbcc: undefined,
  
          fdzjm: undefined,
  
          fdczxt: undefined,
  
          fdywip: undefined,
  
          fdglip: undefined,
  
          fdsbpzsm: undefined,
  
          fdsbyt: undefined,
  
          fdbz: undefined,
  
          beginFdsbrcrq: undefined,
  
          beginFdsbtyrq: undefined,
  
          beginFdsbtcrq: undefined,
  
          beginFdsbzbqx: undefined,
  
          beginFdsbwbqx: undefined,
  
          fdsfsj: undefined,
  
          fdsfbf: undefined,
  
          fdsfbj: undefined,
  
          fdsfywsb: undefined,
  
          fdsbdsdhaqjgqk: undefined,
  
          fdwzbgjl: undefined,
  
          fdbjhcsl: undefined,
  
          fdcgje: undefined,
  
          fdext1: undefined,
  
          fdext2: undefined,
  
          fdext3: undefined,
  
          fdext4: undefined,
  
          fdext5: undefined,
  
          refId: undefined,
  
          fdFtype: undefined,
  
          fdinit: undefined,
  
          createUserid: undefined,
  
          updateUserid: undefined
  
        },
        dataSource: [],
        selectedRowKeys: [],
        selectedRows: [],
        selectedRowKeys2: [],
        selectedRows2: [],
        name:null,
        total:0,
      }
    },
    watch: {
      activeKey(key) {
        console.log(key)
      },
    },
    computed: {
      rowSelection() {
        return {
          selectedRowKeys: this.selectedRowKeys,
          onChange: this.onSelectChange,
        }
      },
      rowSelection2() {
        return {
          selectedRowKeys: this.selectedRowKeys2,
          onChange: this.onSelectChange2,
        }
      },
    },
    activated(){
    
      if(sessionStorage.getItem(this.$route.path)==2){
         this.refreshint0 = false
        this.$refs.StepForm.emptyYWYR()
        this.$refs.StepShow.emptySteps()
        this.$refs.SubmitShow.emptyPaths()
        this.Steps = [] 
        this.taskInfo = {} 
        this.saved=false
        this.stepInfoId = null
        this.dataSource=[]
        this.flowLogs=new Array()
        this.disable=false
        if(null!=this.$route.params.xid&&""!=this.$route.params.xid
        &&null!=this.$route.params.xpid&&""!=this.$route.params.xpid){
          this.stepInfoId=this.$route.params.xpid
          this.todoDtoId = this.$route.params.xid
          this.readStep()
        }else{
          // this.readyStep()
           this.refreshint0 = true
        }
        sessionStorage.setItem(this.$route.path,1)
      }
    },
    created(){
      this.dataSource=[]
      // if(null!=this.$route.params.xid&&""!=this.$route.params.xid
      // &&null!=this.$route.params.xpid&&""!=this.$route.params.xpid){
      //   this.stepInfoId=this.$route.params.xpid
      //   this.todoDtoId = this.$route.params.xid
      //   this.readStep()
      // }else{
      //   this.readyStep()
      // }
      
    },
    methods: {
      handleIdCard (id) {  // 处理身份证号年月日为星号替代
        return maskIdCard(id)
      },
      handleAdd() {
        this.showEditModal = true
        this.$nextTick(() => this.$refs.uesWxryqkbEditForm.handleAdd())
      },
      getList(data) {
        if(!this.dataSource.filter(ele=>ele.fdsfzh===data.fdsfzh).length)   this.dataSource.push(data)
      },
      onShowSizeChange(current, pageSize) {
      this.queryParam.pageSize = pageSize
      this.listUesWxryqkbFun()
    },

    /** 翻页操作 */
    changeSize(current, pageSize) {
      this.queryParam.pageNum = current
      this.queryParam.pageSize = pageSize
      this.listUesWxryqkbFun()
    },
      getBpmLogFun(){
        getbpmlog(this.taskInfo.todoDto.parentDocId).then((res)=>{
          const logs = eval(
              '([' + res.data.flowLogs.replace(/[\r\n]/g, '<BR>') + '])'
            )
          for (let index = 0; index < logs.length; index++) {
            const element = logs[index]
            element.index = index + 1
          }
          this.flowLogs=logs
          console.error(logs)
        })
      },
      delSel(){
        var arr = new Array()
        
          for(var j=0;j<this.dataSource.length;j++){
            if(this.selectedRowKeys2.indexOf(j)==-1){
              arr.push(this.dataSource[j])
            }
          }
         this.dataSource=arr
      },
      handleInputChange(row) {
      // 当下拉框的值改变时，这个方法会被调用
      // row 参数是当前行的数据
      console.log('变更情况已更改:', row.fdbgqk)
      // 在这里，您可以根据需要更新其他属性或发送请求到后端
    },
    handleDelete(record) {
      console.log(record, 'record')

      if (record) {
        const index = this.dataSource.findIndex((item) => item.id == record.id)
        if (index !== -1) {
          this.dataSource.splice(index, 1)
        } else {
          console.error('未找到要删除的数据项')
        }
      } else {
        console.error('尝试删除的行数据未定义')
      }
    },
      onSelectChange(selectedRowKeys) {
        this.selectedRowKeys = selectedRowKeys
        console.log('selectedRowKeys changed: ', selectedRowKeys)
      },
      onSelectChange2(selectedRowKeys) {
        this.selectedRowKeys2 = selectedRowKeys
        console.log('selectedRowKeys changed: ', selectedRowKeys)
      },
      searchDevice() {
        this.listUseDeviceFun()
      },
      listUseDeviceFun() {
        const params = {
          pageNum: this.pageNum,
          pageSize: this.pageSize,
        }
        listUesDeviceSelect(params).then((response) => {
         console.error(response)
        })
      },
    showModal() {
      this.name = null 
      this.visible = true
      this.listUesWxryqkbFun()
    },
    searchPerson() {
      this.listUesWxryqkbFun()
    },
    listUesWxryqkbFun() {
      const params = {
        pageNum: this.queryParam.pageNum,
        pageSize: this.queryParam.pageSize,
        fdxm: this.name,
      }
      listUesWxryqkb(params).then((response) => {
        console.log(response, 'xxxxxx')
        this.dataSources = response.data.list
        this.pageNum = response.data.pages
        this.pageSize = response.data.pageSize
        this.total = response.data.total
      })
    },
      hideModal() {
        if(null==this.dataSource||this.dataSource.length==0){
          this.dataSource = this.selectedRowKeys.map((index) => this.dataSources[index])
          this.selectedRowKeys = []
        }else{
          this.selectedRowKeys.map((index) => {
            this.dataSource.push(this.dataSources[index])            
          })
          this.selectedRowKeys = []
        }
        this.visible = false
      },
      saveStepForm(callback){
        let vform = this.$refs.StepForm.getFrom()   
        console.error(vform)
        console.error(this.doc)
        if(undefined!=vform && null!=vform &&""!=vform){
          let param = {
          MAIN_TABLE:{

          },
          SUB_TABLE:{
            uesub_ywrybgsqd: [
                 
              ]},
          PENDING_TABLE:{}
          }
          let parr = new Array()
          for(var i=0;i<this.dataSource.length;i++){
            // let p ={
            //   id:this.dataSource[i].id,
            //   fdnum:this.dataSource[i].fdnum.fdnum,
            //   fdnum:this.dataSource[i].fdnum.fdnum,
            //   fdsfzh:this.dataSource[i].fdsfzh.fdsfzh,
            // //   fdsl:this.dataSource[i].fdsl.fdsl,
            // //   fdyt:this.dataSource[i].fdyt.fdyt,
            // //   fdglks:this.dataSource[i].fdglks.fdglks,
            // //   fddw:this.dataSource[i].fddw.fddw,
            // //   fdsffp:0,
            // //   fdState:1,
            // }
            // parr.push(p)
            param.SUB_TABLE.uesub_ywrybgsqd.push(this.dataSource[i])
          }
        //   param.SUB_TABLE.uesub_ywrybgsqd=parr
        console.error(vform)
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_bt=vform.fd_bt
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_create_date_time=vform.fd_create_date_time
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_create_dept_name=vform.fd_create_dept_name
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_create_unit_name=vform.fd_create_unit_name
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_create_user_name=vform.fd_create_user_name
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_sfdr=vform.fd_sfdr
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_create_user_phone=vform.fd_create_user_phone
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_htmc=vform.fd_htmc
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_whksrq=vform.fd_whksrq
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_whjsrq=vform.fd_whjsrq
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_xmmc=vform.fd_xmmc
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_start_datetime=null
          

          let xd = new Date()
          this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)].fd_archive_datetime=moment().format('YYYY-MM-DD')       
          param.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)]=this.doc.FORM_DATA.MAIN_TABLE[Object.keys(this.doc.FORM_DATA.MAIN_TABLE)]
          saveformdataPost(param).then((res)=>{
            this.$notification.success({
              message: '保存成功',
              description: ``
            })
            // this.$router.push({name:'Ywrygldbcx'})
            callback(true)
          })
        }else{
        this.$notification.error({
            message: '保存失败',
            description: ``
          })
          callback(false)
      }
        
      },
      handleDel(key) {
        console.error(key)
        let arr = new Array()
        for(var i=0;i<this.dataSource.length;i++){
          if(this.dataSource[i].xid!=key){
            arr.push(this.dataSource[i])
          }
        }
        this.dataSource=arr
      },
      async readStep(){
          let that =this
          let odata = {
            title:"根据TASK_INFO.id、TASK_INFO.todoDto.id获取表单权限",
            id:null, 
            parentDocId:this.stepInfoId , 
            // id:null, 
            // parentDocId:'836317322489626624' ,
            isOpened:"1"
          }
          await getDocInfo(odata).then(async (res)=>{
            that.taskInfo = res.TASK_INFO
            console.log('getDocInfo 返回内容:', res);
            that.stepId = res.TASK_INFO.stepId
            that.doc=null;
            let vform = that.$refs.StepForm.getFrom()   
            that.refreshint0=false
            that.doc = res
            console.error(vform)
            that.$multiTab.rename(that.$route.path,'运维人员申请--'+that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_bh)
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_bt=vform.fd_bt
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_create_date_time=vform.fd_create_date_time
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_create_dept_name=vform.fd_create_dept_name
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_create_unit_name=vform.fd_create_unit_name
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_create_user_name=vform.fd_create_user_name
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_sfdr=vform.fd_sfdr
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_create_user_phone=vform.fd_create_user_phone
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_htmc=vform.fd_htmc
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_whksrq=vform.fd_whksrq
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_whjsrq=vform.fd_whjsrq
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_xmmc=vform.fd_xmmc
            that.doc.FORM_DATA.MAIN_TABLE[Object.keys(that.doc.FORM_DATA.MAIN_TABLE)].fd_start_datetime=null
          
            that.stepInfoId = that.doc.TASK_INFO.id
            console.error('------------------------')
            console.error(this.dataSource)
            console.error('------------------------')
            if(null==this.dataSource||this.dataSource.length==0){
              this.dataSource = this.doc.FORM_DATA.SUB_TABLE.uesub_ywrybgsqd
            }

            // let arr2 = new Array()
            // let arr = this.doc.FORM_DATA.SUB_TABLE.uesub_ywrybgsqd
            // if(null!=arr){
            //     for(var i=0;i<arr.length;i++){
            //     this.subIndex = this.subIndex+1
            //     let p={
            //         xid:this.subIndex,
            //         id:arr[i].id,
            //         fdnum:{fdnum:arr[i].fdnum},
            //         fdnum:{fdnum:arr[i].fdnum},
            //         fdsfzh:{fdsfzh:arr[i].fdsfzh},
            //         // fdsl:{fdsl:arr[i].fdsl},
            //         // fdyt:{fdyt:arr[i].fdyt},
            //         // fdglks:{fdglks:arr[i].fdglks},
            //         // fddw:{fddw:arr[i].fddw} ,
            //     }
            //     arr2.push(p)
            //     }
            // }
            // this.dataSource=arr2
            await getStepChartGet({
              parentDocId: this.stepInfoId,
            }).then((response) => {    
              this.Steps=response
              console.log('getStepChartGet 返回内容:', response); // 打印全部内容
              let xi=-1
              let xif=0
              for(var i=0;i<this.Steps.length;i++){
                if(this.Steps[i].state>=1){
                  xi=i
                xif++
                }
              }
              this.current = xi
              if(xif>=this.Steps.length){
              this.current=this.Steps.length+1
              }
              console.error('当前步骤:', this.current)
              if(xi>0){
                this.disable=true;
              }
              this.refreshint0 = true
            })
            this.saveStepForm()
          })
      },
      async readyStep(){
        if(this.saved==false){
          await startflowPost({
          "fileName":"运维人员变更申请",
            "id":"t804691974417285120",
            "fileFormId":"814458170318520320",
            "flowId":"f804689059417948160",
            "haveNumber":"0",
            "fileCat": "运维人员变更申请",
            "fileCatId": "c180010",
          }).then(async (res)=>{        
          
            this.stepInfoId = res.TASK_INFO.id        
            this.todoDtoId = res.TASK_INFO.todoDto.id
            await this.readStep()
            this.saved = true
         })
        }else{
          await this.readStep()
        }
        

      }
      
    },
  }
  </script>
  <style lang="less" scoped="scoped">
  ::v-deep  .ant-table-wrapper {
    margin:0px;
  }
  ::v-deep .ant-form-item-label {
      width: 120px;
  }
  ::v-deep .anticon-loading {
    color: #30a000;
    font-size: 32px;
  }

  
  ::v-deep .ant-steps-item-custom .ant-steps-item-tail::after {
    background-color: #50c51c;
  }
  .ant-advanced-search-form {
    // margin-top: 20px;
    padding: 24px;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
  }
  .ant-coll-all {
    margin: 0% 4%;
    // padding: 0 4%;
    background: #fff;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
  }
  .ant-collapse {
    // margin-left: 10px;
    margin: 0% 4%;
    // padding: 1% 1%;
    background: #fafafa;
    border: 1px solid #d9d9d9;
    border-radius: 6px;
    // border-radius: 6px;
    // margin-left: 10px;
  }
  .ant-collapse-header {
    background-color: #fafafa;
    margin: 0% 0%;
    height: 50px;
    border-radius: 6px;
  }
  .sqlc-font {
    font-size: 14px;
    display: flex;
    align-items: center;
  }
  .ant-coll-font {
    display: flex;
    height: 50px;
    padding: 2%;
    font-size: 15px;
    align-items: center;
  }
  .ant-collapse-foot {
    padding: 0 4%;
    background: #fff;
    border: 0px solid #d9d9d9;
  }
  .table-page-search-wrapper {
    // background: #fff;
    // border: 1px solid #d9d9d9;
    margin-top: 20px;
    padding: 3%;
  }
  
  .ant-advanced-search-form .ant-form-item {
    display: flex;
  }
  
  .ant-advanced-search-form .ant-form-item-control-wrapper {
    flex: 1;
  }
  
  #components-form-demo-advanced-search .ant-form {
    max-width: none;
  }
  #components-form-demo-advanced-search .search-result-list {
    margin-top: 16px;
    border: 1px dashed #e9e9e9;
    border-radius: 6px;
    background-color: #fafafa;
    min-height: 200px;
    text-align: center;
    padding-top: 80px;
  }
  </style>
  