import request from '@/utils/request'

// 查询计分卡列表
export function listItsmQualityScorecard (query) {
  return request({
    url: '/system/itsmqualityscorecard/list',
    method: 'get',
    params: query
  })
}

// 查询计分卡详细
export function getItsmQualityScorecard (id) {
  return request({
    url: '/system/itsmqualityscorecard/' + id,
    method: 'get'
  })
}

// 新增计分卡
export function addItsmQualityScorecard (data) {
  return request({
    url: '/system/itsmqualityscorecard',
    method: 'post',
    data: data
  })
}

// 修改计分卡
export function updateItsmQualityScorecard (data) {
  return request({
    url: '/system/itsmqualityscorecard',
    method: 'put',
    data: data
  })
}

// 删除计分卡
export function delItsmQualityScorecard (id) {
  return request({
    url: '/system/itsmqualityscorecard/' + id,
    method: 'delete'
  })
}


// 导出计分卡
export function exportItsmQualityScorecard (query) {
  return request({
    url: '/system/itsmqualityscorecard/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/itsmqualityscorecard/getInitData/' + dictTypes,
    method: 'get'
  })
}
