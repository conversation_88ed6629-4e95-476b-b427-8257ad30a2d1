import request from '@/utils/request'

// 查询工作督办列表
export function listItsmWorkMain (query) {
  return request({
    url: '/supervise/itsmWorkMain/list',
    method: 'get',
    params: query
  })
}

// 查询工作督办详细
export function getItsmWorkMain (addTime) {
  return request({
    url: '/supervise/itsmWorkMain/' + addTime,
    method: 'get'
  })
}

// 新增工作督办
export function addItsmWorkMain (data) {
  return request({
    url: '/supervise/itsmWorkMain',
    method: 'post',
    data: data
  })
}

// 修改工作督办
export function updateItsmWorkMain (data) {
  return request({
    url: '/supervise/itsmWorkMain',
    method: 'put',
    data: data
  })
}

// 删除工作督办
export function delItsmWorkMain (addTime) {
  return request({
    url: '/supervise/itsmWorkMain/' + addTime,
    method: 'delete'
  })
}


// 导出工作督办
export function exportItsmWorkMain (query) {
  return request({
    url: '/supervise/itsmWorkMain/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/supervise/itsmWorkMain/getInitData/' + dictTypes,
    method: 'get'
  })
}
