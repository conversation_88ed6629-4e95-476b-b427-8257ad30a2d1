import request from '@/utils/request'

// 查询周月报主体表列表
// export function listItsmReportMain (query) {
//   return request({
//     url: '/ItsmReportMain/itsmReportMain/list',
//     method: 'get',
//     params: query
//   })
// }
// 查询周月报所有列表  （新接口）
export function listItsmreportmain(query) {
  return request({
    url: '/system/itsmreportmain/list',
    method: 'get',
    params: query
  })
}

// 查询我的周月报  （新接口）
export function listItsmreportmainMyList(query) {
  return request({
    url: '/system/itsmreportmain/mylist',
    method: 'get',
    params: query
  })
}

// // 查询周月报主体表详细
// export function getItsmReportMain (name) {
//   return request({
//     url: '/ItsmReportMain/itsmReportMain/' + name,
//     method: 'get'
//   })
// }
// 查询周月报主体表详细
export function getItsmReportMain (name) {
  return request({
    url: '/system/itsmreportmain/' + name,
    method: 'get'
  })
}



// // 新增周月报主体表
// export function addItsmReportMain (data) {
//   return request({
//     url: '/ItsmReportMain/itsmReportMain',
//     method: 'post',
//     data: data
//   })
// }

// 新增周月报主体表  （新接口）
export function addItsmReportMain (data) {
  return request({
    url: '/system/itsmreportmain',
    method: 'post',
    data: data
  })
}

// // 修改周月报主体表
// export function updateItsmReportMain (data) {
//   return request({
//     url: '/ItsmReportMain/itsmReportMain',
//     method: 'put',
//     data: data
//   })
// }
// 修改周月报主体表 (新接口)
export function updateItsmReportMain (data) {
  return request({
    url: '/ItsmReportMain/itsmReportMain',
    method: 'put',
    data: data
  })
}
// 删除周月报主体表
export function delItsmReportMain (name) {
  return request({
    url: '/ItsmReportMain/itsmReportMain/' + name,
    method: 'delete'
  })
}

// 删除周月报主体表
export function handleDelItsmReportMain (query) {
  return request({
    url: '/system/itsmreportmain/delById',
    method: 'get',
    params: query
  })
}

// 导出周月报主体表
export function exportItsmReportMain (query) {
  return request({
    url: '/ItsmReportMain/itsmReportMain/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/ItsmReportMain/itsmReportMain/getInitData/' + dictTypes,
    method: 'get'
  })
}


// 删除周月报主体表
export function deleteFile (id) {
  return request({
    url: '/ItsmReportMain/itsmReportMain/delfile?id=' + id,
    method: 'get'
  })
}


// 删除周月报主体表
export function deleteList (id) {
  return request({
    url: '/ItsmReportMain/itsmReportMain/delList?id=' + id,
    method: 'get'
  })
}


export function dlJFZReport (id) {
  return request({
    url: '/system/itsmreportmain/dlJFZReport?id=' + id,
    method: 'get'
  })
}

export function dlRJZReport (id) {
  return request({
    url: '/system/itsmreportmain/dlRJZReport?id=' + id,
    method: 'get'
  })
}

export function dFWZLReport (id) {
  return request({
    url: '/system/itsmreportmain/dFWZLReport?id=' + id,
    method: 'get'
  })
}
// 获取数据调用接口

// 软件运维服务指标完成情况
// export function getSoftDevOpsSlaConditions (query) {
//   return request({
//     url: '/system/itsmEvent/statis/getTypeTwoLevelConditions',
//     method: 'post',
//     data: query
//   })
// }
// 本周运维情况综述
export function getDevOpsTypeStatis (query) {
  return request({
    url: '/system/itsmEvent/statis/getDevOpsTypeStatis',
    method: 'post',
    data: query
  })
}

//   软件运维-获取数据  三、运维服务指标完成情况 （一）运维分类统计情况；   
//   指标分析：1.一线问题解决情况汇总统计
export function getTypeTwoLevelConditions (query) {
  return request({
    url: '/system/itsmEvent/statis/getTypeTwoLevelConditions',
    method: 'post',
    data: query
  })
}

// 三、运维服务指标完成情况 （二）软件运维服务指标完成情况
export function getSoftDevOpsSlaConditions (query) {
  return request({
    url: '/system/itsmEvent/statis/getSoftDevOpsSlaConditions',
    method: 'post',
    data: query
  })
}
// 三、运维服务指标完成情况  (三) 工单TopN事件分析
export function getTypeTopEventAnalysis (query) {
  return request({
    url: '/system/itsmEvent/statis/getTypeTopEventAnalysis',
    method: 'post',
    data: query
  })
}
// 三、本期:作业计划list 上期:作业计划list
export function getOperationQueryList (query) {
  return request({
    url: '/system/itsmOperationOrder/statis/getOperationQueryList',
    method: 'post',
    data: query
  })
}
 // 本月故障事件 搜索内容
export function getContent4WeekReport (query) {
  return request({
    url: '/system/itsmTrouble/getContent4WeekReport',
    method: 'get',
    data: query
  })
}
/* 周报月报 常量配置 2个接口
     * 机房维护周报_运维材料提交情况 
     * 机房维护月报_概述*/
export function getMachineDevOpsMaterialList (query) {
  return request({
    url: '/system/itsmOperationOrder/statis/getMachineDevOpsMaterialList',
    method: 'post',
    data: query
  })
}