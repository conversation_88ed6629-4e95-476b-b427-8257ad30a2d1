import request from '@/utils/request'

// 查询网络ip地址申请单列表
export function listUeWlipsqd (query) {
  return request({
    url: '/system/ueWlipsqd/pendinglist',
    method: 'get',
    params: query
  })
}


export function querylistUeWlipsqd (query) {
  return request({
    url: '/system/ueWlipsqd/list',
    method: 'get',
    params: query
  })
}

// 查询网络ip地址申请单详细
export function getUeWlipsqd (id) {
  return request({
    url: '/system/ueWlipsqd/' + id,
    method: 'get'
  })
}

// 新增网络ip地址申请单
export function addUeWlipsqd (data) {
  return request({
    url: '/system/ueWlipsqd',
    method: 'post',
    data: data
  })
}

// 修改网络ip地址申请单
export function updateUeWlipsqd (data) {
  return request({
    url: '/system/ueWlipsqd',
    method: 'put',
    data: data
  })
}

// 删除网络ip地址申请单
export function delUeWlipsqd (id) {
  return request({
    url: '/system/ueWlipsqd/' + id,
    method: 'delete'
  })
}

// 导出网络ip地址申请单
export function exportUeWlipsqd (query) {
  return request({
    url: '/system/ueWlipsqd/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/ueWlipsqd/getInitData/' + dictTypes,
    method: 'get'
  })
}
