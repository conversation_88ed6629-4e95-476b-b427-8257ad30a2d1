import request from '@/utils/request'

// 查询流程实例表列表
export function listBpmRuExecution (query) {
  return request({
    url: '/system/bpmRuExecution/list',
    method: 'get',
    params: query
  })
}

// 查询流程实例表详细
export function getBpmRuExecution (id) {
  return request({
    url: '/system/bpmRuExecution/' + id,
    method: 'get'
  })
}

// 新增流程实例表
export function addBpmRuExecution (data) {
  return request({
    url: '/system/bpmRuExecution',
    method: 'post',
    data: data
  })
}

// 修改流程实例表
export function updateBpmRuExecution (data) {
  return request({
    url: '/system/bpmRuExecution',
    method: 'put',
    data: data
  })
}

// 删除流程实例表
export function delBpmRuExecution (id) {
  return request({
    url: '/system/bpmRuExecution/' + id,
    method: 'delete'
  })
}

// 导出流程实例表
export function exportBpmRuExecution (query) {
  return request({
    url: '/system/bpmRuExecution/export',
    method: 'get',
    params: query
  })
}

// 获取初始化数据
export function getInitData (dictTypes) {
  return request({
    url: '/system/bpmRuExecution/getInitData/' + dictTypes,
    method: 'get'
  })
}
